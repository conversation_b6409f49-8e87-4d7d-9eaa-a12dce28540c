import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentModel from "../../model/Component.js";
var PolarModel = (
  /** @class */
  function(_super) {
    __extends(PolarModel2, _super);
    function PolarModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = PolarModel2.type;
      return _this;
    }
    PolarModel2.prototype.findAxisModel = function(axisType) {
      var foundAxisModel;
      var ecModel = this.ecModel;
      ecModel.eachComponent(axisType, function(axisModel) {
        if (axisModel.getCoordSysModel() === this) {
          foundAxisModel = axisModel;
        }
      }, this);
      return foundAxisModel;
    };
    PolarModel2.type = "polar";
    PolarModel2.dependencies = ["radiusAxis", "angleAxis"];
    PolarModel2.defaultOption = {
      // zlevel: 0,
      z: 0,
      center: ["50%", "50%"],
      radius: "80%"
    };
    return PolarModel2;
  }(ComponentModel)
);
export {
  PolarModel as default
};

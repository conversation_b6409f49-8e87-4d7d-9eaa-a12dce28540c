import _sfc_main from "./inputNumber.vue2.js";
import { resolveComponent, createElementBlock, openBlock, createVNode, withCtx, createElementVNode, normalizeClass } from "vue";
/* empty css                 */
import _export_sfc from "../_virtual/_plugin-vue_export-helper.js";
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_Hlxb_icon = resolveComponent("Hlxb-icon");
  const _component_HlxbInput = resolveComponent("HlxbInput");
  return openBlock(), createElementBlock("div", null, [
    createVNode(_component_HlxbInput, {
      modelValue: _ctx.inputValue,
      "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => _ctx.inputValue = $event),
      center: "",
      onChange: _ctx.handleChange
    }, {
      prepend: withCtx(() => [
        createElementVNode("div", {
          class: normalizeClass(["cursor-pointer", [{ "is-disabled": _ctx.decreaseDisabled }, _ctx.styleClass]]),
          onClick: _cache[0] || (_cache[0] = ($event) => _ctx.downCount("decrease"))
        }, [
          createVNode(_component_Hlxb_icon, { name: "jian" })
        ], 2)
      ]),
      append: withCtx(() => [
        createElementVNode("div", {
          class: normalizeClass(["cursor-pointer", [{ "is-disabled": _ctx.increaseDisabled }, _ctx.styleClass]]),
          onClick: _cache[1] || (_cache[1] = ($event) => _ctx.addCount("increase"))
        }, [
          createVNode(_component_Hlxb_icon, { name: "jia" })
        ], 2)
      ]),
      _: 1
    }, 8, ["modelValue", "onChange"])
  ]);
}
const inputNumber = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render]]);
export {
  inputNumber as default
};

import { use } from "../../extension.js";
import radarLayout from "./radarLayout.js";
import dataFilter from "../../processor/dataFilter.js";
import radarBackwardCompat from "./backwardCompat.js";
import RadarView from "./RadarView.js";
import RadarSeriesModel from "./RadarSeries.js";
import { install as install$1 } from "../../component/radar/install.js";
function install(registers) {
  use(install$1);
  registers.registerChartView(RadarView);
  registers.registerSeriesModel(RadarSeriesModel);
  registers.registerLayout(radarLayout);
  registers.registerProcessor(dataFilter("radar"));
  registers.registerPreprocessor(radarBackwardCompat);
}
export {
  install
};

import { withInstall } from "../../../utils/index.js";
import _sfc_main from "./Ranking.vue.js";
/* empty css             */
import _sfc_main$1 from "./RankingSimple.vue.js";
/* empty css                   */
import _sfc_main$2 from "./EnergySummary.vue.js";
/* empty css                   */
import _sfc_main$3 from "./DrugSummary.vue.js";
/* empty css                 */
import _sfc_main$4 from "./HorizontaSmallSquare.vue.js";
/* empty css                          */
import _sfc_main$5 from "./Loading.vue.js";
/* empty css             */
import _sfc_main$6 from "./Empty.vue.js";
/* empty css           */
const HlxbRanking = withInstall(_sfc_main);
const HlxbRankingSimple = withInstall(_sfc_main$1);
const HlxbEnergySummary = withInstall(_sfc_main$2);
const HlxbDrugSummary = withInstall(_sfc_main$3);
const HlxbHorizontaSmallSquare = withInstall(_sfc_main$4);
const HlxbCardLoading = withInstall(_sfc_main$5);
const HlxbCardEmpty = withInstall(_sfc_main$6);
export {
  HlxbCardEmpty,
  HlxbCardLoading,
  HlxbDrugSummary,
  HlxbEnergySummary,
  HlxbHorizontaSmallSquare,
  HlxbRanking,
  HlxbRankingSimple
};

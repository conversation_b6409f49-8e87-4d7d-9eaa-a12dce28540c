import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each, extend, mixin } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import env from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/env.js";
import { DataFormatMixin } from "../../model/mixin/dataFormat.js";
import ComponentModel from "../../model/Component.js";
import { makeInner, defaultEmphasis } from "../../util/model.js";
import { createTooltipMarkup } from "../tooltip/tooltipMarkup.js";
function fillLabel(opt) {
  defaultEmphasis(opt, "label", ["show"]);
}
var inner = makeInner();
var MarkerModel = (
  /** @class */
  function(_super) {
    __extends(MarkerModel2, _super);
    function MarkerModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkerModel2.type;
      _this.createdBySelf = false;
      _this.preventAutoZ = true;
      return _this;
    }
    MarkerModel2.prototype.init = function(option, parentModel, ecModel) {
      if (process.env.NODE_ENV !== "production") {
        if (this.type === "marker") {
          throw new Error("Marker component is abstract component. Use markLine, markPoint, markArea instead.");
        }
      }
      this.mergeDefaultAndTheme(option, ecModel);
      this._mergeOption(option, ecModel, false, true);
    };
    MarkerModel2.prototype.isAnimationEnabled = function() {
      if (env.node) {
        return false;
      }
      var hostSeries = this.__hostSeries;
      return this.getShallow("animation") && hostSeries && hostSeries.isAnimationEnabled();
    };
    MarkerModel2.prototype.mergeOption = function(newOpt, ecModel) {
      this._mergeOption(newOpt, ecModel, false, false);
    };
    MarkerModel2.prototype._mergeOption = function(newOpt, ecModel, createdBySelf, isInit) {
      var componentType = this.mainType;
      if (!createdBySelf) {
        ecModel.eachSeries(function(seriesModel) {
          var markerOpt = seriesModel.get(this.mainType, true);
          var markerModel = inner(seriesModel)[componentType];
          if (!markerOpt || !markerOpt.data) {
            inner(seriesModel)[componentType] = null;
            return;
          }
          if (!markerModel) {
            if (isInit) {
              fillLabel(markerOpt);
            }
            each(markerOpt.data, function(item) {
              if (item instanceof Array) {
                fillLabel(item[0]);
                fillLabel(item[1]);
              } else {
                fillLabel(item);
              }
            });
            markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);
            extend(markerModel, {
              mainType: this.mainType,
              // Use the same series index and name
              seriesIndex: seriesModel.seriesIndex,
              name: seriesModel.name,
              createdBySelf: true
            });
            markerModel.__hostSeries = seriesModel;
          } else {
            markerModel._mergeOption(markerOpt, ecModel, true);
          }
          inner(seriesModel)[componentType] = markerModel;
        }, this);
      }
    };
    MarkerModel2.prototype.formatTooltip = function(dataIndex, multipleSeries, dataType) {
      var data = this.getData();
      var value = this.getRawValue(dataIndex);
      var itemName = data.getName(dataIndex);
      return createTooltipMarkup("section", {
        header: this.name,
        blocks: [createTooltipMarkup("nameValue", {
          name: itemName,
          value,
          noName: !itemName,
          noValue: value == null
        })]
      });
    };
    MarkerModel2.prototype.getData = function() {
      return this._data;
    };
    MarkerModel2.prototype.setData = function(data) {
      this._data = data;
    };
    MarkerModel2.prototype.getDataParams = function(dataIndex, dataType) {
      var params = DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);
      var hostSeries = this.__hostSeries;
      if (hostSeries) {
        params.seriesId = hostSeries.id;
        params.seriesName = hostSeries.name;
        params.seriesType = hostSeries.subType;
      }
      return params;
    };
    MarkerModel2.getMarkerModelFromSeries = function(seriesModel, componentType) {
      return inner(seriesModel)[componentType];
    };
    MarkerModel2.type = "marker";
    MarkerModel2.dependencies = ["series", "grid", "polar", "geo"];
    return MarkerModel2;
  }(ComponentModel)
);
mixin(MarkerModel, DataFormatMixin.prototype);
export {
  MarkerModel as default
};

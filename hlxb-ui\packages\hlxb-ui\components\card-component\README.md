# HLCardComponent 组件库文档

## 组件库概述

HLCardComponent 是一个卡片组件库，提供了各种类型的卡片组件用于数据展示。组件库包含基础组件、图表组件和组合卡片组件，适用于各种数据展示场景。

## 文件结构

```
/src/project/aoa/components/HLCardComponent/
├── index.tsx                    # 组件库入口文件
├── src/
│   ├── basicComponents/         # 基础组件
│   │   ├── Ranking.vue          # 排名组件
│   │   ├── HorizontaSmallSquare.vue # 水平小方块组件
│   │   ├── EnergySummary.vue    # 能耗统计组件
│   │   ├── DrugSummary.vue      # 药耗统计组件
│   │   ├── Loading.vue          # 加载组件
│   │   ├── Empty.vue            # 空状态组件
│   │   └── RankingSimple.vue    # 简单排名组件
│   ├── charts/                  # 图表基础组件
│   │   ├── bar/                 # 柱状图
│   │   │   └── BarEcharts.vue
│   │   ├── line/                # 折线图
│   │   │   └── LineEcharts.vue
│   │   └── pie/                 # 饼图
│   │       └── PieEcharts.vue
│   └── combinationCards/        # 组合卡片组件
│       ├── index.ts             # 组合卡片入口文件
│       ├── chartsType/          # 图表类卡片
│       │   ├── BarSimpleCard.vue  # 简单柱状图卡片
│       │   ├── BarPlusCard.vue    # 增强柱状图卡片
│       │   ├── LineSimpleCard.vue # 简单折线图卡片
│       │   ├── LinePlusCard.vue   # 增强折线图卡片
│       │   ├── PieSimpleCard.vue  # 简单饼图卡片
│       │   └── PiePlusCard.vue    # 增强饼图卡片
│       ├── rankingType/         # 排名类卡片
│       │   ├── RankingSimpleCard.vue # 简单排名卡片
│       │   └── RankingPlusCard.vue   # 增强排名卡片
│       └── summaryType/         # 总结类卡片
│           └── SummarySimpleCard.vue  # 简单总结卡片
```

## 组件类型

### 基础组件

- `HlxbRanking`: 排名组件
- `HlxbHorizontaSmallSquare`: 水平小方块组件
- `HlxbEnergySummary`: 能耗统计组件
- `HlxbDrugSummary`: 药耗统计组件
- `HlxbCardLoading`: 加载状态组件
- `HlxbCardEmpty`: 空状态组件
- `HlxbRankingSimple`: 简单排名组件

### 基础图表组件

- `HlxbBarEcharts`: 柱状图组件
- `HlxbLineEcharts`: 折线图组件
- `HlxbPieEcharts`: 饼图组件

### 组合卡片组件

组合卡片组件是基于基础组件和基础图表组件封装的更高级别组件，分为三类：

1. **图表类卡片**:

   - `HlxbPieSimpleCard`: 简单饼图卡片
   - `HlxbPiePlusCard`: 增强饼图卡片
   - `HlxbLineSimpleCard`: 简单折线图卡片
   - `HlxbLinePlusCard`: 增强折线图卡片
   - `HlxbBarSimpleCard`: 简单柱状图卡片
   - `HlxbBarPlusCard`: 增强柱状图卡片

2. **排名类卡片**:

   - `HlxbRankingSimpleCard`: 简单排名卡片
   - `HlxbRankingPlusCard`: 增强排名卡片

3. **总结类卡片**:
   - `HlxbSummarySimpleCard`: 简单总结卡片

## 使用方式

### 直接引入组件

可以从组件库中直接引入所需组件：

```typescript
import { HlxbBarSimpleCard, HlxbLineSimpleCard, HlxbPieSimpleCard } from 'hlxb-ui';

// 然后在Vue组件中使用
<template>
  <HlxbBarSimpleCard
    :bottomList="dataList"
    :empty="isEmpty"
    :loading="isLoading"
    :themeColor="theme"
  />
</template>
```

### 使用 combinationCards

组件库提供了动态加载组件的方法，通过 `combinationCards/index.ts` 中的 `loadComponents` 函数可以动态加载所有组合卡片组件：

```typescript
// 直接从 hlxb-ui 导入所需组件
import {
  HlxbBarSimpleCard,
  HlxbLineSimpleCard,
  HlxbPieSimpleCard,
  HlxbRankingSimpleCard,
  HlxbSummarySimpleCard,
} from 'hlxb-ui';

// 在组件中使用
export default {
  components: {
    HlxbBarSimpleCard,
    HlxbLineSimpleCard,
    HlxbPieSimpleCard,
  },
};
```

### 组件属性

以 `HlxbBarSimpleCard` 为例，组合卡片组件通常支持以下属性：

- `bottomList`: 数据列表，用于图表渲染
- `empty`: 布尔值，表示数据是否为空
- `loading`: 布尔值，是否显示加载状态
- `themeColor`: 主题颜色，可选值包括 'light'、'Dark' 和 'screenColor'

所有组合卡片组件都基于 `HlxbCard` 组件构建，并支持通过插槽进行内容自定义。

## 主题支持

组件库支持以下主题：

- `light`: 亮色主题，适合白天环境
- `Dark`: 暗色主题，适合夜间环境
- `screenColor`: 透明背景主题，适合在大屏展示

## 注意事项

1. 组合卡片组件需要依赖 HlxbCard 组件
2. 使用组件时需要提供相应的数据格式，特别是图表类组件的数据结构
3. 所有组件支持主题切换，通过 themeColor 属性控制
4. 所有组件名称都以 `Hlxb` 前缀开始，保持命名一致性
5. 支持 TypeScript，提供完整的类型定义
6. 所有组件都支持响应式设计，适配不同屏幕尺寸
7. 提供丰富的插槽支持，支持灵活的内容定制

## 使用示例

### 基础图表组件使用

```vue
<template>
  <div>
    <!-- 柱状图 -->
    <HlxbBarEcharts :chartData="barData" :options="barOptions" />

    <!-- 折线图 -->
    <HlxbLineEcharts :chartData="lineData" :options="lineOptions" />

    <!-- 饼图 -->
    <HlxbPieEcharts :chartData="pieData" :options="pieOptions" />
  </div>
</template>

<script lang="ts" setup>
  import { HlxbBarEcharts, HlxbLineEcharts, HlxbPieEcharts } from 'hlxb-ui';
  import type { echartsListType } from 'hlxb-ui';

  // 图表数据
  const barData: echartsListType[] = [
    { name: '产品A', value: 120 },
    { name: '产品B', value: 80 },
    { name: '产品C', value: 150 },
  ];
</script>
```

### 组合卡片组件使用

```vue
<template>
  <div class="dashboard">
    <!-- 简单柱状图卡片 -->
    <HlxbBarSimpleCard
      title="销售数据"
      :bottomList="salesData"
      :empty="isEmpty"
      :loading="isLoading"
      themeColor="light"
    />

    <!-- 排名卡片 -->
    <HlxbRankingSimpleCard
      title="产品排名"
      :bottomList="rankingData"
      :empty="false"
      :loading="false"
      themeColor="Dark"
    />

    <!-- 总结卡片 -->
    <HlxbSummarySimpleCard title="数据概览" :bottomList="summaryData" themeColor="screenColor" />
  </div>
</template>

<script lang="ts" setup>
  import { HlxbBarSimpleCard, HlxbRankingSimpleCard, HlxbSummarySimpleCard } from 'hlxb-ui';
  import type { ArrayListType, rankingListType, summaryListType } from 'hlxb-ui';
  import 'hlxb-ui/style.css';

  // 数据定义
  const salesData: ArrayListType[] = [
    { name: '一月', value: 1200 },
    { name: '二月', value: 1350 },
    { name: '三月', value: 1100 },
  ];

  const isEmpty = ref(false);
  const isLoading = ref(false);
</script>
```

## 导入方式

### 完整导入

```typescript
import hlxbUI from 'hlxb-ui';
import 'hlxb-ui/style.css';

// 在 Vue 应用中注册
app.use(hlxbUI);
```

### 按需导入

```typescript
// 导入基础组件
import { HlxbButton, HlxbInput, HlxbCard, HlxbCardBody, HlxbCardHeader } from 'hlxb-ui';

// 导入卡片组件
import {
  HlxbRanking,
  HlxbEnergySummary,
  HlxbBarEcharts,
  HlxbLineEcharts,
  HlxbPieEcharts,
  HlxbBarSimpleCard,
  HlxbLineSimpleCard,
  HlxbPieSimpleCard,
} from 'hlxb-ui';

// 导入样式
import 'hlxb-ui/style.css';
```

### 类型定义导入

```typescript
// 导入数据类型
import type {
  echartsListType,
  ArrayListType,
  pieDataListType,
  HorizonListType,
  summaryListType,
  drugSummaryListType,
  rankingListType,
} from 'hlxb-ui';

// 导入组件相关类型
import type {
  CalendarOptions,
  DateItem,
  GridItem,
  HGridProps,
  HGridEmits,
  SwiperItem,
  ModuleName,
  WeatherData,
} from 'hlxb-ui';
```

## 数据类型说明

### echartsListType

用于基础图表组件的数据结构：

```typescript
interface echartsListType {
  name: string; // 数据项名称
  value: number; // 数据值
  [key: string]: any; // 其他扩展属性
}
```

### ArrayListType

用于组合卡片组件的通用数据结构：

```typescript
interface ArrayListType {
  name: string; // 项目名称
  value: number | string; // 项目值
  unit?: string; // 单位（可选）
  [key: string]: any;
}
```

### rankingListType

用于排名类组件：

```typescript
interface rankingListType {
  rank: number; // 排名
  name: string; // 名称
  value: number; // 值
  change?: number; // 变化幅度（可选）
}
```

## 组件属性说明

### 通用属性

所有组合卡片组件都支持以下通用属性：

| 属性名     | 类型    | 默认值  | 描述             |
| ---------- | ------- | ------- | ---------------- |
| title      | String  | -       | 卡片标题         |
| bottomList | Array   | []      | 数据列表         |
| empty      | Boolean | false   | 是否为空状态     |
| loading    | Boolean | false   | 是否显示加载状态 |
| themeColor | String  | 'light' | 主题颜色         |

### 主题选项

- `light`: 亮色主题，适合白天环境
- `Dark`: 暗色主题，适合夜间环境
- `screenColor`: 透明背景主题，适合在大屏展示

```

```

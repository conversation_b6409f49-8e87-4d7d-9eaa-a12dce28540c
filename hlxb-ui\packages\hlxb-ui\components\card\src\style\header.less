@import '../../../style/index.less';

@card-header-prefix-cls: ~'@{hlxb-prefix}-card-header';

.@{card-header-prefix-cls} {
  display: flex;
  align-items: center;
  padding: 0 16px;
  color: #333;
  min-height: 48px;
  justify-content: space-between;

  /* 底部分割线样式 */
  &.base-header_line {
    border-bottom: 1px solid #e9e9e9;
  }

  /* 左侧内容容器样式 */
  .header_left {
    display: flex;

    /* gap: 0 16px; */
    align-items: center;
  }

  /* 标题文本样式 */
  .text {
    font-size: 16px;
    font-weight: 600;
    white-space: nowrap;
  }

  &.Dark {
    color: #fff;
    // padding: 16px 8px 16px 16px;
    // height: calc(100% - 50px);
    // overflow-y: auto;
    // box-sizing: border-box;
  }

  &.light {
    color: #333;
  }

  &.screenColor {
    color: #fff;
    background: transparent;
  }
}

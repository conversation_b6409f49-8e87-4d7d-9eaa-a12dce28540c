import { defineComponent, watch, onMounted, onActivated, onUnmounted, onDeactivated, createElementBlock, openBlock, normalizeClass, unref, createElementVNode, Fragment, renderList, toDisplayString, createBlock, createCommentVNode, withCtx, normalizeStyle, createTextVNode } from "vue";
import { Tooltip } from "ant-design-vue";
import { getPrefixCls } from "../../../config/index.js";
import { useIntervalFn } from "../../../node_modules/.pnpm/@vueuse_shared@13.7.0_vue@3.5.20_typescript@4.9.5_/node_modules/@vueuse/shared/index.js";
const _hoisted_1 = { class: "total-list" };
const _hoisted_2 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_3 = {
  key: 2,
  class: "unit"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbHorizontaSmallSquare"
  },
  __name: "HorizontaSmallSquare",
  props: {
    className: {
      type: String,
      default: "horizonta-small-square"
    },
    pleftType: {
      type: Boolean,
      // value是否有左内间距
      default: false
    },
    classType: {
      type: Boolean,
      // value name是否有换行
      default: false
    },
    nameBefore: {
      // name 左边是否有小圆圈
      type: Boolean,
      default: false
    },
    dataList: {
      type: Array,
      default: () => []
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("horizonta-small-square");
    const props = __props;
    function showTooltip(e) {
      if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none";
      }
    }
    async function getIndicatorList() {
    }
    const { pause, resume } = useIntervalFn(getIndicatorList, 60 * 1e3);
    watch(
      () => props.dataList,
      (newVal) => {
        if (!newVal) {
          pause();
        }
      }
    );
    onMounted(() => {
      resume();
    });
    onActivated(() => {
      resume();
    });
    onUnmounted(() => {
      pause();
    });
    onDeactivated(() => {
      pause();
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        createElementVNode("div", _hoisted_1, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(__props.dataList, (item, index) => {
            return openBlock(), createElementBlock("div", {
              class: "item",
              key: index
            }, [
              createElementVNode("div", {
                class: normalizeClass(["content", __props.classType ? "content-column" : ""])
              }, [
                createElementVNode("div", {
                  class: normalizeClass(["name", __props.nameBefore ? `beforecor_${index}` : ""])
                }, toDisplayString(item.indexName), 3),
                createElementVNode("div", {
                  class: normalizeClass(["value", __props.pleftType ? `value_pleft` : ""])
                }, [
                  item.value === null ? (openBlock(), createElementBlock("div", _hoisted_2, "-")) : (openBlock(), createBlock(unref(Tooltip), {
                    key: 1,
                    onMouseenter: showTooltip
                  }, {
                    title: withCtx(() => [
                      createTextVNode(toDisplayString(item.value) + toDisplayString(item.unitName), 1)
                    ]),
                    default: withCtx(() => [
                      createElementVNode("div", {
                        class: "number-value",
                        style: normalizeStyle({ color: item.color ? item.color : "unset" })
                      }, toDisplayString(item.value ? item.value : ""), 5)
                    ]),
                    _: 2
                  }, 1024)),
                  item.value !== null ? (openBlock(), createElementBlock("span", _hoisted_3, toDisplayString(item.unitName), 1)) : createCommentVNode("", true)
                ], 2)
              ], 2)
            ]);
          }), 128))
        ])
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

import { create, min, max } from "./vector.js";
import { quadraticAt, cubicExtrema, cubicAt, quadraticExtremum } from "./curve.js";
var mathMin = Math.min;
var mathMax = Math.max;
var mathSin = Math.sin;
var mathCos = Math.cos;
var PI2 = Math.PI * 2;
var start = create();
var end = create();
var extremity = create();
function fromLine(x0, y0, x1, y1, min2, max2) {
  min2[0] = mathMin(x0, x1);
  min2[1] = mathMin(y0, y1);
  max2[0] = mathMax(x0, x1);
  max2[1] = mathMax(y0, y1);
}
var xDim = [];
var yDim = [];
function fromCubic(x0, y0, x1, y1, x2, y2, x3, y3, min2, max2) {
  var cubicExtrema$1 = cubicExtrema;
  var cubicAt$1 = cubicAt;
  var n = cubicExtrema$1(x0, x1, x2, x3, xDim);
  min2[0] = Infinity;
  min2[1] = Infinity;
  max2[0] = -Infinity;
  max2[1] = -Infinity;
  for (var i = 0; i < n; i++) {
    var x = cubicAt$1(x0, x1, x2, x3, xDim[i]);
    min2[0] = mathMin(x, min2[0]);
    max2[0] = mathMax(x, max2[0]);
  }
  n = cubicExtrema$1(y0, y1, y2, y3, yDim);
  for (var i = 0; i < n; i++) {
    var y = cubicAt$1(y0, y1, y2, y3, yDim[i]);
    min2[1] = mathMin(y, min2[1]);
    max2[1] = mathMax(y, max2[1]);
  }
  min2[0] = mathMin(x0, min2[0]);
  max2[0] = mathMax(x0, max2[0]);
  min2[0] = mathMin(x3, min2[0]);
  max2[0] = mathMax(x3, max2[0]);
  min2[1] = mathMin(y0, min2[1]);
  max2[1] = mathMax(y0, max2[1]);
  min2[1] = mathMin(y3, min2[1]);
  max2[1] = mathMax(y3, max2[1]);
}
function fromQuadratic(x0, y0, x1, y1, x2, y2, min2, max2) {
  var quadraticExtremum$1 = quadraticExtremum;
  var quadraticAt$1 = quadraticAt;
  var tx = mathMax(mathMin(quadraticExtremum$1(x0, x1, x2), 1), 0);
  var ty = mathMax(mathMin(quadraticExtremum$1(y0, y1, y2), 1), 0);
  var x = quadraticAt$1(x0, x1, x2, tx);
  var y = quadraticAt$1(y0, y1, y2, ty);
  min2[0] = mathMin(x0, x2, x);
  min2[1] = mathMin(y0, y2, y);
  max2[0] = mathMax(x0, x2, x);
  max2[1] = mathMax(y0, y2, y);
}
function fromArc(x, y, rx, ry, startAngle, endAngle, anticlockwise, min$1, max$1) {
  var vec2Min = min;
  var vec2Max = max;
  var diff = Math.abs(startAngle - endAngle);
  if (diff % PI2 < 1e-4 && diff > 1e-4) {
    min$1[0] = x - rx;
    min$1[1] = y - ry;
    max$1[0] = x + rx;
    max$1[1] = y + ry;
    return;
  }
  start[0] = mathCos(startAngle) * rx + x;
  start[1] = mathSin(startAngle) * ry + y;
  end[0] = mathCos(endAngle) * rx + x;
  end[1] = mathSin(endAngle) * ry + y;
  vec2Min(min$1, start, end);
  vec2Max(max$1, start, end);
  startAngle = startAngle % PI2;
  if (startAngle < 0) {
    startAngle = startAngle + PI2;
  }
  endAngle = endAngle % PI2;
  if (endAngle < 0) {
    endAngle = endAngle + PI2;
  }
  if (startAngle > endAngle && !anticlockwise) {
    endAngle += PI2;
  } else if (startAngle < endAngle && anticlockwise) {
    startAngle += PI2;
  }
  if (anticlockwise) {
    var tmp = endAngle;
    endAngle = startAngle;
    startAngle = tmp;
  }
  for (var angle = 0; angle < endAngle; angle += Math.PI / 2) {
    if (angle > startAngle) {
      extremity[0] = mathCos(angle) * rx + x;
      extremity[1] = mathSin(angle) * ry + y;
      vec2Min(min$1, extremity, min$1);
      vec2Max(max$1, extremity, max$1);
    }
  }
}
export {
  fromArc,
  fromCubic,
  fromLine,
  fromQuadratic
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const graphic = require("../../util/graphic.js");
const innerStore = require("../../util/innerStore.js");
const labelStyle = require("../../label/labelStyle.js");
const Model = require("../../model/Model.js");
const number = require("../../util/number.js");
const symbol = require("../../util/symbol.js");
const matrix = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/matrix.js");
const vector = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/vector.js");
const axisHelper = require("../../coord/axisHelper.js");
const labelLayoutHelper = require("../../label/labelLayoutHelper.js");
const model = require("../../util/model.js");
const axisBreakHelper = require("./axisBreakHelper.js");
const axisAction = require("./axisAction.js");
const BoundingRect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js");
const Point = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Point.js");
const Transformable = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Transformable.js");
const axisTickLabelBuilder = require("../../coord/axisTickLabelBuilder.js");
const Group = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js");
const Text = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js");
const Line = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Line.js");
const Rect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js");
var PI = Math.PI;
var DEFAULT_CENTER_NAME_MARGIN_LEVELS = [[1, 2, 1, 2], [5, 3, 5, 3], [8, 3, 8, 3]];
var DEFAULT_ENDS_NAME_MARGIN_LEVELS = [[0, 1, 0, 1], [0, 3, 0, 3], [0, 3, 0, 3]];
var getLabelInner = model.makeInner();
var getTickInner = model.makeInner();
var AxisBuilderSharedContext = (
  /** @class */
  function() {
    function AxisBuilderSharedContext2(resolveAxisNameOverlap) {
      this.recordMap = {};
      this.resolveAxisNameOverlap = resolveAxisNameOverlap;
    }
    AxisBuilderSharedContext2.prototype.ensureRecord = function(axisModel) {
      var dim = axisModel.axis.dim;
      var idx = axisModel.componentIndex;
      var recordMap = this.recordMap;
      var records = recordMap[dim] || (recordMap[dim] = []);
      return records[idx] || (records[idx] = {
        ready: {}
      });
    };
    return AxisBuilderSharedContext2;
  }()
);
function resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList) {
  var axis = axisModel.axis;
  var record = shared.ensureRecord(axisModel);
  var labelInfoList = [];
  var stOccupiedRect;
  var useStOccupiedRect = hasAxisName(cfg.axisName) && axisHelper.isNameLocationCenter(cfg.nameLocation);
  util.each(labelLayoutList, function(layout) {
    var layoutInfo = labelLayoutHelper.ensureLabelLayoutWithGeometry(layout);
    if (!layoutInfo || layoutInfo.label.ignore) {
      return;
    }
    labelInfoList.push(layoutInfo);
    var transGroup = record.transGroup;
    if (useStOccupiedRect) {
      transGroup.transform ? matrix.invert(_stTransTmp, transGroup.transform) : matrix.identity(_stTransTmp);
      if (layoutInfo.transform) {
        matrix.mul(_stTransTmp, _stTransTmp, layoutInfo.transform);
      }
      BoundingRect.default.copy(_stLabelRectTmp, layoutInfo.localRect);
      _stLabelRectTmp.applyTransform(_stTransTmp);
      stOccupiedRect ? stOccupiedRect.union(_stLabelRectTmp) : BoundingRect.default.copy(stOccupiedRect = new BoundingRect.default(0, 0, 0, 0), _stLabelRectTmp);
    }
  });
  var sortByDim = Math.abs(record.dirVec.x) > 0.1 ? "x" : "y";
  var sortByValue = record.transGroup[sortByDim];
  labelInfoList.sort(function(info1, info2) {
    return Math.abs(info1.label[sortByDim] - sortByValue) - Math.abs(info2.label[sortByDim] - sortByValue);
  });
  if (useStOccupiedRect && stOccupiedRect) {
    var extent = axis.getExtent();
    var axisLineX = Math.min(extent[0], extent[1]);
    var axisLineWidth = Math.max(extent[0], extent[1]) - axisLineX;
    stOccupiedRect.union(new BoundingRect.default(axisLineX, 0, axisLineWidth, 1));
  }
  record.stOccupiedRect = stOccupiedRect;
  record.labelInfoList = labelInfoList;
}
var _stTransTmp = matrix.create();
var _stLabelRectTmp = new BoundingRect.default(0, 0, 0, 0);
var resolveAxisNameOverlapDefault = function(cfg, ctx, axisModel, nameLayoutInfo, nameMoveDirVec, thisRecord) {
  if (axisHelper.isNameLocationCenter(cfg.nameLocation)) {
    var stOccupiedRect = thisRecord.stOccupiedRect;
    if (stOccupiedRect) {
      moveIfOverlap(labelLayoutHelper.computeLabelGeometry2({}, stOccupiedRect, thisRecord.transGroup.transform), nameLayoutInfo, nameMoveDirVec);
    }
  } else {
    moveIfOverlapByLinearLabels(thisRecord.labelInfoList, thisRecord.dirVec, nameLayoutInfo, nameMoveDirVec);
  }
};
function moveIfOverlap(basedLayoutInfo, movableLayoutInfo, moveDirVec) {
  var mtv = new Point.default();
  if (labelLayoutHelper.labelIntersect(basedLayoutInfo, movableLayoutInfo, mtv, {
    direction: Math.atan2(moveDirVec.y, moveDirVec.x),
    bidirectional: false,
    touchThreshold: 0.05
  })) {
    labelLayoutHelper.labelLayoutApplyTranslation(movableLayoutInfo, mtv);
  }
}
function moveIfOverlapByLinearLabels(baseLayoutInfoList, baseDirVec, movableLayoutInfo, moveDirVec) {
  var sameDir = Point.default.dot(moveDirVec, baseDirVec) >= 0;
  for (var idx = 0, len = baseLayoutInfoList.length; idx < len; idx++) {
    var labelInfo = baseLayoutInfoList[sameDir ? idx : len - 1 - idx];
    if (!labelInfo.label.ignore) {
      moveIfOverlap(labelInfo, movableLayoutInfo, moveDirVec);
    }
  }
}
var AxisBuilder = (
  /** @class */
  function() {
    function AxisBuilder2(axisModel, api, opt, shared) {
      this.group = new Group.default();
      this._axisModel = axisModel;
      this._api = api;
      this._local = {};
      this._shared = shared || new AxisBuilderSharedContext(resolveAxisNameOverlapDefault);
      this._resetCfgDetermined(opt);
    }
    AxisBuilder2.prototype.updateCfg = function(opt) {
      if (process.env.NODE_ENV !== "production") {
        var ready = this._shared.ensureRecord(this._axisModel).ready;
        util.assert(!ready.axisLine && !ready.axisTickLabelDetermine);
        ready.axisName = ready.axisTickLabelEstimate = false;
      }
      var raw = this._cfg.raw;
      raw.position = opt.position;
      raw.labelOffset = opt.labelOffset;
      this._resetCfgDetermined(raw);
    };
    AxisBuilder2.prototype.__getRawCfg = function() {
      return this._cfg.raw;
    };
    AxisBuilder2.prototype._resetCfgDetermined = function(raw) {
      var axisModel = this._axisModel;
      var axisModelDefaultOption = axisModel.getDefaultOption ? axisModel.getDefaultOption() : {};
      var axisName = util.retrieve2(raw.axisName, axisModel.get("name"));
      var nameMoveOverlapOption = axisModel.get("nameMoveOverlap");
      if (nameMoveOverlapOption == null || nameMoveOverlapOption === "auto") {
        nameMoveOverlapOption = util.retrieve2(raw.defaultNameMoveOverlap, true);
      }
      var cfg = {
        raw,
        position: raw.position,
        rotation: raw.rotation,
        nameDirection: util.retrieve2(raw.nameDirection, 1),
        tickDirection: util.retrieve2(raw.tickDirection, 1),
        labelDirection: util.retrieve2(raw.labelDirection, 1),
        labelOffset: util.retrieve2(raw.labelOffset, 0),
        silent: util.retrieve2(raw.silent, true),
        axisName,
        nameLocation: util.retrieve3(axisModel.get("nameLocation"), axisModelDefaultOption.nameLocation, "end"),
        shouldNameMoveOverlap: hasAxisName(axisName) && nameMoveOverlapOption,
        optionHideOverlap: axisModel.get(["axisLabel", "hideOverlap"]),
        showMinorTicks: axisModel.get(["minorTick", "show"])
      };
      if (process.env.NODE_ENV !== "production") {
        util.assert(cfg.position != null);
        util.assert(cfg.rotation != null);
      }
      this._cfg = cfg;
      var transformGroup = new Group.default({
        x: cfg.position[0],
        y: cfg.position[1],
        rotation: cfg.rotation
      });
      transformGroup.updateTransform();
      this._transformGroup = transformGroup;
      var record = this._shared.ensureRecord(axisModel);
      record.transGroup = this._transformGroup;
      record.dirVec = new Point.default(Math.cos(-cfg.rotation), Math.sin(-cfg.rotation));
    };
    AxisBuilder2.prototype.build = function(axisPartNameMap, extraParams) {
      var _this = this;
      if (!axisPartNameMap) {
        axisPartNameMap = {
          axisLine: true,
          axisTickLabelEstimate: false,
          axisTickLabelDetermine: true,
          axisName: true
        };
      }
      util.each(AXIS_BUILDER_AXIS_PART_NAMES, function(partName) {
        if (axisPartNameMap[partName]) {
          builders[partName](_this._cfg, _this._local, _this._shared, _this._axisModel, _this.group, _this._transformGroup, _this._api, extraParams || {});
        }
      });
      return this;
    };
    AxisBuilder2.innerTextLayout = function(axisRotation, textRotation, direction) {
      var rotationDiff = number.remRadian(textRotation - axisRotation);
      var textAlign;
      var textVerticalAlign;
      if (number.isRadianAroundZero(rotationDiff)) {
        textVerticalAlign = direction > 0 ? "top" : "bottom";
        textAlign = "center";
      } else if (number.isRadianAroundZero(rotationDiff - PI)) {
        textVerticalAlign = direction > 0 ? "bottom" : "top";
        textAlign = "center";
      } else {
        textVerticalAlign = "middle";
        if (rotationDiff > 0 && rotationDiff < PI) {
          textAlign = direction > 0 ? "right" : "left";
        } else {
          textAlign = direction > 0 ? "left" : "right";
        }
      }
      return {
        rotation: rotationDiff,
        textAlign,
        textVerticalAlign
      };
    };
    AxisBuilder2.makeAxisEventDataBase = function(axisModel) {
      var eventData = {
        componentType: axisModel.mainType,
        componentIndex: axisModel.componentIndex
      };
      eventData[axisModel.mainType + "Index"] = axisModel.componentIndex;
      return eventData;
    };
    AxisBuilder2.isLabelSilent = function(axisModel) {
      var tooltipOpt = axisModel.get("tooltip");
      return axisModel.get("silent") || !(axisModel.get("triggerEvent") || tooltipOpt && tooltipOpt.show);
    };
    return AxisBuilder2;
  }()
);
var AXIS_BUILDER_AXIS_PART_NAMES = ["axisLine", "axisTickLabelEstimate", "axisTickLabelDetermine", "axisName"];
var builders = {
  axisLine: function(cfg, local, shared, axisModel, group, transformGroup, api) {
    if (process.env.NODE_ENV !== "production") {
      var ready = shared.ensureRecord(axisModel).ready;
      util.assert(!ready.axisLine);
      ready.axisLine = true;
    }
    var shown = axisModel.get(["axisLine", "show"]);
    if (shown === "auto") {
      shown = true;
      if (cfg.raw.axisLineAutoShow != null) {
        shown = !!cfg.raw.axisLineAutoShow;
      }
    }
    if (!shown) {
      return;
    }
    var extent = axisModel.axis.getExtent();
    var matrix2 = transformGroup.transform;
    var pt1 = [extent[0], 0];
    var pt2 = [extent[1], 0];
    var inverse = pt1[0] > pt2[0];
    if (matrix2) {
      vector.applyTransform(pt1, pt1, matrix2);
      vector.applyTransform(pt2, pt2, matrix2);
    }
    var lineStyle = util.extend({
      lineCap: "round"
    }, axisModel.getModel(["axisLine", "lineStyle"]).getLineStyle());
    var pathBaseProp = {
      strokeContainThreshold: cfg.raw.strokeContainThreshold || 5,
      silent: true,
      z2: 1,
      style: lineStyle
    };
    if (axisModel.get(["axisLine", "breakLine"]) && axisModel.axis.scale.hasBreaks()) {
      axisBreakHelper.getAxisBreakHelper().buildAxisBreakLine(axisModel, group, transformGroup, pathBaseProp);
    } else {
      var line = new Line.default(util.extend({
        shape: {
          x1: pt1[0],
          y1: pt1[1],
          x2: pt2[0],
          y2: pt2[1]
        }
      }, pathBaseProp));
      graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);
      line.anid = "line";
      group.add(line);
    }
    var arrows = axisModel.get(["axisLine", "symbol"]);
    if (arrows != null) {
      var arrowSize = axisModel.get(["axisLine", "symbolSize"]);
      if (util.isString(arrows)) {
        arrows = [arrows, arrows];
      }
      if (util.isString(arrowSize) || util.isNumber(arrowSize)) {
        arrowSize = [arrowSize, arrowSize];
      }
      var arrowOffset = symbol.normalizeSymbolOffset(axisModel.get(["axisLine", "symbolOffset"]) || 0, arrowSize);
      var symbolWidth_1 = arrowSize[0];
      var symbolHeight_1 = arrowSize[1];
      util.each([{
        rotate: cfg.rotation + Math.PI / 2,
        offset: arrowOffset[0],
        r: 0
      }, {
        rotate: cfg.rotation - Math.PI / 2,
        offset: arrowOffset[1],
        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))
      }], function(point, index) {
        if (arrows[index] !== "none" && arrows[index] != null) {
          var symbol$1 = symbol.createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);
          var r = point.r + point.offset;
          var pt = inverse ? pt2 : pt1;
          symbol$1.attr({
            rotation: point.rotate,
            x: pt[0] + r * Math.cos(cfg.rotation),
            y: pt[1] - r * Math.sin(cfg.rotation),
            silent: true,
            z2: 11
          });
          group.add(symbol$1);
        }
      });
    }
  },
  /**
   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called
   *  in size measurement. Thus this method should be idempotent, and should be performant.
   */
  axisTickLabelEstimate: function(cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {
    if (process.env.NODE_ENV !== "production") {
      var ready = shared.ensureRecord(axisModel).ready;
      util.assert(!ready.axisTickLabelDetermine);
      ready.axisTickLabelEstimate = true;
    }
    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);
    if (needCallLayout) {
      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, axisTickLabelBuilder.AxisTickLabelComputingKind.estimate);
    }
  },
  /**
   * Finish axis tick label build.
   * Can be only called once.
   */
  axisTickLabelDetermine: function(cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {
    if (process.env.NODE_ENV !== "production") {
      var ready = shared.ensureRecord(axisModel).ready;
      ready.axisTickLabelDetermine = true;
    }
    var needCallLayout = dealLastTickLabelResultReusable(local, group, extraParams);
    if (needCallLayout) {
      layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, axisTickLabelBuilder.AxisTickLabelComputingKind.determine);
    }
    var ticksEls = buildAxisMajorTicks(cfg, group, transformGroup, axisModel);
    syncLabelIgnoreToMajorTicks(cfg, local.labelLayoutList, ticksEls);
    buildAxisMinorTicks(cfg, group, transformGroup, axisModel, cfg.tickDirection);
  },
  /**
   * [CAUTION] This method can be called multiple times, following the change due to `resetCfg` called
   *  in size measurement. Thus this method should be idempotent, and should be performant.
   */
  axisName: function(cfg, local, shared, axisModel, group, transformGroup, api, extraParams) {
    var sharedRecord = shared.ensureRecord(axisModel);
    if (process.env.NODE_ENV !== "production") {
      var ready = sharedRecord.ready;
      util.assert(ready.axisTickLabelEstimate || ready.axisTickLabelDetermine);
      ready.axisName = true;
    }
    if (local.nameEl) {
      group.remove(local.nameEl);
      local.nameEl = sharedRecord.nameLayout = sharedRecord.nameLocation = null;
    }
    var name = cfg.axisName;
    if (!hasAxisName(name)) {
      return;
    }
    var nameLocation = cfg.nameLocation;
    var nameDirection = cfg.nameDirection;
    var textStyleModel = axisModel.getModel("nameTextStyle");
    var gap = axisModel.get("nameGap") || 0;
    var extent = axisModel.axis.getExtent();
    var gapStartEndSignal = axisModel.axis.inverse ? -1 : 1;
    var pos = new Point.default(0, 0);
    var nameMoveDirVec = new Point.default(0, 0);
    if (nameLocation === "start") {
      pos.x = extent[0] - gapStartEndSignal * gap;
      nameMoveDirVec.x = -gapStartEndSignal;
    } else if (nameLocation === "end") {
      pos.x = extent[1] + gapStartEndSignal * gap;
      nameMoveDirVec.x = gapStartEndSignal;
    } else {
      pos.x = (extent[0] + extent[1]) / 2;
      pos.y = cfg.labelOffset + nameDirection * gap;
      nameMoveDirVec.y = nameDirection;
    }
    var mt = matrix.create();
    nameMoveDirVec.transform(matrix.rotate(mt, mt, cfg.rotation));
    var nameRotation = axisModel.get("nameRotate");
    if (nameRotation != null) {
      nameRotation = nameRotation * PI / 180;
    }
    var labelLayout;
    var axisNameAvailableWidth;
    if (axisHelper.isNameLocationCenter(nameLocation)) {
      labelLayout = AxisBuilder.innerTextLayout(
        cfg.rotation,
        nameRotation != null ? nameRotation : cfg.rotation,
        // Adapt to axis.
        nameDirection
      );
    } else {
      labelLayout = endTextLayout(cfg.rotation, nameLocation, nameRotation || 0, extent);
      axisNameAvailableWidth = cfg.raw.axisNameAvailableWidth;
      if (axisNameAvailableWidth != null) {
        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));
        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);
      }
    }
    var textFont = textStyleModel.getFont();
    var truncateOpt = axisModel.get("nameTruncate", true) || {};
    var ellipsis = truncateOpt.ellipsis;
    var maxWidth = util.retrieve(cfg.raw.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);
    var nameMarginLevel = extraParams.nameMarginLevel || 0;
    var textEl = new Text.default({
      x: pos.x,
      y: pos.y,
      rotation: labelLayout.rotation,
      silent: AxisBuilder.isLabelSilent(axisModel),
      style: labelStyle.createTextStyle(textStyleModel, {
        text: name,
        font: textFont,
        overflow: "truncate",
        width: maxWidth,
        ellipsis,
        fill: textStyleModel.getTextColor() || axisModel.get(["axisLine", "lineStyle", "color"]),
        align: textStyleModel.get("align") || labelLayout.textAlign,
        verticalAlign: textStyleModel.get("verticalAlign") || labelLayout.textVerticalAlign
      }),
      z2: 1
    });
    graphic.setTooltipConfig({
      el: textEl,
      componentModel: axisModel,
      itemName: name
    });
    textEl.__fullText = name;
    textEl.anid = "name";
    if (axisModel.get("triggerEvent")) {
      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);
      eventData.targetType = "axisName";
      eventData.name = name;
      innerStore.getECData(textEl).eventData = eventData;
    }
    transformGroup.add(textEl);
    textEl.updateTransform();
    local.nameEl = textEl;
    var nameLayout = sharedRecord.nameLayout = labelLayoutHelper.ensureLabelLayoutWithGeometry({
      label: textEl,
      priority: textEl.z2,
      defaultAttr: {
        ignore: textEl.ignore
      },
      marginDefault: axisHelper.isNameLocationCenter(nameLocation) ? DEFAULT_CENTER_NAME_MARGIN_LEVELS[nameMarginLevel] : DEFAULT_ENDS_NAME_MARGIN_LEVELS[nameMarginLevel]
    });
    sharedRecord.nameLocation = nameLocation;
    group.add(textEl);
    textEl.decomposeTransform();
    if (cfg.shouldNameMoveOverlap && nameLayout) {
      var record = shared.ensureRecord(axisModel);
      if (process.env.NODE_ENV !== "production") {
        util.assert(record.labelInfoList);
      }
      shared.resolveAxisNameOverlap(cfg, shared, axisModel, nameLayout, nameMoveDirVec, record);
    }
  }
};
function layOutAxisTickLabel(cfg, local, shared, axisModel, group, transformGroup, api, kind) {
  if (!axisLabelBuildResultExists(local)) {
    buildAxisLabel(cfg, local, group, kind, axisModel, api);
  }
  var labelLayoutList = local.labelLayoutList;
  updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup);
  adjustBreakLabels(axisModel, cfg.rotation);
  var optionHideOverlap = cfg.optionHideOverlap;
  fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap);
  if (optionHideOverlap) {
    labelLayoutHelper.hideOverlap(
      // Filter the already ignored labels by the previous overlap resolving methods.
      util.filter(labelLayoutList, function(layout) {
        return layout && !layout.label.ignore;
      })
    );
  }
  resetOverlapRecordToShared(cfg, shared, axisModel, labelLayoutList);
}
function endTextLayout(rotation, textPosition, textRotate, extent) {
  var rotationDiff = number.remRadian(textRotate - rotation);
  var textAlign;
  var textVerticalAlign;
  var inverse = extent[0] > extent[1];
  var onLeft = textPosition === "start" && !inverse || textPosition !== "start" && inverse;
  if (number.isRadianAroundZero(rotationDiff - PI / 2)) {
    textVerticalAlign = onLeft ? "bottom" : "top";
    textAlign = "center";
  } else if (number.isRadianAroundZero(rotationDiff - PI * 1.5)) {
    textVerticalAlign = onLeft ? "top" : "bottom";
    textAlign = "center";
  } else {
    textVerticalAlign = "middle";
    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {
      textAlign = onLeft ? "left" : "right";
    } else {
      textAlign = onLeft ? "right" : "left";
    }
  }
  return {
    rotation: rotationDiff,
    textAlign,
    textVerticalAlign
  };
}
function fixMinMaxLabelShow(axisModel, labelLayoutList, optionHideOverlap) {
  if (axisHelper.shouldShowAllLabels(axisModel.axis)) {
    return;
  }
  function deal(showMinMaxLabel, outmostLabelIdx, innerLabelIdx) {
    var outmostLabelLayout = labelLayoutHelper.ensureLabelLayoutWithGeometry(labelLayoutList[outmostLabelIdx]);
    var innerLabelLayout = labelLayoutHelper.ensureLabelLayoutWithGeometry(labelLayoutList[innerLabelIdx]);
    if (!outmostLabelLayout || !innerLabelLayout) {
      return;
    }
    if (showMinMaxLabel === false || outmostLabelLayout.suggestIgnore) {
      ignoreEl(outmostLabelLayout.label);
      return;
    }
    if (innerLabelLayout.suggestIgnore) {
      ignoreEl(innerLabelLayout.label);
      return;
    }
    var touchThreshold = 0.1;
    if (!optionHideOverlap) {
      var marginForce = [0, 0, 0, 0];
      outmostLabelLayout = labelLayoutHelper.newLabelLayoutWithGeometry({
        marginForce
      }, outmostLabelLayout);
      innerLabelLayout = labelLayoutHelper.newLabelLayoutWithGeometry({
        marginForce
      }, innerLabelLayout);
    }
    if (labelLayoutHelper.labelIntersect(outmostLabelLayout, innerLabelLayout, null, {
      touchThreshold
    })) {
      if (showMinMaxLabel) {
        ignoreEl(innerLabelLayout.label);
      } else {
        ignoreEl(outmostLabelLayout.label);
      }
    }
  }
  var showMinLabel = axisModel.get(["axisLabel", "showMinLabel"]);
  var showMaxLabel = axisModel.get(["axisLabel", "showMaxLabel"]);
  var labelsLen = labelLayoutList.length;
  deal(showMinLabel, 0, 1);
  deal(showMaxLabel, labelsLen - 1, labelsLen - 2);
}
function syncLabelIgnoreToMajorTicks(cfg, labelLayoutList, tickEls) {
  if (cfg.showMinorTicks) {
    return;
  }
  util.each(labelLayoutList, function(labelLayout) {
    if (labelLayout && labelLayout.label.ignore) {
      for (var idx = 0; idx < tickEls.length; idx++) {
        var tickEl = tickEls[idx];
        var tickInner = getTickInner(tickEl);
        var labelInner = getLabelInner(labelLayout.label);
        if (tickInner.tickValue != null && !tickInner.onBand && tickInner.tickValue === labelInner.tickValue) {
          ignoreEl(tickEl);
          return;
        }
      }
    }
  });
}
function ignoreEl(el) {
  el && (el.ignore = true);
}
function createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {
  var tickEls = [];
  var pt1 = [];
  var pt2 = [];
  for (var i = 0; i < ticksCoords.length; i++) {
    var tickCoord = ticksCoords[i].coord;
    pt1[0] = tickCoord;
    pt1[1] = 0;
    pt2[0] = tickCoord;
    pt2[1] = tickEndCoord;
    if (tickTransform) {
      vector.applyTransform(pt1, pt1, tickTransform);
      vector.applyTransform(pt2, pt2, tickTransform);
    }
    var tickEl = new Line.default({
      shape: {
        x1: pt1[0],
        y1: pt1[1],
        x2: pt2[0],
        y2: pt2[1]
      },
      style: tickLineStyle,
      z2: 2,
      autoBatch: true,
      silent: true
    });
    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);
    tickEl.anid = anidPrefix + "_" + ticksCoords[i].tickValue;
    tickEls.push(tickEl);
    var inner = getTickInner(tickEl);
    inner.onBand = !!ticksCoords[i].onBand;
    inner.tickValue = ticksCoords[i].tickValue;
  }
  return tickEls;
}
function buildAxisMajorTicks(cfg, group, transformGroup, axisModel) {
  var axis = axisModel.axis;
  var tickModel = axisModel.getModel("axisTick");
  var shown = tickModel.get("show");
  if (shown === "auto") {
    shown = true;
    if (cfg.raw.axisTickAutoShow != null) {
      shown = !!cfg.raw.axisTickAutoShow;
    }
  }
  if (!shown || axis.scale.isBlank()) {
    return [];
  }
  var lineStyleModel = tickModel.getModel("lineStyle");
  var tickEndCoord = cfg.tickDirection * tickModel.get("length");
  var ticksCoords = axis.getTicksCoords();
  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, util.defaults(lineStyleModel.getLineStyle(), {
    stroke: axisModel.get(["axisLine", "lineStyle", "color"])
  }), "ticks");
  for (var i = 0; i < ticksEls.length; i++) {
    group.add(ticksEls[i]);
  }
  return ticksEls;
}
function buildAxisMinorTicks(cfg, group, transformGroup, axisModel, tickDirection) {
  var axis = axisModel.axis;
  var minorTickModel = axisModel.getModel("minorTick");
  if (!cfg.showMinorTicks || axis.scale.isBlank()) {
    return;
  }
  var minorTicksCoords = axis.getMinorTicksCoords();
  if (!minorTicksCoords.length) {
    return;
  }
  var lineStyleModel = minorTickModel.getModel("lineStyle");
  var tickEndCoord = tickDirection * minorTickModel.get("length");
  var minorTickLineStyle = util.defaults(lineStyleModel.getLineStyle(), util.defaults(axisModel.getModel("axisTick").getLineStyle(), {
    stroke: axisModel.get(["axisLine", "lineStyle", "color"])
  }));
  for (var i = 0; i < minorTicksCoords.length; i++) {
    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, "minorticks_" + i);
    for (var k = 0; k < minorTicksEls.length; k++) {
      group.add(minorTicksEls[k]);
    }
  }
}
function dealLastTickLabelResultReusable(local, group, extraParams) {
  if (axisLabelBuildResultExists(local)) {
    var axisLabelsCreationContext = local.axisLabelsCreationContext;
    if (process.env.NODE_ENV !== "production") {
      util.assert(local.labelGroup && axisLabelsCreationContext);
    }
    var noPxChangeTryDetermine = axisLabelsCreationContext.out.noPxChangeTryDetermine;
    if (extraParams.noPxChange) {
      var canDetermine = true;
      for (var idx = 0; idx < noPxChangeTryDetermine.length; idx++) {
        canDetermine = canDetermine && noPxChangeTryDetermine[idx]();
      }
      if (canDetermine) {
        return false;
      }
    }
    if (noPxChangeTryDetermine.length) {
      group.remove(local.labelGroup);
      axisLabelBuildResultSet(local, null, null, null);
    }
  }
  return true;
}
function buildAxisLabel(cfg, local, group, kind, axisModel, api) {
  var axis = axisModel.axis;
  var show = util.retrieve(cfg.raw.axisLabelShow, axisModel.get(["axisLabel", "show"]));
  var labelGroup = new Group.default();
  group.add(labelGroup);
  var axisLabelCreationCtx = axisTickLabelBuilder.createAxisLabelsComputingContext(kind);
  if (!show || axis.scale.isBlank()) {
    axisLabelBuildResultSet(local, [], labelGroup, axisLabelCreationCtx);
    return;
  }
  var labelModel = axisModel.getModel("axisLabel");
  var labels = axis.getViewLabels(axisLabelCreationCtx);
  var labelRotation = (util.retrieve(cfg.raw.labelRotate, labelModel.get("rotate")) || 0) * PI / 180;
  var labelLayout = AxisBuilder.innerTextLayout(cfg.rotation, labelRotation, cfg.labelDirection);
  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);
  var labelEls = [];
  var triggerEvent = axisModel.get("triggerEvent");
  var z2Min = Infinity;
  var z2Max = -Infinity;
  util.each(labels, function(labelItem, index) {
    var _a;
    var tickValue = axis.scale.type === "ordinal" ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;
    var formattedLabel = labelItem.formattedLabel;
    var rawLabel = labelItem.rawLabel;
    var itemLabelModel = labelModel;
    if (rawCategoryData && rawCategoryData[tickValue]) {
      var rawCategoryItem = rawCategoryData[tickValue];
      if (util.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {
        itemLabelModel = new Model.default(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);
      }
    }
    var textColor = itemLabelModel.getTextColor() || axisModel.get(["axisLine", "lineStyle", "color"]);
    var align = itemLabelModel.getShallow("align", true) || labelLayout.textAlign;
    var alignMin = util.retrieve2(itemLabelModel.getShallow("alignMinLabel", true), align);
    var alignMax = util.retrieve2(itemLabelModel.getShallow("alignMaxLabel", true), align);
    var verticalAlign = itemLabelModel.getShallow("verticalAlign", true) || itemLabelModel.getShallow("baseline", true) || labelLayout.textVerticalAlign;
    var verticalAlignMin = util.retrieve2(itemLabelModel.getShallow("verticalAlignMinLabel", true), verticalAlign);
    var verticalAlignMax = util.retrieve2(itemLabelModel.getShallow("verticalAlignMaxLabel", true), verticalAlign);
    var z2 = 10 + (((_a = labelItem.time) === null || _a === void 0 ? void 0 : _a.level) || 0);
    z2Min = Math.min(z2Min, z2);
    z2Max = Math.max(z2Max, z2);
    var textEl = new Text.default({
      // --- transform props start ---
      // All of the transform props MUST not be set here, but should be set in
      // `updateAxisLabelChangableProps`, because they may change in estimation,
      // and need to calculate based on global coord sys by `decomposeTransform`.
      x: 0,
      y: 0,
      rotation: 0,
      // --- transform props end ---
      silent: AxisBuilder.isLabelSilent(axisModel),
      z2,
      style: labelStyle.createTextStyle(itemLabelModel, {
        text: formattedLabel,
        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,
        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,
        fill: util.isFunction(textColor) ? textColor(
          // (1) In category axis with data zoom, tick is not the original
          // index of axis.data. So tick should not be exposed to user
          // in category axis.
          // (2) Compatible with previous version, which always use formatted label as
          // input. But in interval scale the formatted label is like '223,445', which
          // maked user replace ','. So we modify it to return original val but remain
          // it as 'string' to avoid error in replacing.
          axis.type === "category" ? rawLabel : axis.type === "value" ? tickValue + "" : tickValue,
          index
        ) : textColor
      })
    });
    textEl.anid = "label_" + tickValue;
    var inner = getLabelInner(textEl);
    inner["break"] = labelItem["break"];
    inner.tickValue = tickValue;
    inner.layoutRotation = labelLayout.rotation;
    graphic.setTooltipConfig({
      el: textEl,
      componentModel: axisModel,
      itemName: formattedLabel,
      formatterParamsExtra: {
        isTruncated: function() {
          return textEl.isTruncated;
        },
        value: rawLabel,
        tickIndex: index
      }
    });
    if (triggerEvent) {
      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);
      eventData.targetType = "axisLabel";
      eventData.value = rawLabel;
      eventData.tickIndex = index;
      if (labelItem["break"]) {
        eventData["break"] = {
          // type: labelItem.break.type,
          start: labelItem["break"].parsedBreak.vmin,
          end: labelItem["break"].parsedBreak.vmax
        };
      }
      if (axis.type === "category") {
        eventData.dataIndex = tickValue;
      }
      innerStore.getECData(textEl).eventData = eventData;
      if (labelItem["break"]) {
        addBreakEventHandler(axisModel, api, textEl, labelItem["break"]);
      }
    }
    labelEls.push(textEl);
    labelGroup.add(textEl);
  });
  var labelLayoutList = util.map(labelEls, function(label) {
    return {
      label,
      priority: getLabelInner(label)["break"] ? label.z2 + (z2Max - z2Min + 1) : label.z2,
      defaultAttr: {
        ignore: label.ignore
      }
    };
  });
  axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelCreationCtx);
}
function axisLabelBuildResultExists(local) {
  return !!local.labelLayoutList;
}
function axisLabelBuildResultSet(local, labelLayoutList, labelGroup, axisLabelsCreationContext) {
  local.labelLayoutList = labelLayoutList;
  local.labelGroup = labelGroup;
  local.axisLabelsCreationContext = axisLabelsCreationContext;
}
function updateAxisLabelChangableProps(cfg, axisModel, labelLayoutList, transformGroup) {
  var labelMargin = axisModel.get(["axisLabel", "margin"]);
  util.each(labelLayoutList, function(layout, idx) {
    var geometry = labelLayoutHelper.ensureLabelLayoutWithGeometry(layout);
    if (!geometry) {
      return;
    }
    var labelEl = geometry.label;
    var inner = getLabelInner(labelEl);
    geometry.suggestIgnore = labelEl.ignore;
    labelEl.ignore = false;
    Transformable.copyTransform(_tmpLayoutEl, _tmpLayoutElReset);
    _tmpLayoutEl.x = axisModel.axis.dataToCoord(inner.tickValue);
    _tmpLayoutEl.y = cfg.labelOffset + cfg.labelDirection * labelMargin;
    _tmpLayoutEl.rotation = inner.layoutRotation;
    transformGroup.add(_tmpLayoutEl);
    _tmpLayoutEl.updateTransform();
    transformGroup.remove(_tmpLayoutEl);
    _tmpLayoutEl.decomposeTransform();
    Transformable.copyTransform(labelEl, _tmpLayoutEl);
    labelEl.markRedraw();
    labelLayoutHelper.setLabelLayoutDirty(geometry, true);
    labelLayoutHelper.ensureLabelLayoutWithGeometry(geometry);
  });
}
var _tmpLayoutEl = new Rect.default();
var _tmpLayoutElReset = new Rect.default();
function hasAxisName(axisName) {
  return !!axisName;
}
function addBreakEventHandler(axisModel, api, textEl, visualBreak) {
  textEl.on("click", function(params) {
    var payload = {
      type: axisAction.AXIS_BREAK_EXPAND_ACTION_TYPE,
      breaks: [{
        start: visualBreak.parsedBreak.breakOption.start,
        end: visualBreak.parsedBreak.breakOption.end
      }]
    };
    payload[axisModel.axis.dim + "AxisIndex"] = axisModel.componentIndex;
    api.dispatchAction(payload);
  });
}
function adjustBreakLabels(axisModel, axisRotation, labelLayoutList) {
  {
    return;
  }
}
exports.AxisBuilderSharedContext = AxisBuilderSharedContext;
exports.default = AxisBuilder;
exports.getLabelInner = getLabelInner;
exports.moveIfOverlapByLinearLabels = moveIfOverlapByLinearLabels;
exports.resolveAxisNameOverlapDefault = resolveAxisNameOverlapDefault;

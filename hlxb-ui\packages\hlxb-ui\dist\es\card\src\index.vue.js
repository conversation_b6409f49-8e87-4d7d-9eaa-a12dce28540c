import { defineComponent, useSlots, computed, createElementBlock, openBlock, normalizeClass, normalizeStyle, unref, renderSlot, createVNode, createElementVNode, mergeProps, createSlots, renderList, withCtx } from "vue";
import _sfc_main$1 from "./CardHeader.vue.js";
/* empty css                */
import _sfc_main$2 from "./CardBody.vue.js";
/* empty css              */
import { filterAllowedSlots, cardHeaderDate, cardBodyDate } from "./data.js";
import { getPrefixCls } from "../../config/index.js";
const _hoisted_1 = { class: "container-content" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCard",
    inheritAttrs: false
  },
  __name: "index",
  props: {
    // 卡片的额外类名，类型为字符串，默认值为空字符串
    className: {
      type: String,
      default: ""
    },
    // 卡片的样式对象，类型为 CSSProperties，默认值为空对象
    styleData: {
      type: Object,
      default: () => ({})
    },
    // 深色模式 浅色 大屏等其他主题色
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("card");
    const slots = useSlots();
    const filterCardHeader = computed(() => {
      return filterAllowedSlots(slots, cardHeaderDate);
    });
    const filterCardBody = computed(() => {
      return filterAllowedSlots(slots, cardBodyDate);
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        style: normalizeStyle(Object.keys(__props.styleData).length ? __props.styleData : ""),
        class: normalizeClass([unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        renderSlot(_ctx.$slots, "default"),
        createVNode(_sfc_main$1, mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), createSlots({ _: 2 }, [
          renderList(filterCardHeader.value, (item) => {
            return {
              name: item,
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, item)
              ])
            };
          })
        ]), 1040, ["themeColor"]),
        createElementVNode("div", _hoisted_1, [
          createVNode(_sfc_main$2, mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), createSlots({ _: 2 }, [
            renderList(filterCardBody.value, (item) => {
              return {
                name: item,
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, item)
                ])
              };
            })
          ]), 1040, ["themeColor"])
        ])
      ], 6);
    };
  }
});
export {
  _sfc_main as default
};

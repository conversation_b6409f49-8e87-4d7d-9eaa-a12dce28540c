"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const index = require("../../../utils/index.js");
const Ranking_vue_vue_type_script_setup_true_lang = require("./Ranking.vue.js");
;/* empty css              */
const RankingSimple_vue_vue_type_script_setup_true_lang = require("./RankingSimple.vue.js");
;/* empty css                    */
const EnergySummary_vue_vue_type_script_setup_true_lang = require("./EnergySummary.vue.js");
;/* empty css                    */
const DrugSummary_vue_vue_type_script_setup_true_lang = require("./DrugSummary.vue.js");
;/* empty css                  */
const HorizontaSmallSquare_vue_vue_type_script_setup_true_lang = require("./HorizontaSmallSquare.vue.js");
;/* empty css                           */
const Loading_vue_vue_type_script_setup_true_name_CardLoading_lang = require("./Loading.vue.js");
;/* empty css              */
const Empty_vue_vue_type_script_setup_true_name_HEmpty_lang = require("./Empty.vue.js");
;/* empty css            */
const HlxbRanking = index.withInstall(Ranking_vue_vue_type_script_setup_true_lang.default);
const HlxbRankingSimple = index.withInstall(RankingSimple_vue_vue_type_script_setup_true_lang.default);
const HlxbEnergySummary = index.withInstall(EnergySummary_vue_vue_type_script_setup_true_lang.default);
const HlxbDrugSummary = index.withInstall(DrugSummary_vue_vue_type_script_setup_true_lang.default);
const HlxbHorizontaSmallSquare = index.withInstall(HorizontaSmallSquare_vue_vue_type_script_setup_true_lang.default);
const HlxbCardLoading = index.withInstall(Loading_vue_vue_type_script_setup_true_name_CardLoading_lang.default);
const HlxbCardEmpty = index.withInstall(Empty_vue_vue_type_script_setup_true_name_HEmpty_lang.default);
exports.HlxbCardEmpty = HlxbCardEmpty;
exports.HlxbCardLoading = HlxbCardLoading;
exports.HlxbDrugSummary = HlxbDrugSummary;
exports.HlxbEnergySummary = HlxbEnergySummary;
exports.HlxbHorizontaSmallSquare = HlxbHorizontaSmallSquare;
exports.HlxbRanking = HlxbRanking;
exports.HlxbRankingSimple = HlxbRankingSimple;

import { retrieve, each, isArray } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import Polar, { polarDimensions } from "./Polar.js";
import { parsePercent } from "../../util/number.js";
import { getDataDimensionsOnAxis, niceScaleExtent, createScaleByModel } from "../axisHelper.js";
import { SINGLE_REFERRING } from "../../util/model.js";
import { createBoxLayoutReference } from "../../util/layout.js";
function resizePolar(polar, polarModel, api) {
  var center = polarModel.get("center");
  var refContainer = createBoxLayoutReference(polarModel, api).refContainer;
  polar.cx = parsePercent(center[0], refContainer.width) + refContainer.x;
  polar.cy = parsePercent(center[1], refContainer.height) + refContainer.y;
  var radiusAxis = polar.getRadiusAxis();
  var size = Math.min(refContainer.width, refContainer.height) / 2;
  var radius = polarModel.get("radius");
  if (radius == null) {
    radius = [0, "100%"];
  } else if (!isArray(radius)) {
    radius = [0, radius];
  }
  var parsedRadius = [parsePercent(radius[0], size), parsePercent(radius[1], size)];
  radiusAxis.inverse ? radiusAxis.setExtent(parsedRadius[1], parsedRadius[0]) : radiusAxis.setExtent(parsedRadius[0], parsedRadius[1]);
}
function updatePolarScale(ecModel, api) {
  var polar = this;
  var angleAxis = polar.getAngleAxis();
  var radiusAxis = polar.getRadiusAxis();
  angleAxis.scale.setExtent(Infinity, -Infinity);
  radiusAxis.scale.setExtent(Infinity, -Infinity);
  ecModel.eachSeries(function(seriesModel) {
    if (seriesModel.coordinateSystem === polar) {
      var data_1 = seriesModel.getData();
      each(getDataDimensionsOnAxis(data_1, "radius"), function(dim) {
        radiusAxis.scale.unionExtentFromData(data_1, dim);
      });
      each(getDataDimensionsOnAxis(data_1, "angle"), function(dim) {
        angleAxis.scale.unionExtentFromData(data_1, dim);
      });
    }
  });
  niceScaleExtent(angleAxis.scale, angleAxis.model);
  niceScaleExtent(radiusAxis.scale, radiusAxis.model);
  if (angleAxis.type === "category" && !angleAxis.onBand) {
    var extent = angleAxis.getExtent();
    var diff = 360 / angleAxis.scale.count();
    angleAxis.inverse ? extent[1] += diff : extent[1] -= diff;
    angleAxis.setExtent(extent[0], extent[1]);
  }
}
function isAngleAxisModel(axisModel) {
  return axisModel.mainType === "angleAxis";
}
function setAxis(axis, axisModel) {
  var _a;
  axis.type = axisModel.get("type");
  axis.scale = createScaleByModel(axisModel);
  axis.onBand = axisModel.get("boundaryGap") && axis.type === "category";
  axis.inverse = axisModel.get("inverse");
  if (isAngleAxisModel(axisModel)) {
    axis.inverse = axis.inverse !== axisModel.get("clockwise");
    var startAngle = axisModel.get("startAngle");
    var endAngle = (_a = axisModel.get("endAngle")) !== null && _a !== void 0 ? _a : startAngle + (axis.inverse ? -360 : 360);
    axis.setExtent(startAngle, endAngle);
  }
  axisModel.axis = axis;
  axis.model = axisModel;
}
var polarCreator = {
  dimensions: polarDimensions,
  create: function(ecModel, api) {
    var polarList = [];
    ecModel.eachComponent("polar", function(polarModel, idx) {
      var polar = new Polar(idx + "");
      polar.update = updatePolarScale;
      var radiusAxis = polar.getRadiusAxis();
      var angleAxis = polar.getAngleAxis();
      var radiusAxisModel = polarModel.findAxisModel("radiusAxis");
      var angleAxisModel = polarModel.findAxisModel("angleAxis");
      setAxis(radiusAxis, radiusAxisModel);
      setAxis(angleAxis, angleAxisModel);
      resizePolar(polar, polarModel, api);
      polarList.push(polar);
      polarModel.coordinateSystem = polar;
      polar.model = polarModel;
    });
    ecModel.eachSeries(function(seriesModel) {
      if (seriesModel.get("coordinateSystem") === "polar") {
        var polarModel = seriesModel.getReferringComponents("polar", SINGLE_REFERRING).models[0];
        if (process.env.NODE_ENV !== "production") {
          if (!polarModel) {
            throw new Error('Polar "' + retrieve(seriesModel.get("polarIndex"), seriesModel.get("polarId"), 0) + '" not found');
          }
        }
        seriesModel.coordinateSystem = polarModel.coordinateSystem;
      }
    });
    return polarList;
  }
};
export {
  polarCreator as default
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Axis from "../Axis.js";
var ParallelAxis = (
  /** @class */
  function(_super) {
    __extends(ParallelAxis2, _super);
    function ParallelAxis2(dim, scale, coordExtent, axisType, axisIndex) {
      var _this = _super.call(this, dim, scale, coordExtent) || this;
      _this.type = axisType || "value";
      _this.axisIndex = axisIndex;
      return _this;
    }
    ParallelAxis2.prototype.isHorizontal = function() {
      return this.coordinateSystem.getModel().get("layout") !== "horizontal";
    };
    return ParallelAxis2;
  }(Axis)
);
export {
  ParallelAxis as default
};

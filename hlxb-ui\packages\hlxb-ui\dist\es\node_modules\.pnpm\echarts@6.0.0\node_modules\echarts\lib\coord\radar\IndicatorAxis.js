import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Axis from "../Axis.js";
var IndicatorAxis = (
  /** @class */
  function(_super) {
    __extends(IndicatorAxis2, _super);
    function IndicatorAxis2(dim, scale, radiusExtent) {
      var _this = _super.call(this, dim, scale, radiusExtent) || this;
      _this.type = "value";
      _this.angle = 0;
      _this.name = "";
      return _this;
    }
    return IndicatorAxis2;
  }(Axis)
);
export {
  IndicatorAxis as default
};

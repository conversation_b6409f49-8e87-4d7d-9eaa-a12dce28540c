"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const barGrid = require("../../layout/barGrid.js");
const dataSample = require("../../processor/dataSample.js");
const BarSeries = require("./BarSeries.js");
const BarView = require("./BarView.js");
function install(registers) {
  registers.registerChartView(BarView.default);
  registers.registerSeriesModel(BarSeries.default);
  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, util.curry(barGrid.layout, "bar"));
  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, barGrid.createProgressiveLayout("bar"));
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample.default("bar"));
  registers.registerAction({
    type: "changeAxisOrder",
    event: "changeAxisOrder",
    update: "update"
  }, function(payload, ecModel) {
    var componentType = payload.componentType || "series";
    ecModel.eachComponent({
      mainType: componentType,
      query: payload
    }, function(componentModel) {
      if (payload.sortInfo) {
        componentModel.axis.setCategorySortInfo(payload.sortInfo);
      }
    });
  });
}
exports.install = install;

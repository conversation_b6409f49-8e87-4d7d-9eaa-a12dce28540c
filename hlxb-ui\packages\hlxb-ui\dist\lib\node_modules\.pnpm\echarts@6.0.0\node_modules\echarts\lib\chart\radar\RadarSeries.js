"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const Series = require("../../model/Series.js");
const createSeriesDataSimply = require("../helper/createSeriesDataSimply.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const LegendVisualProvider = require("../../visual/LegendVisualProvider.js");
const tooltipMarkup = require("../../component/tooltip/tooltipMarkup.js");
var RadarSeriesModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(RadarSeriesModel2, _super);
    function RadarSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = RadarSeriesModel2.type;
      _this.hasSymbolVisual = true;
      return _this;
    }
    RadarSeriesModel2.prototype.init = function(option) {
      _super.prototype.init.apply(this, arguments);
      this.legendVisualProvider = new LegendVisualProvider.default(util.bind(this.getData, this), util.bind(this.getRawData, this));
    };
    RadarSeriesModel2.prototype.getInitialData = function(option, ecModel) {
      return createSeriesDataSimply.default(this, {
        generateCoord: "indicator_",
        generateCoordCount: Infinity
      });
    };
    RadarSeriesModel2.prototype.formatTooltip = function(dataIndex, multipleSeries, dataType) {
      var data = this.getData();
      var coordSys = this.coordinateSystem;
      var indicatorAxes = coordSys.getIndicatorAxes();
      var name = this.getData().getName(dataIndex);
      var nameToDisplay = name === "" ? this.name : name;
      var markerColor = tooltipMarkup.retrieveVisualColorForTooltipMarker(this, dataIndex);
      return tooltipMarkup.createTooltipMarkup("section", {
        header: nameToDisplay,
        sortBlocks: true,
        blocks: util.map(indicatorAxes, function(axis) {
          var val = data.get(data.mapDimension(axis.dim), dataIndex);
          return tooltipMarkup.createTooltipMarkup("nameValue", {
            markerType: "subItem",
            markerColor,
            name: axis.name,
            value: val,
            sortParam: val
          });
        })
      });
    };
    RadarSeriesModel2.prototype.getTooltipPosition = function(dataIndex) {
      if (dataIndex != null) {
        var data_1 = this.getData();
        var coordSys = this.coordinateSystem;
        var values = data_1.getValues(util.map(coordSys.dimensions, function(dim) {
          return data_1.mapDimension(dim);
        }), dataIndex);
        for (var i = 0, len = values.length; i < len; i++) {
          if (!isNaN(values[i])) {
            var indicatorAxes = coordSys.getIndicatorAxes();
            return coordSys.coordToPoint(indicatorAxes[i].dataToCoord(values[i]), i);
          }
        }
      }
    };
    RadarSeriesModel2.type = "series.radar";
    RadarSeriesModel2.dependencies = ["radar"];
    RadarSeriesModel2.defaultOption = {
      // zlevel: 0,
      z: 2,
      colorBy: "data",
      coordinateSystem: "radar",
      legendHoverLink: true,
      radarIndex: 0,
      lineStyle: {
        width: 2,
        type: "solid",
        join: "round"
      },
      label: {
        position: "top"
      },
      // areaStyle: {
      // },
      // itemStyle: {}
      symbolSize: 8
      // symbolRotate: null
    };
    return RadarSeriesModel2;
  }(Series.default)
);
exports.default = RadarSeriesModel;

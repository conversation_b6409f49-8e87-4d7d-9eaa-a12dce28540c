import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Axis from "../Axis.js";
var RadiusAxis = (
  /** @class */
  function(_super) {
    __extends(RadiusAxis2, _super);
    function RadiusAxis2(scale, radiusExtent) {
      return _super.call(this, "radius", scale, radiusExtent) || this;
    }
    RadiusAxis2.prototype.pointToData = function(point, clamp) {
      return this.polar.pointToData(point, clamp)[this.dim === "radius" ? 0 : 1];
    };
    return RadiusAxis2;
  }(Axis)
);
RadiusAxis.prototype.dataToRadius = Axis.prototype.dataToCoord;
RadiusAxis.prototype.radiusToData = Axis.prototype.coordToData;
export {
  RadiusAxis as default
};

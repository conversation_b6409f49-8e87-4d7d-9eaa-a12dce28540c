import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { mixin } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import ComponentModel from "../../model/Component.js";
import { AxisModelCommonMixin } from "../axisModelCommonMixin.js";
import { SINGLE_REFERRING } from "../../util/model.js";
var PolarAxisModel = (
  /** @class */
  function(_super) {
    __extends(PolarAxisModel2, _super);
    function PolarAxisModel2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    PolarAxisModel2.prototype.getCoordSysModel = function() {
      return this.getReferringComponents("polar", SINGLE_REFERRING).models[0];
    };
    PolarAxisModel2.type = "polarAxis";
    return PolarAxisModel2;
  }(ComponentModel)
);
mixin(PolarAxisModel, AxisModelCommonMixin);
var AngleAxisModel = (
  /** @class */
  function(_super) {
    __extends(AngleAxisModel2, _super);
    function AngleAxisModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = AngleAxisModel2.type;
      return _this;
    }
    AngleAxisModel2.type = "angleAxis";
    return AngleAxisModel2;
  }(PolarAxisModel)
);
var RadiusAxisModel = (
  /** @class */
  function(_super) {
    __extends(RadiusAxisModel2, _super);
    function RadiusAxisModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = RadiusAxisModel2.type;
      return _this;
    }
    RadiusAxisModel2.type = "radiusAxis";
    return RadiusAxisModel2;
  }(PolarAxisModel)
);
export {
  AngleAxisModel,
  PolarAxisModel,
  RadiusAxisModel
};

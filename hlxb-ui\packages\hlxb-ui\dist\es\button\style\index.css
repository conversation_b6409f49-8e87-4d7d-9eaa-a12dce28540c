.hlxb-button {
  /**  圆角  **/
  /**  size  **/
}
.hlxb-button.r-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px;
}
.hlxb-button.r-button + .r-button {
  margin-left: 10px;
}
.hlxb-button.r-button:focus,
.hlxb-button.r-button:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}
.hlxb-button.r-button:active {
  color: #3a8ee6;
  border-color: #3a8ee6;
  outline: 0;
}
.hlxb-button.r-button.is-disabled,
.hlxb-button.r-button.is-disabled:focus,
.hlxb-button.r-button.is-disabled:hover {
  color: #c0c4cc;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff;
  border-color: #ebeef5;
}
.hlxb-button.r-button--primary {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}
.hlxb-button.r-button--primary:focus,
.hlxb-button.r-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}
.hlxb-button.r-button--primary.is-disabled,
.hlxb-button.r-button--primary.is-disabled:active,
.hlxb-button.r-button--primary.is-disabled:focus,
.hlxb-button.r-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #a0cfff;
  border-color: #a0cfff;
}
.hlxb-button.r-button--success {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}
.hlxb-button.r-button--success:focus,
.hlxb-button.r-button--success:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}
.hlxb-button.r-button--success.is-active,
.hlxb-button.r-button--success:active {
  background: #5daf34;
  border-color: #5daf34;
  color: #fff;
}
.hlxb-button.r-button--success:active {
  outline: 0;
}
.hlxb-button.r-button--success.is-disabled,
.hlxb-button.r-button--success.is-disabled:active,
.hlxb-button.r-button--success.is-disabled:focus,
.hlxb-button.r-button--success.is-disabled:hover {
  color: #fff;
  background-color: #b3e19d;
  border-color: #b3e19d;
}
.hlxb-button.r-button--warning {
  color: #fff;
  background-color: #e6a23c;
  border-color: #e6a23c;
}
.hlxb-button.r-button--warning:focus,
.hlxb-button.r-button--warning:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}
.hlxb-button.r-button--warning.is-active,
.hlxb-button.r-button--warning:active {
  background: #cf9236;
  border-color: #cf9236;
  color: #fff;
}
.hlxb-button.r-button--warning:active {
  outline: 0;
}
.hlxb-button.r-button--warning.is-disabled,
.hlxb-button.r-button--warning.is-disabled:active,
.hlxb-button.r-button--warning.is-disabled:focus,
.hlxb-button.r-button--warning.is-disabled:hover {
  color: #fff;
  background-color: #f3d19e;
  border-color: #f3d19e;
}
.hlxb-button.r-button--danger {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
.hlxb-button.r-button--danger:focus,
.hlxb-button.r-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}
.hlxb-button.r-button--danger.is-active,
.hlxb-button.r-button--danger:active {
  background: #dd6161;
  border-color: #dd6161;
  color: #fff;
}
.hlxb-button.r-button--danger:active {
  outline: 0;
}
.hlxb-button.r-button--danger.is-disabled,
.hlxb-button.r-button--danger.is-disabled:active,
.hlxb-button.r-button--danger.is-disabled:focus,
.hlxb-button.r-button--danger.is-disabled:hover {
  color: #fff;
  background-color: #fab6b6;
  border-color: #fab6b6;
}
.hlxb-button.r-button--info {
  color: #fff;
  background-color: #909399;
  border-color: #909399;
}
.hlxb-button.r-button--info:focus,
.hlxb-button.r-button--info:hover {
  background: #a6a9ad;
  border-color: #a6a9ad;
  color: #fff;
}
.hlxb-button.r-button--info.is-active,
.hlxb-button.r-button--info:active {
  background: #82848a;
  border-color: #82848a;
  color: #fff;
}
.hlxb-button.r-button--info:active {
  outline: 0;
}
.hlxb-button.r-button--info.is-disabled,
.hlxb-button.r-button--info.is-disabled:active,
.hlxb-button.r-button--info.is-disabled:focus,
.hlxb-button.r-button--info.is-disabled:hover {
  color: #fff;
  background-color: #c8c9cc;
  border-color: #c8c9cc;
}
.hlxb-button.r-button.is-round {
  border-radius: 20px;
  padding: 12px 23px;
}
.hlxb-button.r-button--medium {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}
.hlxb-button.r-button--mini,
.hlxb-button.r-button--small {
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button--small {
  padding: 9px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button--mini {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button::-moz-focus-inner {
  border: 0;
}

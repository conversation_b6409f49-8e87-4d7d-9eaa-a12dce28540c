"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const text = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/contain/text.js");
function getSectorCornerRadius(model, shape, zeroIfNull) {
  var cornerRadius = model.get("borderRadius");
  if (cornerRadius == null) {
    return zeroIfNull ? {
      cornerRadius: 0
    } : null;
  }
  if (!util.isArray(cornerRadius)) {
    cornerRadius = [cornerRadius, cornerRadius, cornerRadius, cornerRadius];
  }
  var dr = Math.abs(shape.r || 0 - shape.r0 || 0);
  return {
    cornerRadius: util.map(cornerRadius, function(cr) {
      return text.parsePercent(cr, dr);
    })
  };
}
exports.getSectorCornerRadius = getSectorCornerRadius;

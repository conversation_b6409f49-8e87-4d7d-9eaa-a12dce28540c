import { __extends } from "../../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { registerAction } from "../../../core/echarts.js";
import { clear } from "../../dataZoom/history.js";
import { ToolboxFeature } from "../featureManager.js";
var RestoreOption = (
  /** @class */
  function(_super) {
    __extends(RestoreOption2, _super);
    function RestoreOption2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    RestoreOption2.prototype.onclick = function(ecModel, api) {
      clear(ecModel);
      api.dispatchAction({
        type: "restore",
        from: this.uid
      });
    };
    RestoreOption2.getDefaultOption = function(ecModel) {
      var defaultOption = {
        show: true,
        // eslint-disable-next-line
        icon: "M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",
        title: ecModel.getLocaleModel().get(["toolbox", "restore", "title"])
      };
      return defaultOption;
    };
    return RestoreOption2;
  }(ToolboxFeature)
);
registerAction({
  type: "restore",
  event: "restore",
  update: "prepareAndUpdate"
}, function(payload, ecModel) {
  ecModel.resetOption("recreate");
});
export {
  RestoreOption as default
};

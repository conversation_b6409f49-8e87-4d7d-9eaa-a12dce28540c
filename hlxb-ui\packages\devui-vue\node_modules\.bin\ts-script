#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/dist/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/dist/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/dist/bin-script-deprecated.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/ts-node@10.9.2_@types+node@16.18.126_typescript@4.9.5/node_modules/ts-node/dist/bin-script-deprecated.js" "$@"
fi

import dataZoomProcessor from "./dataZoomProcessor.js";
import installDataZoomAction from "./dataZoomAction.js";
var installed = false;
function installCommon(registers) {
  if (installed) {
    return;
  }
  installed = true;
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.FILTER, dataZoomProcessor);
  installDataZoomAction(registers);
  registers.registerSubTypeDefaulter("dataZoom", function() {
    return "slider";
  });
}
export {
  installCommon as default
};

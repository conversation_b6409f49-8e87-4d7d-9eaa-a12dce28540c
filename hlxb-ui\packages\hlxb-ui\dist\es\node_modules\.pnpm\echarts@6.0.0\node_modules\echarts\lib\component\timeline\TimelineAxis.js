import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Axis from "../../coord/Axis.js";
var TimelineAxis = (
  /** @class */
  function(_super) {
    __extends(TimelineAxis2, _super);
    function TimelineAxis2(dim, scale, coordExtent, axisType) {
      var _this = _super.call(this, dim, scale, coordExtent) || this;
      _this.type = axisType || "value";
      return _this;
    }
    TimelineAxis2.prototype.getLabelModel = function() {
      return this.model.getModel("label");
    };
    TimelineAxis2.prototype.isHorizontal = function() {
      return this.model.get("orient") === "horizontal";
    };
    return TimelineAxis2;
  }(Axis)
);
export {
  TimelineAxis as default
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const checkboxGroup_vue_vue_type_script_setup_true_lang = require("./checkboxGroup.vue2.js");
require("./checkboxGroup.vue3.js");
const _pluginVue_exportHelper = require("../_virtual/_plugin-vue_export-helper.js");
const checkboxGroup = /* @__PURE__ */ _pluginVue_exportHelper.default(checkboxGroup_vue_vue_type_script_setup_true_lang.default, [["__scopeId", "data-v-90bc5d50"]]);
exports.default = checkboxGroup;

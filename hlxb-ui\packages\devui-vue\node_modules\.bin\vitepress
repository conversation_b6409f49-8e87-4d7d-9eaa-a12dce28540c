#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules/vitepress/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules/vitepress/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules/vitepress/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules/vitepress/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress@0.20.1_less@4.4.1_8da61e2262998a292ff0ba8c8be76e97/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitepress/bin/vitepress.js" "$@"
else
  exec node  "$basedir/../vitepress/bin/vitepress.js" "$@"
fi

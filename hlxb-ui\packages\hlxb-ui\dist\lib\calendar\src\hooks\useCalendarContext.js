"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const index = require("../../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js");
const lodashEs = require("lodash-es");
const vue = require("vue");
const useProvideCalendarContext = (props) => {
  var _a;
  const isExpanded = vue.ref(false);
  const currentDate = vue.ref(((_a = props.selectedDate) == null ? void 0 : _a.startOf("month")) || index.default());
  const options = vue.computed(() => {
    return lodashEs.merge(
      // 默认配置
      {
        showToday: true,
        selectable: true,
        wheelMonthChange: true,
        showExpandButton: true,
        fillPrevNextMonth: true,
        autoCollapseAfterDateSelect: true,
        autoExpandAfterMonthChange: true,
        wheelSensitivity: 150,
        cellClass: () => "",
        cellContent: () => "",
        hasIndicator: () => false,
        indicatorConfig: {
          color: "#fc7c22",
          size: 4,
          position: "bottom-center",
          style: {}
        }
      },
      props.options || {}
    );
  });
  const calendarContext = vue.computed(() => ({
    options: options.value,
    isExpanded: isExpanded.value,
    currentDate: currentDate.value,
    selectedDate: props.selectedDate || index.default()
  }));
  vue.provide("calendarContext", calendarContext);
  return {
    options,
    isExpanded,
    currentDate,
    calendarContext
  };
};
const useCalendarContext = () => {
  const calendarContext = vue.inject("calendarContext");
  if (!calendarContext) {
    throw new Error("请在HCalendar子组件中使用useCalendarContext");
  }
  const options = vue.computed(() => {
    var _a;
    return (_a = calendarContext.value) == null ? void 0 : _a.options;
  });
  return {
    calendarContext,
    options
  };
};
exports.useCalendarContext = useCalendarContext;
exports.useProvideCalendarContext = useProvideCalendarContext;

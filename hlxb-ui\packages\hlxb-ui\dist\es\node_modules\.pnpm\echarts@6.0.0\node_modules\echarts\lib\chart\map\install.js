import { use } from "../../extension.js";
import MapView from "./MapView.js";
import MapSeries from "./MapSeries.js";
import mapDataStatistic from "./mapDataStatistic.js";
import mapSymbolLayout from "./mapSymbolLayout.js";
import { createLegacyDataSelectAction } from "../../legacy/dataSelectAction.js";
import { install as install$1 } from "../../component/geo/install.js";
function install(registers) {
  use(install$1);
  registers.registerChartView(MapView);
  registers.registerSeriesModel(MapSeries);
  registers.registerLayout(mapSymbolLayout);
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, mapDataStatistic);
  createLegacyDataSelectAction("map", registers.registerAction);
}
export {
  install
};

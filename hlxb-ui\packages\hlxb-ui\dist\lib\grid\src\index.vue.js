"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../../config/index.js");
const index$1 = require("../../empty/index.js");
const _hoisted_1 = ["onClick"];
const _hoisted_2 = { class: "nav-icon-container" };
const _hoisted_3 = ["src", "alt"];
const _hoisted_4 = { class: "grid-item-text text-14px mt-12px truncate max-w-[90%]" };
const _hoisted_5 = {
  key: 0,
  class: "w-full h-full flex justify-center items-center"
};
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbGrid"
  },
  __name: "index",
  props: {
    data: { default: () => [] },
    loading: { type: Boolean, default: false },
    columns: { default: 6 },
    itemHeight: { default: "86px" },
    gap: { default: "8px" },
    emptyText: { default: "暂无数据" },
    hoverShow: { type: Boolean, default: true },
    containerClass: {},
    containerStyle: {},
    itemClass: {},
    itemStyle: {}
  },
  emits: ["item-click"],
  setup(__props, { emit: __emit }) {
    const prefixCls = index.getPrefixCls("grid");
    const props = __props;
    const emit = __emit;
    const isEmpty = vue.computed(() => {
      var _a;
      return !((_a = props.data) == null ? void 0 : _a.length) && !props.loading;
    });
    const handleItemClick = (item, index2) => {
      emit("item-click", item, index2);
    };
    return (_ctx, _cache) => {
      const _directive_loading = vue.resolveDirective("loading");
      return vue.withDirectives((vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([
          vue.unref(prefixCls),
          "flex flex-wrap px-[16px] relative",
          _ctx.containerClass,
          { "h-full": isEmpty.value || _ctx.loading }
        ]),
        style: vue.normalizeStyle(_ctx.containerStyle)
      }, [
        (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(_ctx.data, (item, index2) => {
          return vue.openBlock(), vue.createElementBlock("div", {
            class: vue.normalizeClass([
              "grid-item",
              "flex flex-col justify-center items-center cursor-pointer",
              { "hover-show": _ctx.hoverShow },
              _ctx.itemClass
            ]),
            style: vue.normalizeStyle([
              _ctx.itemStyle,
              {
                height: _ctx.itemHeight,
                marginBottom: _ctx.gap,
                width: `${100 / _ctx.columns}%`
              }
            ]),
            key: index2,
            onClick: ($event) => handleItemClick(item, index2)
          }, [
            vue.renderSlot(_ctx.$slots, "item", {
              item,
              index: index2
            }, () => [
              vue.createElementVNode("div", _hoisted_2, [
                vue.renderSlot(_ctx.$slots, "icon", {
                  item,
                  index: index2
                }, () => [
                  vue.createElementVNode("img", {
                    class: "grid-item-icon",
                    style: { "width": "44px", "height": "44px" },
                    src: item.icon,
                    alt: item.name
                  }, null, 8, _hoisted_3)
                ])
              ]),
              vue.createElementVNode("span", _hoisted_4, [
                vue.renderSlot(_ctx.$slots, "title", {
                  item,
                  index: index2
                }, () => [
                  vue.createTextVNode(vue.toDisplayString(item.name), 1)
                ])
              ])
            ])
          ], 14, _hoisted_1);
        }), 128)),
        isEmpty.value ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_5, [
          vue.renderSlot(_ctx.$slots, "empty", {}, () => [
            vue.createVNode(vue.unref(index$1.default), { description: _ctx.emptyText }, null, 8, ["description"])
          ])
        ])) : vue.createCommentVNode("", true)
      ], 6)), [
        [_directive_loading, _ctx.loading]
      ]);
    };
  }
});
exports.default = _sfc_main;

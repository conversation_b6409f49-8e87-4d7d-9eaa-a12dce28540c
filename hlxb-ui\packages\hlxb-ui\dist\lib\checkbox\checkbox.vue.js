"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const checkbox_vue_vue_type_script_setup_true_lang = require("./checkbox.vue2.js");
;/* empty css               */
const _pluginVue_exportHelper = require("../_virtual/_plugin-vue_export-helper.js");
const checkbox = /* @__PURE__ */ _pluginVue_exportHelper.default(checkbox_vue_vue_type_script_setup_true_lang.default, [["__scopeId", "data-v-eefd8a13"]]);
exports.default = checkbox;

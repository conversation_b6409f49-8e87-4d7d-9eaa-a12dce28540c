import { config } from "./configReadonly.js";
import { createMultiply } from "../function/arithmetic/multiply.js";
import { createBignumber } from "../type/bignumber/function/bignumber.js";
import { createTyped } from "../core/function/typed.js";
import { createMultiplyScalar } from "../function/arithmetic/multiplyScalar.js";
import { createMatrix } from "../type/matrix/function/matrix.js";
import { createEqualScalar } from "../function/relational/equalScalar.js";
import { createDot } from "../function/matrix/dot.js";
import { createAddScalar } from "../function/arithmetic/addScalar.js";
import { createBigNumberClass } from "../type/bignumber/BigNumber.js";
import { createFractionClass } from "../type/fraction/Fraction.js";
import { createDenseMatrixClass } from "../type/matrix/DenseMatrix.js";
import { createComplexClass } from "../type/complex/Complex.js";
import { createSparseMatrixClass } from "../type/matrix/SparseMatrix.js";
import { createMatrixClass } from "../type/matrix/Matrix.js";
import { createSize } from "../function/matrix/size.js";
import { createConj } from "../function/complex/conj.js";
var BigNumber = /* @__PURE__ */ createBigNumberClass({
  config
});
var Complex = /* @__PURE__ */ createComplexClass({});
var Fraction = /* @__PURE__ */ createFractionClass({});
var Matrix = /* @__PURE__ */ createMatrixClass({});
var DenseMatrix = /* @__PURE__ */ createDenseMatrixClass({
  Matrix
});
var typed = /* @__PURE__ */ createTyped({
  BigNumber,
  Complex,
  DenseMatrix,
  Fraction
});
var addScalar = /* @__PURE__ */ createAddScalar({
  typed
});
var conj = /* @__PURE__ */ createConj({
  typed
});
var equalScalar = /* @__PURE__ */ createEqualScalar({
  config,
  typed
});
var multiplyScalar = /* @__PURE__ */ createMultiplyScalar({
  typed
});
var SparseMatrix = /* @__PURE__ */ createSparseMatrixClass({
  Matrix,
  equalScalar,
  typed
});
var bignumber = /* @__PURE__ */ createBignumber({
  BigNumber,
  typed
});
var matrix = /* @__PURE__ */ createMatrix({
  DenseMatrix,
  Matrix,
  SparseMatrix,
  typed
});
var size = /* @__PURE__ */ createSize({
  matrix,
  config,
  typed
});
var dot = /* @__PURE__ */ createDot({
  addScalar,
  conj,
  multiplyScalar,
  size,
  typed
});
var multiply = /* @__PURE__ */ createMultiply({
  addScalar,
  dot,
  equalScalar,
  matrix,
  multiplyScalar,
  typed
});
export {
  BigNumber,
  Complex,
  DenseMatrix,
  Fraction,
  Matrix,
  SparseMatrix,
  addScalar,
  bignumber,
  conj,
  dot,
  equalScalar,
  matrix,
  multiply,
  multiplyScalar,
  size,
  typed
};

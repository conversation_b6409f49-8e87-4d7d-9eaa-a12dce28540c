"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const active_r = require("../assets/images/active_r.png.js");
const index = require("../../../config/index.js");
const _hoisted_1 = { class: "item-list" };
const _hoisted_2 = ["onClick"];
const _hoisted_3 = { class: "top-title" };
const _hoisted_4 = { class: "item-index" };
const _hoisted_5 = { class: "num-index" };
const _hoisted_6 = { class: "label" };
const _hoisted_7 = { class: "item-content" };
const _hoisted_8 = { class: "value" };
const _hoisted_9 = { class: "unit" };
const _hoisted_10 = ["src"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbRanking"
  },
  __name: "Ranking",
  props: {
    className: {
      type: String,
      default: "ranking"
    },
    dataList: {
      type: Array,
      default: () => []
    },
    // 是否点击联动
    clickFlags: {
      type: Boolean,
      default: true
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  emits: ["setItem"],
  setup(__props, { emit: __emit }) {
    const prefixCls = index.getPrefixCls("ranking");
    const props = __props;
    const setIndex = vue.ref(null);
    const itemVal = vue.ref("");
    const emit = __emit;
    function setItem(item, index2) {
      console.log("item", item);
      if (props.clickFlags) {
        itemVal.value = item.tag;
        setIndex.value = index2;
        emit("setItem", { value: itemVal.value });
      }
    }
    function getWidth(index2) {
      const lengthN = props.dataList.length;
      if (lengthN > 0) {
        const setupN = Math.floor(100 / lengthN);
        const widthN = Math.floor(setupN * (lengthN - index2));
        return `${widthN}%`;
      } else {
        return "100%";
      }
    }
    async function getTimeList() {
    }
    vue.watch(
      () => props.dataList,
      async (val) => {
        setIndex.value = null;
        console.log("ranking123", val);
      }
    );
    getTimeList();
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        vue.createElementVNode("div", _hoisted_1, [
          (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(props.dataList, (item, index2) => {
            return vue.openBlock(), vue.createElementBlock("div", {
              key: index2,
              onClick: ($event) => __props.clickFlags ? setItem(item, index2) : "",
              class: vue.normalizeClass({
                item: true,
                activeItem: __props.clickFlags && index2 === setIndex.value,
                hoverItem: __props.clickFlags
              })
            }, [
              vue.createElementVNode("div", _hoisted_3, [
                vue.createElementVNode("div", _hoisted_4, [
                  vue.createElementVNode("div", _hoisted_5, vue.toDisplayString(index2 + 1), 1),
                  vue.createElementVNode("div", _hoisted_6, vue.toDisplayString(item.indexName), 1)
                ]),
                vue.createElementVNode("div", _hoisted_7, [
                  vue.createElementVNode("div", _hoisted_8, vue.toDisplayString(item.value || "-"), 1),
                  vue.createElementVNode("div", _hoisted_9, vue.toDisplayString(item.unitName), 1)
                ])
              ]),
              vue.createElementVNode("div", {
                class: vue.normalizeClass(["bar-box", index2 < 3 ? "abar-box" : ""])
              }, [
                vue.createElementVNode("div", {
                  style: vue.normalizeStyle({ width: getWidth(index2) }),
                  class: vue.normalizeClass(["bar-conter", index2 < 3 ? "abar-conter" : ""])
                }, null, 6)
              ], 2),
              __props.clickFlags && index2 === setIndex.value ? (vue.openBlock(), vue.createElementBlock("img", {
                key: 0,
                class: "img_box",
                src: vue.unref(active_r.default)
              }, null, 8, _hoisted_10)) : vue.createCommentVNode("", true)
            ], 10, _hoisted_2);
          }), 128))
        ])
      ], 2);
    };
  }
});
exports.default = _sfc_main;

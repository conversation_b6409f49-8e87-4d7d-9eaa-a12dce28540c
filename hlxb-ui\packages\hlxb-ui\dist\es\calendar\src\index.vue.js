import { defineComponent, ref, computed, watch, createElementBlock, openBlock, normalizeClass, unref, createVNode, withCtx, renderSlot } from "vue";
import dayjs from "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js";
import { prefixCls } from "./hooks/prefixCls.js";
import _sfc_main$2 from "./components/DateGrid.vue.js";
/* empty css                         */
import { generateFullMonthDates } from "./hooks/utils.js";
import _sfc_main$3 from "./components/ExpandButton.vue.js";
/* empty css                             */
import _sfc_main$1 from "./components/WeekdaysHeader.vue.js";
/* empty css                               */
import { useProvideCalendarContext } from "./hooks/useCalendarContext.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCalendar"
  },
  __name: "index",
  props: {
    selectedDate: { default: () => dayjs() },
    options: { default: () => ({}) }
  },
  emits: ["update:selectedDate", "date-click", "month-change", "expand-change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const fullMonthDates = ref([]);
    let wheelDelta = 0;
    const { options, currentDate, isExpanded } = useProvideCalendarContext(props);
    const generateFullMonthDatesWrapper = () => {
      fullMonthDates.value = generateFullMonthDates(
        currentDate.value,
        options.value.fillPrevNextMonth
      );
    };
    const displayedDates = computed(() => {
      if (!isExpanded.value) {
        const selected = props.selectedDate;
        const selectedWeekStart = selected.startOf("week");
        const selectedWeekEnd = selected.endOf("week");
        const year = currentDate.value.year();
        const month = currentDate.value.month();
        const lastDay = dayjs().year(year).month(month).endOf("month");
        const lastWeekStart = lastDay.startOf("week");
        const isInLastWeek = selected.isSame(lastWeekStart, "week") || selected.isAfter(lastWeekStart);
        let startDate, endDate;
        if (isInLastWeek) {
          startDate = selectedWeekStart.subtract(1, "week");
          endDate = selectedWeekEnd;
        } else {
          startDate = selectedWeekStart;
          endDate = selectedWeekEnd.add(1, "week");
        }
        return fullMonthDates.value.filter((dateInfo) => {
          const date = dateInfo.date;
          return (date.isAfter(startDate) || date.isSame(startDate)) && (date.isBefore(endDate) || date.isSame(endDate));
        });
      } else {
        return fullMonthDates.value;
      }
    });
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
      emit("expand-change", isExpanded.value);
      if (!isExpanded.value) {
        const selectedMonth = props.selectedDate.month();
        const currentMonth = currentDate.value.month();
        if (selectedMonth !== currentMonth) {
          currentDate.value = props.selectedDate.startOf("month");
          generateFullMonthDatesWrapper();
        }
      }
    };
    const handleDateClick = (date, dateInfo) => {
      if (!options.value.selectable) return;
      const clickedMonth = date.month();
      const currentMonth = currentDate.value.month();
      emit("update:selectedDate", date);
      emit("date-click", date, dateInfo);
      if (clickedMonth !== currentMonth) {
        currentDate.value = date.startOf("month");
        generateFullMonthDatesWrapper();
        emit("month-change", currentDate.value);
      }
      if (options.value.autoCollapseAfterDateSelect !== false) {
        isExpanded.value = false;
      }
    };
    const previousMonth = () => {
      currentDate.value = currentDate.value.subtract(1, "month");
      if (!isExpanded.value && options.value.autoExpandAfterMonthChange !== false) {
        isExpanded.value = true;
      }
      generateFullMonthDatesWrapper();
      emit("month-change", currentDate.value);
      wheelDelta = 0;
    };
    const nextMonth = () => {
      currentDate.value = currentDate.value.add(1, "month");
      if (!isExpanded.value && options.value.autoExpandAfterMonthChange !== false) {
        isExpanded.value = true;
      }
      generateFullMonthDatesWrapper();
      emit("month-change", currentDate.value);
      wheelDelta = 0;
    };
    const handleWheel = (event) => {
      if (!isExpanded.value || !options.value.wheelMonthChange) return;
      event.preventDefault();
      const threshold = Math.max(0, options.value.wheelSensitivity);
      wheelDelta += event.deltaY;
      if (Math.abs(wheelDelta) >= threshold) {
        if (wheelDelta > 0) {
          nextMonth();
        } else {
          previousMonth();
        }
        wheelDelta = 0;
      }
    };
    const goToToday = () => {
      const today = dayjs();
      handleDateClick(today, {
        date: today,
        isCurrentMonth: true,
        isToday: true,
        dateString: today.format("YYYY-MM-DD")
      });
    };
    watch(
      () => props.selectedDate,
      (newDate) => {
        const newMonth = newDate.month();
        const currentMonth = currentDate.value.month();
        if (newMonth !== currentMonth) {
          currentDate.value = newDate.startOf("month");
          generateFullMonthDatesWrapper();
        }
      },
      { immediate: true }
    );
    generateFullMonthDatesWrapper();
    __expose({
      goToToday,
      nextMonth,
      previousMonth,
      fullMonthDates,
      currentDate: () => currentDate.value,
      isExpanded: () => isExpanded.value
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(prefixCls)),
        onWheel: handleWheel
      }, [
        createVNode(_sfc_main$1),
        createVNode(_sfc_main$2, {
          displayedDates: displayedDates.value,
          onDateClick: handleDateClick
        }, {
          "date-cell": withCtx(({ dateItem }) => [
            renderSlot(_ctx.$slots, "date-cell", { dateItem })
          ]),
          _: 3
        }, 8, ["displayedDates"]),
        createVNode(_sfc_main$3, { onToggleExpand: toggleExpand })
      ], 34);
    };
  }
});
export {
  _sfc_main as default
};

import { defineComponent, ref, useAttrs, computed, watch, onMounted, resolveDirective, withDirectives, createElementBlock, openBlock, normalizeStyle, normalizeClass, unref, createCommentVNode, createBlock, createVNode, mergeProps, withCtx, renderSlot, Fragment, renderList, nextTick } from "vue";
/* empty css                                                                     */
import { merge } from "lodash-es";
import HlxbEmpty from "../../empty/index.js";
import { Swiper, SwiperSlide } from "../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/swiper-vue.js";
import { moduleConfigMap, loadSwiperModules } from "./utils.js";
import { getPrefixCls } from "../../config/index.js";
const _hoisted_1 = {
  key: 0,
  class: "h-full w-full flex justify-center items-center"
};
const _hoisted_2 = ["src", "alt"];
const _hoisted_3 = {
  key: 1,
  class: "shadow"
};
const _hoisted_4 = {
  key: 0,
  class: "swiper-pagination"
};
const _hoisted_5 = {
  key: 0,
  class: "swiper-button-prev"
};
const _hoisted_6 = {
  key: 1,
  class: "swiper-button-next"
};
const _hoisted_7 = {
  key: 0,
  class: "swiper-scrollbar"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbSwiper"
  },
  __name: "index",
  props: {
    modules: { default: () => ["Autoplay", "Pagination"] },
    data: { default: () => [] },
    height: { default: "263px" },
    swiperClass: {},
    slideStyle: { default: () => ({ cursor: "grab" }) },
    showShadow: { type: Boolean, default: false },
    options: { default: () => ({}) },
    emptyText: { default: "暂无轮播图" },
    loading: { type: Boolean, default: false },
    isEmpty: { type: Boolean, default: false }
  },
  emits: ["slide-click"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const prefixCls = getPrefixCls("swiper");
    const props = __props;
    const emit = __emit;
    const swiperRef = ref();
    const attrs = useAttrs();
    const isReady = ref(false);
    const loadedModules = ref([]);
    const showPagination = computed(() => props.modules.includes("Pagination"));
    const showNavigation = computed(() => props.modules.includes("Navigation"));
    const showScrollbar = computed(() => props.modules.includes("Scrollbar"));
    const swiperBind = computed(() => {
      const { modules, options } = props;
      const config = {
        ...attrs,
        loop: true,
        modules: loadedModules.value
      };
      modules.forEach((moduleName) => {
        const configItem = moduleConfigMap[moduleName];
        configItem && merge(config, configItem);
      });
      merge(config, options);
      return config;
    });
    const loadModulesAndStyles = async (modules = []) => {
      try {
        isReady.value = false;
        loadedModules.value = [];
        const swiperModules = await loadSwiperModules(modules);
        loadedModules.value = swiperModules;
        await nextTick();
        isReady.value = true;
      } catch (error) {
        console.error("加载 Swiper 模块时出错:", error);
        isReady.value = true;
      }
    };
    watch(
      () => props.modules,
      (newModules) => {
        if (newModules == null ? void 0 : newModules.length) {
          loadModulesAndStyles(newModules);
        }
      },
      { immediate: true }
    );
    onMounted(() => {
      var _a;
      if (!((_a = props.modules) == null ? void 0 : _a.length)) {
        isReady.value = true;
      }
    });
    __expose({
      swiperRef
    });
    return (_ctx, _cache) => {
      const _directive_loading = resolveDirective("loading");
      return withDirectives((openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), "w-full"]),
        style: normalizeStyle({ height: _ctx.height })
      }, [
        !_ctx.loading && _ctx.isEmpty ? (openBlock(), createElementBlock("div", _hoisted_1, [
          createVNode(unref(HlxbEmpty), { description: _ctx.emptyText }, null, 8, ["description"])
        ])) : createCommentVNode("", true),
        isReady.value && !_ctx.loading && !_ctx.isEmpty ? (openBlock(), createBlock(unref(Swiper), mergeProps({ key: 1 }, swiperBind.value, {
          ref_key: "swiperRef",
          ref: swiperRef,
          class: ["swiper", _ctx.swiperClass]
        }), {
          default: withCtx(() => [
            renderSlot(_ctx.$slots, "default", {}, () => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.data, (item, index) => {
                return openBlock(), createBlock(unref(SwiperSlide), {
                  key: index,
                  "data-swiper-autoplay": item.duration || 3e3,
                  style: normalizeStyle(_ctx.slideStyle),
                  onClick: ($event) => emit("slide-click", item, index)
                }, {
                  default: withCtx(() => [
                    renderSlot(_ctx.$slots, "slide", {
                      item,
                      index
                    }, () => [
                      item.imageUrl ? (openBlock(), createElementBlock("img", {
                        key: 0,
                        src: item.imageUrl,
                        alt: item.alt
                      }, null, 8, _hoisted_2)) : createCommentVNode("", true),
                      _ctx.showShadow ? (openBlock(), createElementBlock("div", _hoisted_3)) : createCommentVNode("", true)
                    ])
                  ]),
                  _: 2
                }, 1032, ["data-swiper-autoplay", "style", "onClick"]);
              }), 128))
            ]),
            renderSlot(_ctx.$slots, "pagination", {}, () => [
              showPagination.value ? (openBlock(), createElementBlock("div", _hoisted_4)) : createCommentVNode("", true)
            ]),
            renderSlot(_ctx.$slots, "navigation", {}, () => [
              showNavigation.value ? (openBlock(), createElementBlock("div", _hoisted_5)) : createCommentVNode("", true),
              showNavigation.value ? (openBlock(), createElementBlock("div", _hoisted_6)) : createCommentVNode("", true)
            ]),
            renderSlot(_ctx.$slots, "scrollbar", {}, () => [
              showScrollbar.value ? (openBlock(), createElementBlock("div", _hoisted_7)) : createCommentVNode("", true)
            ]),
            renderSlot(_ctx.$slots, "extra")
          ]),
          _: 3
        }, 16, ["class"])) : createCommentVNode("", true)
      ], 6)), [
        [_directive_loading, _ctx.loading]
      ]);
    };
  }
});
export {
  _sfc_main as default
};

import LegendModel from "./LegendModel.js";
import Legend<PERSON>ie<PERSON> from "./LegendView.js";
import legendFilter from "./legendFilter.js";
import { installLegendAction } from "./legendAction.js";
function install(registers) {
  registers.registerComponentModel(LegendModel);
  registers.registerComponentView(LegendView);
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.SERIES_FILTER, legendFilter);
  registers.registerSubTypeDefaulter("legend", function() {
    return "plain";
  });
  installLegendAction(registers);
}
export {
  install
};

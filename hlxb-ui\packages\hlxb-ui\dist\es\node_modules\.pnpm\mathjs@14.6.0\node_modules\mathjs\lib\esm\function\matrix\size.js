import { arraySize } from "../../utils/array.js";
import { factory } from "../../utils/factory.js";
import { noMatrix } from "../../utils/noop.js";
var name = "size";
var dependencies = ["typed", "config", "?matrix"];
var createSize = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed,
    config,
    matrix
  } = _ref;
  return typed(name, {
    Matrix: function Matrix(x) {
      return x.create(x.size(), "number");
    },
    Array: arraySize,
    string: function string(x) {
      return config.matrix === "Array" ? [x.length] : matrix([x.length], "dense", "number");
    },
    "number | Complex | BigNumber | Unit | boolean | null": function number__Complex__BigNumber__Unit__boolean__null(x) {
      return config.matrix === "Array" ? [] : matrix ? matrix([], "dense", "number") : noMatrix();
    }
  });
});
export {
  createSize
};

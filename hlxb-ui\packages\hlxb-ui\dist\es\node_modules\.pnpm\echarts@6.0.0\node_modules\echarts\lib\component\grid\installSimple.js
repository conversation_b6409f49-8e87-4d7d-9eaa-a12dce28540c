import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentView from "../../view/Component.js";
import GridModel from "../../coord/cartesian/GridModel.js";
import "../../util/graphic.js";
import { defaults } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { CartesianAxisModel } from "../../coord/cartesian/AxisModel.js";
import axisModelCreator from "../../coord/axisModelCreator.js";
import Grid from "../../coord/cartesian/Grid.js";
import { CartesianXAxisView, CartesianYAxisView } from "../axis/CartesianAxisView.js";
import Rect from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js";
var GridView = (
  /** @class */
  function(_super) {
    __extends(GridView2, _super);
    function GridView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = "grid";
      return _this;
    }
    GridView2.prototype.render = function(gridModel, ecModel) {
      this.group.removeAll();
      if (gridModel.get("show")) {
        this.group.add(new Rect({
          shape: gridModel.coordinateSystem.getRect(),
          style: defaults({
            fill: gridModel.get("backgroundColor")
          }, gridModel.getItemStyle()),
          silent: true,
          z2: -1
        }));
      }
    };
    GridView2.type = "grid";
    return GridView2;
  }(ComponentView)
);
var extraOption = {
  // gridIndex: 0,
  // gridId: '',
  offset: 0
};
function install(registers) {
  registers.registerComponentView(GridView);
  registers.registerComponentModel(GridModel);
  registers.registerCoordinateSystem("cartesian2d", Grid);
  axisModelCreator(registers, "x", CartesianAxisModel, extraOption);
  axisModelCreator(registers, "y", CartesianAxisModel, extraOption);
  registers.registerComponentView(CartesianXAxisView);
  registers.registerComponentView(CartesianYAxisView);
  registers.registerPreprocessor(function(option) {
    if (option.xAxis && option.yAxis && !option.grid) {
      option.grid = {};
    }
  });
}
export {
  install
};

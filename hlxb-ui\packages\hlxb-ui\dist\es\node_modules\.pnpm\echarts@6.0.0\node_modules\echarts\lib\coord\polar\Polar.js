import RadiusAxis from "./RadiusAxis.js";
import Angle<PERSON>xis from "./AngleAxis.js";
var polarDimensions = ["radius", "angle"];
var Polar = (
  /** @class */
  function() {
    function Polar2(name) {
      this.dimensions = polarDimensions;
      this.type = "polar";
      this.cx = 0;
      this.cy = 0;
      this._radiusAxis = new RadiusAxis();
      this._angleAxis = new AngleAxis();
      this.axisPointerEnabled = true;
      this.name = name || "";
      this._radiusAxis.polar = this._angleAxis.polar = this;
    }
    Polar2.prototype.containPoint = function(point) {
      var coord = this.pointToCoord(point);
      return this._radiusAxis.contain(coord[0]) && this._angleAxis.contain(coord[1]);
    };
    Polar2.prototype.containData = function(data) {
      return this._radiusAxis.containData(data[0]) && this._angleAxis.containData(data[1]);
    };
    Polar2.prototype.getAxis = function(dim) {
      var key = "_" + dim + "Axis";
      return this[key];
    };
    Polar2.prototype.getAxes = function() {
      return [this._radiusAxis, this._angleAxis];
    };
    Polar2.prototype.getAxesByScale = function(scaleType) {
      var axes = [];
      var angleAxis = this._angleAxis;
      var radiusAxis = this._radiusAxis;
      angleAxis.scale.type === scaleType && axes.push(angleAxis);
      radiusAxis.scale.type === scaleType && axes.push(radiusAxis);
      return axes;
    };
    Polar2.prototype.getAngleAxis = function() {
      return this._angleAxis;
    };
    Polar2.prototype.getRadiusAxis = function() {
      return this._radiusAxis;
    };
    Polar2.prototype.getOtherAxis = function(axis) {
      var angleAxis = this._angleAxis;
      return axis === angleAxis ? this._radiusAxis : angleAxis;
    };
    Polar2.prototype.getBaseAxis = function() {
      return this.getAxesByScale("ordinal")[0] || this.getAxesByScale("time")[0] || this.getAngleAxis();
    };
    Polar2.prototype.getTooltipAxes = function(dim) {
      var baseAxis = dim != null && dim !== "auto" ? this.getAxis(dim) : this.getBaseAxis();
      return {
        baseAxes: [baseAxis],
        otherAxes: [this.getOtherAxis(baseAxis)]
      };
    };
    Polar2.prototype.dataToPoint = function(data, clamp, out) {
      return this.coordToPoint([this._radiusAxis.dataToRadius(data[0], clamp), this._angleAxis.dataToAngle(data[1], clamp)], out);
    };
    Polar2.prototype.pointToData = function(point, clamp, out) {
      out = out || [];
      var coord = this.pointToCoord(point);
      out[0] = this._radiusAxis.radiusToData(coord[0], clamp);
      out[1] = this._angleAxis.angleToData(coord[1], clamp);
      return out;
    };
    Polar2.prototype.pointToCoord = function(point) {
      var dx = point[0] - this.cx;
      var dy = point[1] - this.cy;
      var angleAxis = this.getAngleAxis();
      var extent = angleAxis.getExtent();
      var minAngle = Math.min(extent[0], extent[1]);
      var maxAngle = Math.max(extent[0], extent[1]);
      angleAxis.inverse ? minAngle = maxAngle - 360 : maxAngle = minAngle + 360;
      var radius = Math.sqrt(dx * dx + dy * dy);
      dx /= radius;
      dy /= radius;
      var radian = Math.atan2(-dy, dx) / Math.PI * 180;
      var dir = radian < minAngle ? 1 : -1;
      while (radian < minAngle || radian > maxAngle) {
        radian += dir * 360;
      }
      return [radius, radian];
    };
    Polar2.prototype.coordToPoint = function(coord, out) {
      out = out || [];
      var radius = coord[0];
      var radian = coord[1] / 180 * Math.PI;
      out[0] = Math.cos(radian) * radius + this.cx;
      out[1] = -Math.sin(radian) * radius + this.cy;
      return out;
    };
    Polar2.prototype.getArea = function() {
      var angleAxis = this.getAngleAxis();
      var radiusAxis = this.getRadiusAxis();
      var radiusExtent = radiusAxis.getExtent().slice();
      radiusExtent[0] > radiusExtent[1] && radiusExtent.reverse();
      var angleExtent = angleAxis.getExtent();
      var RADIAN = Math.PI / 180;
      var EPSILON = 1e-4;
      return {
        cx: this.cx,
        cy: this.cy,
        r0: radiusExtent[0],
        r: radiusExtent[1],
        startAngle: -angleExtent[0] * RADIAN,
        endAngle: -angleExtent[1] * RADIAN,
        clockwise: angleAxis.inverse,
        contain: function(x, y) {
          var dx = x - this.cx;
          var dy = y - this.cy;
          var d2 = dx * dx + dy * dy;
          var r = this.r;
          var r0 = this.r0;
          return r !== r0 && d2 - EPSILON <= r * r && d2 + EPSILON >= r0 * r0;
        },
        // As the bounding box
        x: this.cx - radiusExtent[1],
        y: this.cy - radiusExtent[1],
        width: radiusExtent[1] * 2,
        height: radiusExtent[1] * 2
      };
    };
    Polar2.prototype.convertToPixel = function(ecModel, finder, value) {
      var coordSys = getCoordSys(finder);
      return coordSys === this ? this.dataToPoint(value) : null;
    };
    Polar2.prototype.convertFromPixel = function(ecModel, finder, pixel) {
      var coordSys = getCoordSys(finder);
      return coordSys === this ? this.pointToData(pixel) : null;
    };
    return Polar2;
  }()
);
function getCoordSys(finder) {
  var seriesModel = finder.seriesModel;
  var polarModel = finder.polarModel;
  return polarModel && polarModel.coordinateSystem || seriesModel && seriesModel.coordinateSystem;
}
export {
  Polar as default,
  polarDimensions
};

import { createLegacyDataSelectAction } from "../../legacy/dataSelectAction.js";
import pieLayout from "./pieLayout.js";
import dataFilter from "../../processor/dataFilter.js";
import { curry } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import PieView from "./PieView.js";
import PieSeriesModel from "./PieSeries.js";
import negativeDataFilter from "../../processor/negativeDataFilter.js";
function install(registers) {
  registers.registerChartView(PieView);
  registers.registerSeriesModel(PieSeriesModel);
  createLegacyDataSelectAction("pie", registers.registerAction);
  registers.registerLayout(curry(pieLayout, "pie"));
  registers.registerProcessor(dataFilter("pie"));
  registers.registerProcessor(negativeDataFilter("pie"));
}
export {
  install
};

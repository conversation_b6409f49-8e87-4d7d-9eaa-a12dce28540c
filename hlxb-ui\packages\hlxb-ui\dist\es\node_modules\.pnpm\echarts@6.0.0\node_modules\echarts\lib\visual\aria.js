import { clone, merge, createHashMap, isFunction, extend, defaults, filter, indexOf, isString, each } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { makeInner } from "../util/model.js";
import { getDecalFromPalette } from "../model/mixin/palette.js";
var DEFAULT_OPTION = {
  label: {
    enabled: true
  },
  decal: {
    show: false
  }
};
var inner = makeInner();
var decalPaletteScope = {};
function ariaVisual(ecModel, api) {
  var ariaModel = ecModel.getModel("aria");
  if (!ariaModel.get("enabled")) {
    return;
  }
  var defaultOption = clone(DEFAULT_OPTION);
  merge(defaultOption.label, ecModel.getLocaleModel().get("aria"), false);
  merge(ariaModel.option, defaultOption, false);
  setDecal();
  setLabel();
  function setDecal() {
    var decalModel = ariaModel.getModel("decal");
    var useDecal = decalModel.get("show");
    if (useDecal) {
      var paletteScopeGroupByType_1 = createHashMap();
      ecModel.eachSeries(function(seriesModel) {
        if (seriesModel.isColorBySeries()) {
          return;
        }
        var decalScope = paletteScopeGroupByType_1.get(seriesModel.type);
        if (!decalScope) {
          decalScope = {};
          paletteScopeGroupByType_1.set(seriesModel.type, decalScope);
        }
        inner(seriesModel).scope = decalScope;
      });
      ecModel.eachRawSeries(function(seriesModel) {
        if (ecModel.isSeriesFiltered(seriesModel)) {
          return;
        }
        if (isFunction(seriesModel.enableAriaDecal)) {
          seriesModel.enableAriaDecal();
          return;
        }
        var data = seriesModel.getData();
        if (!seriesModel.isColorBySeries()) {
          var dataAll_1 = seriesModel.getRawData();
          var idxMap_1 = {};
          var decalScope_1 = inner(seriesModel).scope;
          data.each(function(idx) {
            var rawIdx = data.getRawIndex(idx);
            idxMap_1[rawIdx] = idx;
          });
          var dataCount_1 = dataAll_1.count();
          dataAll_1.each(function(rawIdx) {
            var idx = idxMap_1[rawIdx];
            var name = dataAll_1.getName(rawIdx) || rawIdx + "";
            var paletteDecal2 = getDecalFromPalette(seriesModel.ecModel, name, decalScope_1, dataCount_1);
            var specifiedDecal2 = data.getItemVisual(idx, "decal");
            data.setItemVisual(idx, "decal", mergeDecal(specifiedDecal2, paletteDecal2));
          });
        } else {
          var paletteDecal = getDecalFromPalette(seriesModel.ecModel, seriesModel.name, decalPaletteScope, ecModel.getSeriesCount());
          var specifiedDecal = data.getVisual("decal");
          data.setVisual("decal", mergeDecal(specifiedDecal, paletteDecal));
        }
        function mergeDecal(specifiedDecal2, paletteDecal2) {
          var resultDecal = specifiedDecal2 ? extend(extend({}, paletteDecal2), specifiedDecal2) : paletteDecal2;
          resultDecal.dirty = true;
          return resultDecal;
        }
      });
    }
  }
  function setLabel() {
    var dom = api.getZr().dom;
    if (!dom) {
      return;
    }
    var labelLocale = ecModel.getLocaleModel().get("aria");
    var labelModel = ariaModel.getModel("label");
    labelModel.option = defaults(labelModel.option, labelLocale);
    if (!labelModel.get("enabled")) {
      return;
    }
    dom.setAttribute("role", "img");
    if (labelModel.get("description")) {
      dom.setAttribute("aria-label", labelModel.get("description"));
      return;
    }
    var seriesCnt = ecModel.getSeriesCount();
    var maxDataCnt = labelModel.get(["data", "maxCount"]) || 10;
    var maxSeriesCnt = labelModel.get(["series", "maxCount"]) || 10;
    var displaySeriesCnt = Math.min(seriesCnt, maxSeriesCnt);
    var ariaLabel;
    if (seriesCnt < 1) {
      return;
    } else {
      var title = getTitle();
      if (title) {
        var withTitle = labelModel.get(["general", "withTitle"]);
        ariaLabel = replace(withTitle, {
          title
        });
      } else {
        ariaLabel = labelModel.get(["general", "withoutTitle"]);
      }
      var seriesLabels_1 = [];
      var prefix = seriesCnt > 1 ? labelModel.get(["series", "multiple", "prefix"]) : labelModel.get(["series", "single", "prefix"]);
      ariaLabel += replace(prefix, {
        seriesCount: seriesCnt
      });
      ecModel.eachSeries(function(seriesModel, idx) {
        if (idx < displaySeriesCnt) {
          var seriesLabel = void 0;
          var seriesName = seriesModel.get("name");
          var withName = seriesName ? "withName" : "withoutName";
          seriesLabel = seriesCnt > 1 ? labelModel.get(["series", "multiple", withName]) : labelModel.get(["series", "single", withName]);
          seriesLabel = replace(seriesLabel, {
            seriesId: seriesModel.seriesIndex,
            seriesName: seriesModel.get("name"),
            seriesType: getSeriesTypeName(seriesModel.subType)
          });
          var data = seriesModel.getData();
          if (data.count() > maxDataCnt) {
            var partialLabel = labelModel.get(["data", "partialData"]);
            seriesLabel += replace(partialLabel, {
              displayCnt: maxDataCnt
            });
          } else {
            seriesLabel += labelModel.get(["data", "allData"]);
          }
          var middleSeparator_1 = labelModel.get(["data", "separator", "middle"]);
          var endSeparator_1 = labelModel.get(["data", "separator", "end"]);
          var excludeDimensionId_1 = labelModel.get(["data", "excludeDimensionId"]);
          var dataLabels = [];
          for (var i = 0; i < data.count(); i++) {
            if (i < maxDataCnt) {
              var name_1 = data.getName(i);
              var value = !excludeDimensionId_1 ? data.getValues(i) : filter(data.getValues(i), function(v, j) {
                return indexOf(excludeDimensionId_1, j) === -1;
              });
              var dataLabel = labelModel.get(["data", name_1 ? "withName" : "withoutName"]);
              dataLabels.push(replace(dataLabel, {
                name: name_1,
                value: value.join(middleSeparator_1)
              }));
            }
          }
          seriesLabel += dataLabels.join(middleSeparator_1) + endSeparator_1;
          seriesLabels_1.push(seriesLabel);
        }
      });
      var separatorModel = labelModel.getModel(["series", "multiple", "separator"]);
      var middleSeparator = separatorModel.get("middle");
      var endSeparator = separatorModel.get("end");
      ariaLabel += seriesLabels_1.join(middleSeparator) + endSeparator;
      dom.setAttribute("aria-label", ariaLabel);
    }
  }
  function replace(str, keyValues) {
    if (!isString(str)) {
      return str;
    }
    var result = str;
    each(keyValues, function(value, key) {
      result = result.replace(new RegExp("\\{\\s*" + key + "\\s*\\}", "g"), value);
    });
    return result;
  }
  function getTitle() {
    var title = ecModel.get("title");
    if (title && title.length) {
      title = title[0];
    }
    return title && title.text;
  }
  function getSeriesTypeName(type) {
    var typeNames = ecModel.getLocaleModel().get(["series", "typeNames"]);
    return typeNames[type] || typeNames.chart;
  }
}
export {
  ariaVisual as default
};

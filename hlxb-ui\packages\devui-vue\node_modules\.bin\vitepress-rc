#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules/vitepress-theme-demoblock/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules/vitepress-theme-demoblock/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules/vitepress-theme-demoblock/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules/vitepress-theme-demoblock/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vitepress-theme-demoblock@1_69696565e689b85afa39868a66486329/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitepress-theme-demoblock/bin/vitepress-register-components.js" "$@"
else
  exec node  "$basedir/../vitepress-theme-demoblock/bin/vitepress-register-components.js" "$@"
fi

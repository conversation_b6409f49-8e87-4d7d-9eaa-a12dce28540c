"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const dataSelectAction = require("../../legacy/dataSelectAction.js");
const pieLayout = require("./pieLayout.js");
const dataFilter = require("../../processor/dataFilter.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const PieView = require("./PieView.js");
const PieSeries = require("./PieSeries.js");
const negativeDataFilter = require("../../processor/negativeDataFilter.js");
function install(registers) {
  registers.registerChartView(PieView.default);
  registers.registerSeriesModel(PieSeries.default);
  dataSelectAction.createLegacyDataSelectAction("pie", registers.registerAction);
  registers.registerLayout(util.curry(pieLayout.default, "pie"));
  registers.registerProcessor(dataFilter.default("pie"));
  registers.registerProcessor(negativeDataFilter.default("pie"));
}
exports.install = install;

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const symbol = require("../../util/symbol.js");
const innerStore = require("../../util/innerStore.js");
const Group = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js");
const BoundingRect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js");
const Path = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Path.js");
var BOOST_SIZE_THRESHOLD = 4;
var LargeSymbolPathShape = (
  /** @class */
  /* @__PURE__ */ function() {
    function LargeSymbolPathShape2() {
    }
    return LargeSymbolPathShape2;
  }()
);
var LargeSymbolPath = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(LargeSymbolPath2, _super);
    function LargeSymbolPath2(opts) {
      var _this = _super.call(this, opts) || this;
      _this._off = 0;
      _this.hoverDataIdx = -1;
      return _this;
    }
    LargeSymbolPath2.prototype.getDefaultShape = function() {
      return new LargeSymbolPathShape();
    };
    LargeSymbolPath2.prototype.reset = function() {
      this.notClear = false;
      this._off = 0;
    };
    LargeSymbolPath2.prototype.buildPath = function(path, shape) {
      var points = shape.points;
      var size = shape.size;
      var symbolProxy = this.symbolProxy;
      var symbolProxyShape = symbolProxy.shape;
      var ctx = path.getContext ? path.getContext() : path;
      var canBoost = ctx && size[0] < BOOST_SIZE_THRESHOLD;
      var softClipShape = this.softClipShape;
      var i;
      if (canBoost) {
        this._ctx = ctx;
        return;
      }
      this._ctx = null;
      for (i = this._off; i < points.length; ) {
        var x = points[i++];
        var y = points[i++];
        if (isNaN(x) || isNaN(y)) {
          continue;
        }
        if (softClipShape && !softClipShape.contain(x, y)) {
          continue;
        }
        symbolProxyShape.x = x - size[0] / 2;
        symbolProxyShape.y = y - size[1] / 2;
        symbolProxyShape.width = size[0];
        symbolProxyShape.height = size[1];
        symbolProxy.buildPath(path, symbolProxyShape, true);
      }
      if (this.incremental) {
        this._off = i;
        this.notClear = true;
      }
    };
    LargeSymbolPath2.prototype.afterBrush = function() {
      var shape = this.shape;
      var points = shape.points;
      var size = shape.size;
      var ctx = this._ctx;
      var softClipShape = this.softClipShape;
      var i;
      if (!ctx) {
        return;
      }
      for (i = this._off; i < points.length; ) {
        var x = points[i++];
        var y = points[i++];
        if (isNaN(x) || isNaN(y)) {
          continue;
        }
        if (softClipShape && !softClipShape.contain(x, y)) {
          continue;
        }
        ctx.fillRect(x - size[0] / 2, y - size[1] / 2, size[0], size[1]);
      }
      if (this.incremental) {
        this._off = i;
        this.notClear = true;
      }
    };
    LargeSymbolPath2.prototype.findDataIndex = function(x, y) {
      var shape = this.shape;
      var points = shape.points;
      var size = shape.size;
      var w = Math.max(size[0], 4);
      var h = Math.max(size[1], 4);
      for (var idx = points.length / 2 - 1; idx >= 0; idx--) {
        var i = idx * 2;
        var x0 = points[i] - w / 2;
        var y0 = points[i + 1] - h / 2;
        if (x >= x0 && y >= y0 && x <= x0 + w && y <= y0 + h) {
          return idx;
        }
      }
      return -1;
    };
    LargeSymbolPath2.prototype.contain = function(x, y) {
      var localPos = this.transformCoordToLocal(x, y);
      var rect = this.getBoundingRect();
      x = localPos[0];
      y = localPos[1];
      if (rect.contain(x, y)) {
        var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);
        return dataIdx >= 0;
      }
      this.hoverDataIdx = -1;
      return false;
    };
    LargeSymbolPath2.prototype.getBoundingRect = function() {
      var rect = this._rect;
      if (!rect) {
        var shape = this.shape;
        var points = shape.points;
        var size = shape.size;
        var w = size[0];
        var h = size[1];
        var minX = Infinity;
        var minY = Infinity;
        var maxX = -Infinity;
        var maxY = -Infinity;
        for (var i = 0; i < points.length; ) {
          var x = points[i++];
          var y = points[i++];
          minX = Math.min(x, minX);
          maxX = Math.max(x, maxX);
          minY = Math.min(y, minY);
          maxY = Math.max(y, maxY);
        }
        rect = this._rect = new BoundingRect.default(minX - w / 2, minY - h / 2, maxX - minX + w, maxY - minY + h);
      }
      return rect;
    };
    return LargeSymbolPath2;
  }(Path.default)
);
var LargeSymbolDraw = (
  /** @class */
  function() {
    function LargeSymbolDraw2() {
      this.group = new Group.default();
    }
    LargeSymbolDraw2.prototype.updateData = function(data, opt) {
      this._clear();
      var symbolEl = this._create();
      symbolEl.setShape({
        points: data.getLayout("points")
      });
      this._setCommon(symbolEl, data, opt);
    };
    LargeSymbolDraw2.prototype.updateLayout = function(data) {
      var points = data.getLayout("points");
      this.group.eachChild(function(child) {
        if (child.startIndex != null) {
          var len = (child.endIndex - child.startIndex) * 2;
          var byteOffset = child.startIndex * 4 * 2;
          points = new Float32Array(points.buffer, byteOffset, len);
        }
        child.setShape("points", points);
        child.reset();
      });
    };
    LargeSymbolDraw2.prototype.incrementalPrepareUpdate = function(data) {
      this._clear();
    };
    LargeSymbolDraw2.prototype.incrementalUpdate = function(taskParams, data, opt) {
      var lastAdded = this._newAdded[0];
      var points = data.getLayout("points");
      var oldPoints = lastAdded && lastAdded.shape.points;
      if (oldPoints && oldPoints.length < 2e4) {
        var oldLen = oldPoints.length;
        var newPoints = new Float32Array(oldLen + points.length);
        newPoints.set(oldPoints);
        newPoints.set(points, oldLen);
        lastAdded.endIndex = taskParams.end;
        lastAdded.setShape({
          points: newPoints
        });
      } else {
        this._newAdded = [];
        var symbolEl = this._create();
        symbolEl.startIndex = taskParams.start;
        symbolEl.endIndex = taskParams.end;
        symbolEl.incremental = true;
        symbolEl.setShape({
          points
        });
        this._setCommon(symbolEl, data, opt);
      }
    };
    LargeSymbolDraw2.prototype.eachRendered = function(cb) {
      this._newAdded[0] && cb(this._newAdded[0]);
    };
    LargeSymbolDraw2.prototype._create = function() {
      var symbolEl = new LargeSymbolPath({
        cursor: "default"
      });
      symbolEl.ignoreCoarsePointer = true;
      this.group.add(symbolEl);
      this._newAdded.push(symbolEl);
      return symbolEl;
    };
    LargeSymbolDraw2.prototype._setCommon = function(symbolEl, data, opt) {
      var hostModel = data.hostModel;
      opt = opt || {};
      var size = data.getVisual("symbolSize");
      symbolEl.setShape("size", size instanceof Array ? size : [size, size]);
      symbolEl.softClipShape = opt.clipShape || null;
      symbolEl.symbolProxy = symbol.createSymbol(data.getVisual("symbol"), 0, 0, 0, 0);
      symbolEl.setColor = symbolEl.symbolProxy.setColor;
      var extrudeShadow = symbolEl.shape.size[0] < BOOST_SIZE_THRESHOLD;
      symbolEl.useStyle(
        // Draw shadow when doing fillRect is extremely slow.
        hostModel.getModel("itemStyle").getItemStyle(extrudeShadow ? ["color", "shadowBlur", "shadowColor"] : ["color"])
      );
      var globalStyle = data.getVisual("style");
      var visualColor = globalStyle && globalStyle.fill;
      if (visualColor) {
        symbolEl.setColor(visualColor);
      }
      var ecData = innerStore.getECData(symbolEl);
      ecData.seriesIndex = hostModel.seriesIndex;
      symbolEl.on("mousemove", function(e) {
        ecData.dataIndex = null;
        var dataIndex = symbolEl.hoverDataIdx;
        if (dataIndex >= 0) {
          ecData.dataIndex = dataIndex + (symbolEl.startIndex || 0);
        }
      });
    };
    LargeSymbolDraw2.prototype.remove = function() {
      this._clear();
    };
    LargeSymbolDraw2.prototype._clear = function() {
      this._newAdded = [];
      this.group.removeAll();
    };
    return LargeSymbolDraw2;
  }()
);
exports.default = LargeSymbolDraw;

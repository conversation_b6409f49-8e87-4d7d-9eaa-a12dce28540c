import { each, indexOf, createHashMap, curry, assert, map } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getTransform } from "../../util/graphic.js";
import { makeLinearBrushOtherExtent, makeRectIsTargetByCursor, makeRectPanelClipPath } from "./brushHelper.js";
import { parseFinder as parseFinder$1 } from "../../util/model.js";
var INCLUDE_FINDER_MAIN_TYPES = ["grid", "xAxis", "yAxis", "geo", "graph", "polar", "radiusAxis", "angleAxis", "bmap"];
var BrushTargetManager = (
  /** @class */
  function() {
    function BrushTargetManager2(finder, ecModel, opt) {
      var _this = this;
      this._targetInfoList = [];
      var foundCpts = parseFinder(ecModel, finder);
      each(targetInfoBuilders, function(builder, type) {
        if (!opt || !opt.include || indexOf(opt.include, type) >= 0) {
          builder(foundCpts, _this._targetInfoList);
        }
      });
    }
    BrushTargetManager2.prototype.setOutputRanges = function(areas, ecModel) {
      this.matchOutputRanges(areas, ecModel, function(area, coordRange, coordSys) {
        (area.coordRanges || (area.coordRanges = [])).push(coordRange);
        if (!area.coordRange) {
          area.coordRange = coordRange;
          var result = coordConvert[area.brushType](0, coordSys, coordRange);
          area.__rangeOffset = {
            offset: diffProcessor[area.brushType](result.values, area.range, [1, 1]),
            xyMinMax: result.xyMinMax
          };
        }
      });
      return areas;
    };
    BrushTargetManager2.prototype.matchOutputRanges = function(areas, ecModel, cb) {
      each(areas, function(area) {
        var targetInfo = this.findTargetInfo(area, ecModel);
        if (targetInfo && targetInfo !== true) {
          each(targetInfo.coordSyses, function(coordSys) {
            var result = coordConvert[area.brushType](1, coordSys, area.range, true);
            cb(area, result.values, coordSys, ecModel);
          });
        }
      }, this);
    };
    BrushTargetManager2.prototype.setInputRanges = function(areas, ecModel) {
      each(areas, function(area) {
        var targetInfo = this.findTargetInfo(area, ecModel);
        if (process.env.NODE_ENV !== "production") {
          assert(!targetInfo || targetInfo === true || area.coordRange, "coordRange must be specified when coord index specified.");
          assert(!targetInfo || targetInfo !== true || area.range, "range must be specified in global brush.");
        }
        area.range = area.range || [];
        if (targetInfo && targetInfo !== true) {
          area.panelId = targetInfo.panelId;
          var result = coordConvert[area.brushType](0, targetInfo.coordSys, area.coordRange);
          var rangeOffset = area.__rangeOffset;
          area.range = rangeOffset ? diffProcessor[area.brushType](result.values, rangeOffset.offset, getScales(result.xyMinMax, rangeOffset.xyMinMax)) : result.values;
        }
      }, this);
    };
    BrushTargetManager2.prototype.makePanelOpts = function(api, getDefaultBrushType) {
      return map(this._targetInfoList, function(targetInfo) {
        var rect = targetInfo.getPanelRect();
        return {
          panelId: targetInfo.panelId,
          defaultBrushType: getDefaultBrushType ? getDefaultBrushType(targetInfo) : null,
          clipPath: makeRectPanelClipPath(rect),
          isTargetByCursor: makeRectIsTargetByCursor(rect, api, targetInfo.coordSysModel),
          getLinearBrushOtherExtent: makeLinearBrushOtherExtent(rect)
        };
      });
    };
    BrushTargetManager2.prototype.controlSeries = function(area, seriesModel, ecModel) {
      var targetInfo = this.findTargetInfo(area, ecModel);
      return targetInfo === true || targetInfo && indexOf(targetInfo.coordSyses, seriesModel.coordinateSystem) >= 0;
    };
    BrushTargetManager2.prototype.findTargetInfo = function(area, ecModel) {
      var targetInfoList = this._targetInfoList;
      var foundCpts = parseFinder(ecModel, area);
      for (var i = 0; i < targetInfoList.length; i++) {
        var targetInfo = targetInfoList[i];
        var areaPanelId = area.panelId;
        if (areaPanelId) {
          if (targetInfo.panelId === areaPanelId) {
            return targetInfo;
          }
        } else {
          for (var j = 0; j < targetInfoMatchers.length; j++) {
            if (targetInfoMatchers[j](foundCpts, targetInfo)) {
              return targetInfo;
            }
          }
        }
      }
      return true;
    };
    return BrushTargetManager2;
  }()
);
function formatMinMax(minMax) {
  minMax[0] > minMax[1] && minMax.reverse();
  return minMax;
}
function parseFinder(ecModel, finder) {
  return parseFinder$1(ecModel, finder, {
    includeMainTypes: INCLUDE_FINDER_MAIN_TYPES
  });
}
var targetInfoBuilders = {
  grid: function(foundCpts, targetInfoList) {
    var xAxisModels = foundCpts.xAxisModels;
    var yAxisModels = foundCpts.yAxisModels;
    var gridModels = foundCpts.gridModels;
    var gridModelMap = createHashMap();
    var xAxesHas = {};
    var yAxesHas = {};
    if (!xAxisModels && !yAxisModels && !gridModels) {
      return;
    }
    each(xAxisModels, function(axisModel) {
      var gridModel = axisModel.axis.grid.model;
      gridModelMap.set(gridModel.id, gridModel);
      xAxesHas[gridModel.id] = true;
    });
    each(yAxisModels, function(axisModel) {
      var gridModel = axisModel.axis.grid.model;
      gridModelMap.set(gridModel.id, gridModel);
      yAxesHas[gridModel.id] = true;
    });
    each(gridModels, function(gridModel) {
      gridModelMap.set(gridModel.id, gridModel);
      xAxesHas[gridModel.id] = true;
      yAxesHas[gridModel.id] = true;
    });
    gridModelMap.each(function(gridModel) {
      var grid = gridModel.coordinateSystem;
      var cartesians = [];
      each(grid.getCartesians(), function(cartesian, index) {
        if (indexOf(xAxisModels, cartesian.getAxis("x").model) >= 0 || indexOf(yAxisModels, cartesian.getAxis("y").model) >= 0) {
          cartesians.push(cartesian);
        }
      });
      targetInfoList.push({
        panelId: "grid--" + gridModel.id,
        gridModel,
        coordSysModel: gridModel,
        // Use the first one as the representitive coordSys.
        coordSys: cartesians[0],
        coordSyses: cartesians,
        getPanelRect: panelRectBuilders.grid,
        xAxisDeclared: xAxesHas[gridModel.id],
        yAxisDeclared: yAxesHas[gridModel.id]
      });
    });
  },
  geo: function(foundCpts, targetInfoList) {
    each(foundCpts.geoModels, function(geoModel) {
      var coordSys = geoModel.coordinateSystem;
      targetInfoList.push({
        panelId: "geo--" + geoModel.id,
        geoModel,
        coordSysModel: geoModel,
        coordSys,
        coordSyses: [coordSys],
        getPanelRect: panelRectBuilders.geo
      });
    });
  }
};
var targetInfoMatchers = [
  // grid
  function(foundCpts, targetInfo) {
    var xAxisModel = foundCpts.xAxisModel;
    var yAxisModel = foundCpts.yAxisModel;
    var gridModel = foundCpts.gridModel;
    !gridModel && xAxisModel && (gridModel = xAxisModel.axis.grid.model);
    !gridModel && yAxisModel && (gridModel = yAxisModel.axis.grid.model);
    return gridModel && gridModel === targetInfo.gridModel;
  },
  // geo
  function(foundCpts, targetInfo) {
    var geoModel = foundCpts.geoModel;
    return geoModel && geoModel === targetInfo.geoModel;
  }
];
var panelRectBuilders = {
  grid: function() {
    return this.coordSys.master.getRect().clone();
  },
  geo: function() {
    var coordSys = this.coordSys;
    var rect = coordSys.getBoundingRect().clone();
    rect.applyTransform(getTransform(coordSys));
    return rect;
  }
};
var coordConvert = {
  lineX: curry(axisConvert, 0),
  lineY: curry(axisConvert, 1),
  rect: function(to, coordSys, rangeOrCoordRange, clamp) {
    var xminymin = to ? coordSys.pointToData([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][0], rangeOrCoordRange[1][0]], clamp);
    var xmaxymax = to ? coordSys.pointToData([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp) : coordSys.dataToPoint([rangeOrCoordRange[0][1], rangeOrCoordRange[1][1]], clamp);
    var values = [formatMinMax([xminymin[0], xmaxymax[0]]), formatMinMax([xminymin[1], xmaxymax[1]])];
    return {
      values,
      xyMinMax: values
    };
  },
  polygon: function(to, coordSys, rangeOrCoordRange, clamp) {
    var xyMinMax = [[Infinity, -Infinity], [Infinity, -Infinity]];
    var values = map(rangeOrCoordRange, function(item) {
      var p = to ? coordSys.pointToData(item, clamp) : coordSys.dataToPoint(item, clamp);
      xyMinMax[0][0] = Math.min(xyMinMax[0][0], p[0]);
      xyMinMax[1][0] = Math.min(xyMinMax[1][0], p[1]);
      xyMinMax[0][1] = Math.max(xyMinMax[0][1], p[0]);
      xyMinMax[1][1] = Math.max(xyMinMax[1][1], p[1]);
      return p;
    });
    return {
      values,
      xyMinMax
    };
  }
};
function axisConvert(axisNameIndex, to, coordSys, rangeOrCoordRange) {
  if (process.env.NODE_ENV !== "production") {
    assert(coordSys.type === "cartesian2d", "lineX/lineY brush is available only in cartesian2d.");
  }
  var axis = coordSys.getAxis(["x", "y"][axisNameIndex]);
  var values = formatMinMax(map([0, 1], function(i) {
    return to ? axis.coordToData(axis.toLocalCoord(rangeOrCoordRange[i]), true) : axis.toGlobalCoord(axis.dataToCoord(rangeOrCoordRange[i]));
  }));
  var xyMinMax = [];
  xyMinMax[axisNameIndex] = values;
  xyMinMax[1 - axisNameIndex] = [NaN, NaN];
  return {
    values,
    xyMinMax
  };
}
var diffProcessor = {
  lineX: curry(axisDiffProcessor, 0),
  lineY: curry(axisDiffProcessor, 1),
  rect: function(values, refer, scales) {
    return [[values[0][0] - scales[0] * refer[0][0], values[0][1] - scales[0] * refer[0][1]], [values[1][0] - scales[1] * refer[1][0], values[1][1] - scales[1] * refer[1][1]]];
  },
  polygon: function(values, refer, scales) {
    return map(values, function(item, idx) {
      return [item[0] - scales[0] * refer[idx][0], item[1] - scales[1] * refer[idx][1]];
    });
  }
};
function axisDiffProcessor(axisNameIndex, values, refer, scales) {
  return [values[0] - scales[axisNameIndex] * refer[0], values[1] - scales[axisNameIndex] * refer[1]];
}
function getScales(xyMinMaxCurr, xyMinMaxOrigin) {
  var sizeCurr = getSize(xyMinMaxCurr);
  var sizeOrigin = getSize(xyMinMaxOrigin);
  var scales = [sizeCurr[0] / sizeOrigin[0], sizeCurr[1] / sizeOrigin[1]];
  isNaN(scales[0]) && (scales[0] = 1);
  isNaN(scales[1]) && (scales[1] = 1);
  return scales;
}
function getSize(xyMinMax) {
  return xyMinMax ? [xyMinMax[0][1] - xyMinMax[0][0], xyMinMax[1][1] - xyMinMax[1][0]] : [NaN, NaN];
}
export {
  BrushTargetManager as default
};

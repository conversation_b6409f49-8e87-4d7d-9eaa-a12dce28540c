"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../config/index.js");
const _hoisted_1 = ["disabled"];
const _hoisted_2 = {
  key: 0,
  class: "r-input-prepend"
};
const _hoisted_3 = { class: "r-input-outer" };
const _hoisted_4 = ["disabled"];
const _hoisted_5 = {
  key: 1,
  class: "r-input-append"
};
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{ name: "HlxbInput" },
  __name: "input",
  props: {
    modelValue: {
      type: [String, Number],
      defalut: ""
    },
    type: {
      type: String,
      validator: (val) => {
        return ["text", "textarea"].includes(val);
      }
    },
    size: {
      type: String,
      default: "",
      validator: (val) => {
        return ["", "small", "medium"].includes(val);
      }
    },
    // 是否能清空
    clearable: <PERSON><PERSON>an,
    disabled: <PERSON><PERSON><PERSON>,
    center: <PERSON><PERSON><PERSON>
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const attrs = vue.useAttrs();
    const slots = vue.useSlots();
    const props = __props;
    const emit = __emit;
    const prefixCls = index.getPrefixCls("input");
    const inputValue = vue.computed({
      get() {
        return props.modelValue;
      },
      set(val) {
        emit("update:modelValue", val);
      }
    });
    const styleClass = vue.computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const inputStyleClass = vue.computed(() => {
      return {
        [`r-input--${props.size}`]: props.size,
        "is-disabled": props.disabled,
        "is-center": props.center
      };
    });
    const className = vue.computed(() => {
      return {
        "has-prepend": slots.prepend,
        "has-append": slots.append
      };
    });
    const inputProps = vue.computed(() => {
      return {
        ...attrs
      };
    });
    const shouClear = vue.computed(() => props.clearable && inputValue.value);
    const clearHandle = () => {
      inputValue.value = "";
    };
    return (_ctx, _cache) => {
      const _component_HlxbIcon = vue.resolveComponent("HlxbIcon");
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(["inline-container", styleClass.value])
      }, [
        __props.type === "textarea" ? vue.withDirectives((vue.openBlock(), vue.createElementBlock("textarea", vue.mergeProps({
          key: 0,
          class: "r-textarea",
          disabled: __props.disabled
        }, inputProps.value, {
          "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => inputValue.value = $event)
        }), null, 16, _hoisted_1)), [
          [vue.vModelText, inputValue.value]
        ]) : (vue.openBlock(), vue.createElementBlock("div", {
          key: 1,
          class: vue.normalizeClass(["inline-container", [className.value, styleClass.value]])
        }, [
          vue.unref(slots).prepend ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_2, [
            vue.renderSlot(_ctx.$slots, "prepend")
          ])) : vue.createCommentVNode("", true),
          vue.createElementVNode("div", _hoisted_3, [
            vue.withDirectives(vue.createElementVNode("input", vue.mergeProps({
              type: "text",
              class: "r-input",
              disabled: __props.disabled
            }, inputProps.value, {
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => inputValue.value = $event),
              class: inputStyleClass.value
            }), null, 16, _hoisted_4), [
              [vue.vModelText, inputValue.value]
            ]),
            shouClear.value ? (vue.openBlock(), vue.createElementBlock("span", {
              key: 0,
              class: "r-input-clear",
              onClick: clearHandle
            }, [
              vue.createVNode(_component_HlxbIcon, { name: "close" })
            ])) : vue.createCommentVNode("", true)
          ]),
          vue.unref(slots).append ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_5, [
            vue.renderSlot(_ctx.$slots, "append")
          ])) : vue.createCommentVNode("", true)
        ], 2))
      ], 2);
    };
  }
});
exports.default = _sfc_main;

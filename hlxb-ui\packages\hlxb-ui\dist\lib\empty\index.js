"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const index = require("../utils/index.js");
const index_vue_vue_type_script_setup_true_lang = require("./index.vue.js");
const EmptyIcon_vue_vue_type_script_setup_true_lang = require("./EmptyIcon.vue.js");
const HlxbEmpty = index.withInstall(index_vue_vue_type_script_setup_true_lang.default);
const HlxbEmptyIcon = index.withInstall(EmptyIcon_vue_vue_type_script_setup_true_lang.default);
exports.HlxbEmptyIcon = HlxbEmptyIcon;
exports.default = HlxbEmpty;

@import 'swiper/swiper-bundle.css';
.hlxb-swiper {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.hlxb-swiper .swiper {
  width: 100%;
  height: 100%;
}
.hlxb-swiper .swiper .shadow {
  width: 100%;
  height: 103px;
  border-radius: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  z-index: 1;
}
.hlxb-swiper .swiper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.hlxb-swiper .swiper .swiper-pagination {
  bottom: 16px;
  height: 8px;
  display: flex;
  justify-content: center;
  z-index: 2;
}
.hlxb-swiper .swiper .swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  transition: all 0.2s;
  margin: 0 4px;
}
.hlxb-swiper .swiper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 26px;
  background: #fff;
}
.hlxb-swiper .swiper .swiper-button-prev,
.hlxb-swiper .swiper .swiper-button-next {
  color: #fff;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  transition: all 0.3s;
}
.hlxb-swiper .swiper .swiper-button-prev:hover,
.hlxb-swiper .swiper .swiper-button-next:hover {
  background: rgba(0, 0, 0, 0.6);
}
.hlxb-swiper .swiper .swiper-button-prev::after,
.hlxb-swiper .swiper .swiper-button-next::after {
  font-size: 16px;
  font-weight: bold;
}
.hlxb-swiper .swiper .swiper-button-prev {
  left: 10px;
}
.hlxb-swiper .swiper .swiper-button-next {
  right: 10px;
}
.hlxb-swiper .swiper .swiper-scrollbar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  height: 4px;
}
.hlxb-swiper .swiper .swiper-scrollbar .swiper-scrollbar-drag {
  background: #fff;
  border-radius: 4px;
}
.hlxb-swiper .swiper-slide {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

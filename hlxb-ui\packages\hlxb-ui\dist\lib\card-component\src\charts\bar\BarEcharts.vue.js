"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const useECharts = require("../../../../utils/hooks/useECharts.js");
const data = require("./data.js");
const _hoisted_1 = { style: { "height": "100%", "width": "100%", "flex": "1" } };
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  __name: "BarEcharts",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    stack: {
      type: String,
      // 堆叠类型
      default: ""
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    },
    // 特殊柱状图 图例不可点击
    special: {
      type: String,
      default: "default"
      // default, hat
    }
  },
  setup(__props) {
    const props = __props;
    const chartRef = vue.ref(null);
    const { setOptions } = useECharts.useECharts(chartRef);
    function handleSetVisitChart() {
      if (props.dataList instanceof Array && props.dataList.length) {
        setOptions(data.optionFormate(props));
      }
    }
    vue.watch(
      () => props.dataList,
      () => {
        handleSetVisitChart();
      },
      { immediate: true }
    );
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
        vue.createElementVNode("div", {
          ref_key: "chartRef",
          ref: chartRef,
          style: { "height": "100%", "width": "100%" }
        }, null, 512)
      ]);
    };
  }
});
exports.default = _sfc_main;

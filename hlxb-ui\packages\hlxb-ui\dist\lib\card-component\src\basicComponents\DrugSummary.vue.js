"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const antDesignVue = require("ant-design-vue");
const up = require("../assets/images/up.svg.js");
const down = require("../assets/images/down.svg.js");
const index = require("../../../config/index.js");
const _hoisted_1 = {
  key: 0,
  class: "container-list"
};
const _hoisted_2 = { class: "conten-box" };
const _hoisted_3 = { class: "name" };
const _hoisted_4 = { class: "text" };
const _hoisted_5 = {
  key: 0,
  class: "value value-before"
};
const _hoisted_6 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_7 = { class: "number-value" };
const _hoisted_8 = {
  key: 2,
  class: "unit"
};
const _hoisted_9 = { class: "item" };
const _hoisted_10 = { class: "conten-box" };
const _hoisted_11 = { class: "name" };
const _hoisted_12 = { class: "text" };
const _hoisted_13 = { class: "value" };
const _hoisted_14 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_15 = { class: "number-value" };
const _hoisted_16 = {
  key: 2,
  class: "unit"
};
const _hoisted_17 = { class: "item" };
const _hoisted_18 = { class: "conten-box" };
const _hoisted_19 = { class: "name" };
const _hoisted_20 = { class: "text" };
const _hoisted_21 = { class: "value" };
const _hoisted_22 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_23 = { class: "number-value" };
const _hoisted_24 = {
  key: 2,
  class: "unit"
};
const _hoisted_25 = {
  key: 3,
  class: "ups-icon"
};
const _hoisted_26 = ["src"];
const _hoisted_27 = ["src"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbDrugSummary"
  },
  __name: "DrugSummary",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    // 小图标是否展示
    elect: {
      type: Boolean,
      default: false
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("drug-summary");
    const props = __props;
    const loading = vue.ref(false);
    function showTooltip(e) {
      if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none";
      }
    }
    vue.watch(
      () => props.dataList,
      (newVal) => {
        if (!newVal) {
          loading.value = true;
        } else {
          loading.value = false;
        }
      }
    );
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        __props.dataList.length ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
          (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(__props.dataList, (item, index2) => {
            return vue.openBlock(), vue.createElementBlock("div", {
              class: "list-item",
              key: index2
            }, [
              vue.createElementVNode("div", {
                class: vue.normalizeClass(["item", item.value || item.unitName ? "item-after" : ""])
              }, [
                vue.createElementVNode("div", _hoisted_2, [
                  vue.createElementVNode("div", _hoisted_3, [
                    vue.createElementVNode("div", _hoisted_4, vue.toDisplayString(item.indexName), 1)
                  ]),
                  item.value || item.unitName ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_5, [
                    item.value === null ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_6, "-")) : (vue.openBlock(), vue.createBlock(vue.unref(antDesignVue.Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: vue.withCtx(() => [
                        vue.createTextVNode(vue.toDisplayString(Number(item.value) ? Number(item.value) : "-") + vue.toDisplayString(item.unitName), 1)
                      ]),
                      default: vue.withCtx(() => [
                        vue.createElementVNode("div", _hoisted_7, vue.toDisplayString(Number(item.value) ? Number(item.value) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.value !== null ? (vue.openBlock(), vue.createElementBlock("span", _hoisted_8, vue.toDisplayString(item.unitName), 1)) : vue.createCommentVNode("", true)
                  ])) : vue.createCommentVNode("", true)
                ])
              ], 2),
              vue.createElementVNode("div", _hoisted_9, [
                vue.createElementVNode("div", _hoisted_10, [
                  vue.createElementVNode("div", _hoisted_11, [
                    vue.renderSlot(_ctx.$slots, "summaryIcon"),
                    vue.createElementVNode("div", _hoisted_12, vue.toDisplayString(item.indexPreName), 1)
                  ]),
                  vue.createElementVNode("div", _hoisted_13, [
                    item.preVal === null ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_14, "-")) : (vue.openBlock(), vue.createBlock(vue.unref(antDesignVue.Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: vue.withCtx(() => [
                        vue.createTextVNode(vue.toDisplayString(Number(item.preVal) ? Number(item.preVal) : "-") + vue.toDisplayString(item.unitName), 1)
                      ]),
                      default: vue.withCtx(() => [
                        vue.createElementVNode("div", _hoisted_15, vue.toDisplayString(Number(item.preVal) ? Number(item.preVal) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.preVal !== null ? (vue.openBlock(), vue.createElementBlock("span", _hoisted_16, vue.toDisplayString(item.unitName), 1)) : vue.createCommentVNode("", true)
                  ])
                ])
              ]),
              vue.createElementVNode("div", _hoisted_17, [
                vue.createElementVNode("div", _hoisted_18, [
                  vue.createElementVNode("div", _hoisted_19, [
                    vue.renderSlot(_ctx.$slots, "summaryIconTwo"),
                    vue.createElementVNode("div", _hoisted_20, vue.toDisplayString(item.ratioName), 1)
                  ]),
                  vue.createElementVNode("div", _hoisted_21, [
                    item.ratioVal === null ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_22, "-")) : (vue.openBlock(), vue.createBlock(vue.unref(antDesignVue.Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: vue.withCtx(() => [
                        vue.createTextVNode(vue.toDisplayString(Number(item.ratioVal) ? Number(item.ratioVal) : "-") + "%", 1)
                      ]),
                      default: vue.withCtx(() => [
                        vue.createElementVNode("div", _hoisted_23, vue.toDisplayString(Number(item.ratioVal) ? Number(item.ratioVal) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.ratioVal !== null ? (vue.openBlock(), vue.createElementBlock("span", _hoisted_24, "%")) : vue.createCommentVNode("", true),
                    item.ratioVal ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_25, [
                      item.ratioVal > 0 ? (vue.openBlock(), vue.createElementBlock("img", {
                        key: 0,
                        src: vue.unref(up.default)
                      }, null, 8, _hoisted_26)) : item.ratioVal < 0 ? (vue.openBlock(), vue.createElementBlock("img", {
                        key: 1,
                        src: vue.unref(down.default)
                      }, null, 8, _hoisted_27)) : vue.createCommentVNode("", true)
                    ])) : vue.createCommentVNode("", true)
                  ])
                ])
              ])
            ]);
          }), 128))
        ])) : vue.createCommentVNode("", true)
      ], 2);
    };
  }
});
exports.default = _sfc_main;

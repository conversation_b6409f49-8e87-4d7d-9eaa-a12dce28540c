"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const PictorialBarView = require("./PictorialBarView.js");
const PictorialBarSeries = require("./PictorialBarSeries.js");
const barGrid = require("../../layout/barGrid.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
function install(registers) {
  registers.registerChartView(PictorialBarView.default);
  registers.registerSeriesModel(PictorialBarSeries.default);
  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, util.curry(barGrid.layout, "pictorialBar"));
  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, barGrid.createProgressiveLayout("pictorialBar"));
}
exports.install = install;

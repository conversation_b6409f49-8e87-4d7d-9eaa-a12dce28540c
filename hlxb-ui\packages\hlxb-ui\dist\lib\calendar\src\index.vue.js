"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js");
const prefixCls = require("./hooks/prefixCls.js");
const DateGrid_vue_vue_type_script_setup_true_lang = require("./components/DateGrid.vue.js");
;/* empty css                          */
const utils = require("./hooks/utils.js");
const ExpandButton_vue_vue_type_script_setup_true_lang = require("./components/ExpandButton.vue.js");
;/* empty css                              */
const WeekdaysHeader_vue_vue_type_script_setup_true_lang = require("./components/WeekdaysHeader.vue.js");
;/* empty css                                */
const useCalendarContext = require("./hooks/useCalendarContext.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCalendar"
  },
  __name: "index",
  props: {
    selectedDate: { default: () => index.default() },
    options: { default: () => ({}) }
  },
  emits: ["update:selectedDate", "date-click", "month-change", "expand-change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const fullMonthDates = vue.ref([]);
    let wheelDelta = 0;
    const { options, currentDate, isExpanded } = useCalendarContext.useProvideCalendarContext(props);
    const generateFullMonthDatesWrapper = () => {
      fullMonthDates.value = utils.generateFullMonthDates(
        currentDate.value,
        options.value.fillPrevNextMonth
      );
    };
    const displayedDates = vue.computed(() => {
      if (!isExpanded.value) {
        const selected = props.selectedDate;
        const selectedWeekStart = selected.startOf("week");
        const selectedWeekEnd = selected.endOf("week");
        const year = currentDate.value.year();
        const month = currentDate.value.month();
        const lastDay = index.default().year(year).month(month).endOf("month");
        const lastWeekStart = lastDay.startOf("week");
        const isInLastWeek = selected.isSame(lastWeekStart, "week") || selected.isAfter(lastWeekStart);
        let startDate, endDate;
        if (isInLastWeek) {
          startDate = selectedWeekStart.subtract(1, "week");
          endDate = selectedWeekEnd;
        } else {
          startDate = selectedWeekStart;
          endDate = selectedWeekEnd.add(1, "week");
        }
        return fullMonthDates.value.filter((dateInfo) => {
          const date = dateInfo.date;
          return (date.isAfter(startDate) || date.isSame(startDate)) && (date.isBefore(endDate) || date.isSame(endDate));
        });
      } else {
        return fullMonthDates.value;
      }
    });
    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value;
      emit("expand-change", isExpanded.value);
      if (!isExpanded.value) {
        const selectedMonth = props.selectedDate.month();
        const currentMonth = currentDate.value.month();
        if (selectedMonth !== currentMonth) {
          currentDate.value = props.selectedDate.startOf("month");
          generateFullMonthDatesWrapper();
        }
      }
    };
    const handleDateClick = (date, dateInfo) => {
      if (!options.value.selectable) return;
      const clickedMonth = date.month();
      const currentMonth = currentDate.value.month();
      emit("update:selectedDate", date);
      emit("date-click", date, dateInfo);
      if (clickedMonth !== currentMonth) {
        currentDate.value = date.startOf("month");
        generateFullMonthDatesWrapper();
        emit("month-change", currentDate.value);
      }
      if (options.value.autoCollapseAfterDateSelect !== false) {
        isExpanded.value = false;
      }
    };
    const previousMonth = () => {
      currentDate.value = currentDate.value.subtract(1, "month");
      if (!isExpanded.value && options.value.autoExpandAfterMonthChange !== false) {
        isExpanded.value = true;
      }
      generateFullMonthDatesWrapper();
      emit("month-change", currentDate.value);
      wheelDelta = 0;
    };
    const nextMonth = () => {
      currentDate.value = currentDate.value.add(1, "month");
      if (!isExpanded.value && options.value.autoExpandAfterMonthChange !== false) {
        isExpanded.value = true;
      }
      generateFullMonthDatesWrapper();
      emit("month-change", currentDate.value);
      wheelDelta = 0;
    };
    const handleWheel = (event) => {
      if (!isExpanded.value || !options.value.wheelMonthChange) return;
      event.preventDefault();
      const threshold = Math.max(0, options.value.wheelSensitivity);
      wheelDelta += event.deltaY;
      if (Math.abs(wheelDelta) >= threshold) {
        if (wheelDelta > 0) {
          nextMonth();
        } else {
          previousMonth();
        }
        wheelDelta = 0;
      }
    };
    const goToToday = () => {
      const today = index.default();
      handleDateClick(today, {
        date: today,
        isCurrentMonth: true,
        isToday: true,
        dateString: today.format("YYYY-MM-DD")
      });
    };
    vue.watch(
      () => props.selectedDate,
      (newDate) => {
        const newMonth = newDate.month();
        const currentMonth = currentDate.value.month();
        if (newMonth !== currentMonth) {
          currentDate.value = newDate.startOf("month");
          generateFullMonthDatesWrapper();
        }
      },
      { immediate: true }
    );
    generateFullMonthDatesWrapper();
    __expose({
      goToToday,
      nextMonth,
      previousMonth,
      fullMonthDates,
      currentDate: () => currentDate.value,
      isExpanded: () => isExpanded.value
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(vue.unref(prefixCls.prefixCls)),
        onWheel: handleWheel
      }, [
        vue.createVNode(WeekdaysHeader_vue_vue_type_script_setup_true_lang.default),
        vue.createVNode(DateGrid_vue_vue_type_script_setup_true_lang.default, {
          displayedDates: displayedDates.value,
          onDateClick: handleDateClick
        }, {
          "date-cell": vue.withCtx(({ dateItem }) => [
            vue.renderSlot(_ctx.$slots, "date-cell", { dateItem })
          ]),
          _: 3
        }, 8, ["displayedDates"]),
        vue.createVNode(ExpandButton_vue_vue_type_script_setup_true_lang.default, { onToggleExpand: toggleExpand })
      ], 34);
    };
  }
});
exports.default = _sfc_main;

.hlxb-card-body {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 16px;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}
.hlxb-card-body.Dark {
  color: #fff;
}
.hlxb-card-body.light {
  color: #333;
}
.hlxb-card-body.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-card-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  color: #333;
  min-height: 48px;
  justify-content: space-between;
  /* 底部分割线样式 */
  /* 左侧内容容器样式 */
  /* 标题文本样式 */
}
.hlxb-card-header.base-header_line {
  border-bottom: 1px solid #e9e9e9;
}
.hlxb-card-header .header_left {
  display: flex;
  /* gap: 0 16px; */
  align-items: center;
}
.hlxb-card-header .text {
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}
.hlxb-card-header.Dark {
  color: #fff;
}
.hlxb-card-header.light {
  color: #333;
}
.hlxb-card-header.screenColor {
  color: #fff;
  background: transparent;
}
/* 卡片基础样式 */
.hlxb-card {
  width: 100%;
  background-color: #fcfcfc;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* 卡片内容容器样式 */
}
.hlxb-card .container-content {
  flex: 1;
  overflow: hidden;
}
.hlxb-card.Dark {
  color: #fff;
}
.hlxb-card.light {
  color: #333;
}
.hlxb-card.screenColor {
  color: #fff;
  background-color: transparent;
}

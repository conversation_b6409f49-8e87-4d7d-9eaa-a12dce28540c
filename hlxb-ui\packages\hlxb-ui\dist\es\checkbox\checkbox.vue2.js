import { defineComponent, inject, computed, createElement<PERSON><PERSON>, openBlock, normalizeClass, createElementVNode, withDirectives, vModelCheckbox, toDisplayString } from "vue";
import { getPrefixCls } from "../config/index.js";
const _hoisted_1 = { class: "r-checkbox--outer" };
const _hoisted_2 = ["value"];
const _hoisted_3 = { class: "r-checkbox__label" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCheckbox"
  },
  __name: "checkbox",
  props: {
    label: String,
    modelValue: [<PERSON>ole<PERSON>, Array]
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const checkboxGroup = inject("CheckboxGroup", null);
    const prefixCls = getPrefixCls("checkbox");
    const isGroup = computed(() => checkboxGroup !== null);
    const CKValue = computed({
      get() {
        return isGroup.value ? checkboxGroup.modelValue : props.modelValue;
      },
      set(val) {
        if (isGroup.value) {
          checkboxGroup.$emit("update:modelValue", val);
        } else {
          emits("update:modelValue", val);
        }
      }
    });
    const isChecked = computed(() => {
      var _a;
      if (isGroup.value) {
        return checkboxGroup.modelValue.includes(props.label);
      } else {
        if (typeof props.modelValue === "boolean") {
          return props.modelValue;
        }
        return (_a = props.modelValue) == null ? void 0 : _a.includes(props.label);
      }
    });
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("label", {
        class: normalizeClass(["r-checkbox", styleClass.value])
      }, [
        createElementVNode("span", _hoisted_1, [
          createElementVNode("span", {
            class: normalizeClass(["r-checkbox--inner", { "is-checked": isChecked.value }])
          }, null, 2),
          withDirectives(createElementVNode("input", {
            type: "checkbox",
            class: "r-checkbox-native",
            value: __props.label,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => CKValue.value = $event)
          }, null, 8, _hoisted_2), [
            [vModelCheckbox, CKValue.value]
          ])
        ]),
        createElementVNode("span", _hoisted_3, toDisplayString(__props.label), 1)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

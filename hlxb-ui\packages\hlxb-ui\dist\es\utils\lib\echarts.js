import * as core from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/core.js";
import { install } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/legend/install.js";
import { install as install$1 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/title/install.js";
import { install as install$2 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/tooltip/install.js";
import { install as install$3 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/grid/install.js";
import { install as install$4 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/polar/install.js";
import { install as install$5 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/aria/install.js";
import { install as install$6 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/parallel/install.js";
import { install as install$7 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/bar/install.js";
import { install as install$8 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/line/install.js";
import { install as install$9 } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/pie/install.js";
import { install as install$a } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/map/install.js";
import { install as install$b } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/radar/install.js";
import { install as install$c } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/renderer/installSVGRenderer.js";
import { install as install$d } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/bar/installPictorialBar.js";
import { install as install$e } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/radar/install.js";
import { install as install$f } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/toolbox/install.js";
import { install as install$g } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/dataZoom/install.js";
import { install as install$h } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/visualMap/install.js";
import { install as install$i } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/timeline/install.js";
import { install as install$j } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/calendar/install.js";
import { install as install$k } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/graphic/install.js";
import { install as install$l } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/chart/scatter/install.js";
import { install as install$m } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/marker/installMarkLine.js";
import { install as install$n } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/component/marker/installMarkPoint.js";
import { use } from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/lib/extension.js";
use([
  install,
  install$1,
  install$2,
  install$3,
  install$4,
  install$5,
  install$6,
  install$7,
  install$8,
  install$9,
  install$a,
  install$b,
  install$c,
  install$d,
  install$e,
  install$f,
  install$g,
  install$h,
  install$i,
  install$j,
  install$k,
  install$l,
  install$m,
  install$n
]);
export {
  core as default
};

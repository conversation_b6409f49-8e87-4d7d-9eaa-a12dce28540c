"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const Loading3QuartersOutlined$1 = require("../../../../../../@ant-design_icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/Loading3QuartersOutlined.js");
const AntdIcon = require("../components/AntdIcon.js");
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var Loading3QuartersOutlined = function Loading3QuartersOutlined2(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return vue.createVNode(AntdIcon.default, _objectSpread({}, p, {
    "icon": Loading3QuartersOutlined$1.default
  }), null);
};
Loading3QuartersOutlined.displayName = "Loading3QuartersOutlined";
Loading3QuartersOutlined.inheritAttrs = false;
exports.default = Loading3QuartersOutlined;

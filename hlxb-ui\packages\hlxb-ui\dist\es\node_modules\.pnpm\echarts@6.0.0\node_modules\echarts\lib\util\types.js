import { createHashMap } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
var VISUAL_DIMENSIONS = createHashMap(["tooltip", "label", "itemName", "itemId", "itemGroupId", "itemChildGroupId", "seriesName"]);
var SOURCE_FORMAT_ORIGINAL = "original";
var SOURCE_FORMAT_ARRAY_ROWS = "arrayRows";
var SOURCE_FORMAT_OBJECT_ROWS = "objectRows";
var SOURCE_FORMAT_KEYED_COLUMNS = "keyedColumns";
var SOURCE_FORMAT_TYPED_ARRAY = "typedArray";
var SOURCE_FORMAT_UNKNOWN = "unknown";
var SERIES_LAYOUT_BY_COLUMN = "column";
var SERIES_LAYOUT_BY_ROW = "row";
export {
  SERIES_LAYOUT_BY_COLUMN,
  SERIES_LAYOUT_BY_ROW,
  SOURCE_FORMAT_ARRAY_ROWS,
  SOURCE_FORMAT_KEYED_COLUMNS,
  SOURCE_FORMAT_OBJECT_ROWS,
  SOURCE_FORMAT_ORIGINAL,
  SOURCE_FORMAT_TYPED_ARRAY,
  SOURCE_FORMAT_UNKNOWN,
  VISUAL_DIMENSIONS
};

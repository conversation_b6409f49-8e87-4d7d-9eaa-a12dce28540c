import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getLayoutRect } from "../../util/layout.js";
var paramsSet = [["left", "right", "width"], ["top", "bottom", "height"]];
function getItemAlign(visualMapModel, api, itemSize) {
  var modelOption = visualMapModel.option;
  var itemAlign = modelOption.align;
  if (itemAlign != null && itemAlign !== "auto") {
    return itemAlign;
  }
  var ecSize = {
    width: api.getWidth(),
    height: api.getHeight()
  };
  var realIndex = modelOption.orient === "horizontal" ? 1 : 0;
  var reals = paramsSet[realIndex];
  var fakeValue = [0, null, 10];
  var layoutInput = {};
  for (var i = 0; i < 3; i++) {
    layoutInput[paramsSet[1 - realIndex][i]] = fakeValue[i];
    layoutInput[reals[i]] = i === 2 ? itemSize[0] : modelOption[reals[i]];
  }
  var rParam = [["x", "width", 3], ["y", "height", 0]][realIndex];
  var rect = getLayoutRect(layoutInput, ecSize, modelOption.padding);
  return reals[(rect.margin[rParam[2]] || 0) + rect[rParam[0]] + rect[rParam[1]] * 0.5 < ecSize[rParam[1]] * 0.5 ? 0 : 1];
}
function makeHighDownBatch(batch, visualMapModel) {
  each(batch || [], function(batchItem) {
    if (batchItem.dataIndex != null) {
      batchItem.dataIndexInside = batchItem.dataIndex;
      batchItem.dataIndex = null;
    }
    batchItem.highlightKey = "visualMap" + (visualMapModel ? visualMapModel.componentIndex : "");
  });
  return batch;
}
export {
  getItemAlign,
  makeHighDownBatch
};

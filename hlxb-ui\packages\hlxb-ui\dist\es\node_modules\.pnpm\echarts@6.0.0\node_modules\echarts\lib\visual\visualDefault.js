import { clone, isArray } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import tokens from "./tokens.js";
var visualDefault = {
  /**
   * @public
   */
  get: function(visualType, key, isCategory) {
    var value = clone((defaultOption[visualType] || {})[key]);
    return isCategory ? isArray(value) ? value[value.length - 1] : value : value;
  }
};
var defaultOption = {
  color: {
    active: ["#006edd", "#e0ffff"],
    inactive: [tokens.color.transparent]
  },
  colorHue: {
    active: [0, 360],
    inactive: [0, 0]
  },
  colorSaturation: {
    active: [0.3, 1],
    inactive: [0, 0]
  },
  colorLightness: {
    active: [0.9, 0.5],
    inactive: [0, 0]
  },
  colorAlpha: {
    active: [0.3, 1],
    inactive: [0, 0]
  },
  opacity: {
    active: [0.3, 1],
    inactive: [0, 0]
  },
  symbol: {
    active: ["circle", "roundRect", "diamond"],
    inactive: ["none"]
  },
  symbolSize: {
    active: [10, 50],
    inactive: [0, 0]
  }
};
export {
  visualDefault as default
};

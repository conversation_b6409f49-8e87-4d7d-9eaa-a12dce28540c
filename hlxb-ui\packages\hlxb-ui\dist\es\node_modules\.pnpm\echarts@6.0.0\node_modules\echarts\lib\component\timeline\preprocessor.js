import { isArray, each, isObject } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function timelinePreprocessor(option) {
  var timelineOpt = option && option.timeline;
  if (!isArray(timelineOpt)) {
    timelineOpt = timelineOpt ? [timelineOpt] : [];
  }
  each(timelineOpt, function(opt) {
    if (!opt) {
      return;
    }
    compatibleEC2(opt);
  });
}
function compatibleEC2(opt) {
  var type = opt.type;
  var ec2Types = {
    "number": "value",
    "time": "time"
  };
  if (ec2Types[type]) {
    opt.axisType = ec2Types[type];
    delete opt.type;
  }
  transferItem(opt);
  if (has(opt, "controlPosition")) {
    var controlStyle = opt.controlStyle || (opt.controlStyle = {});
    if (!has(controlStyle, "position")) {
      controlStyle.position = opt.controlPosition;
    }
    if (controlStyle.position === "none" && !has(controlStyle, "show")) {
      controlStyle.show = false;
      delete controlStyle.position;
    }
    delete opt.controlPosition;
  }
  each(opt.data || [], function(dataItem) {
    if (isObject(dataItem) && !isArray(dataItem)) {
      if (!has(dataItem, "value") && has(dataItem, "name")) {
        dataItem.value = dataItem.name;
      }
      transferItem(dataItem);
    }
  });
}
function transferItem(opt) {
  var itemStyle = opt.itemStyle || (opt.itemStyle = {});
  var itemStyleEmphasis = itemStyle.emphasis || (itemStyle.emphasis = {});
  var label = opt.label || opt.label || {};
  var labelNormal = label.normal || (label.normal = {});
  var excludeLabelAttr = {
    normal: 1,
    emphasis: 1
  };
  each(label, function(value, name) {
    if (!excludeLabelAttr[name] && !has(labelNormal, name)) {
      labelNormal[name] = value;
    }
  });
  if (itemStyleEmphasis.label && !has(label, "emphasis")) {
    label.emphasis = itemStyleEmphasis.label;
    delete itemStyleEmphasis.label;
  }
}
function has(obj, attr) {
  return obj.hasOwnProperty(attr);
}
export {
  timelinePreprocessor as default
};

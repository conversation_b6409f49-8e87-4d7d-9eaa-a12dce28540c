"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index$2 = require("../../../../card/index.js");
const LineEcharts_vue_vue_type_script_setup_true_lang = require("../../charts/line/LineEcharts.vue.js");
const Empty_vue_vue_type_script_setup_true_name_HEmpty_lang = require("../../basicComponents/Empty.vue.js");
;/* empty css                                */
const Loading_vue_vue_type_script_setup_true_name_CardLoading_lang = require("../../basicComponents/Loading.vue.js");
;/* empty css                                  */
const HorizontaSmallSquare_vue_vue_type_script_setup_true_lang = require("../../basicComponents/HorizontaSmallSquare.vue.js");
;/* empty css                                               */
const index = require("../../hooks/index.js");
const index$1 = require("../../../../config/index.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbLinePlusCard",
    inheritAttrs: false
  },
  __name: "LinePlusCard",
  props: {
    // 顶部数据列表，类型为 HorizonListType 数组，默认值为空数组
    topList: {
      type: Array,
      default: () => []
    },
    // 底部数据列表，类型为 echartsListType 数组，默认值为空数组
    bottomList: {
      type: Array,
      default: () => []
    },
    //  数据是否为空 默认值为 false ，类型为布尔值
    empty: {
      type: Boolean,
      default: () => false
    },
    // 是否显示加载状态组件，类型为布尔值，默认值为 false
    loading: {
      type: Boolean,
      default: false
    },
    // 颜色模式
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index$1.getPrefixCls("card-combination-line-plus");
    const { filtersSlots } = index.useFilterSlots();
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        vue.createVNode(vue.unref(index$2.HlxbCard), vue.mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), vue.createSlots({
          bodyTop: vue.withCtx(() => [
            !__props.empty && __props.topList.length && !__props.loading ? (vue.openBlock(), vue.createBlock(HorizontaSmallSquare_vue_vue_type_script_setup_true_lang.default, vue.mergeProps({ key: 0 }, _ctx.$attrs, {
              themeColor: __props.themeColor,
              dataList: __props.topList
            }), null, 16, ["themeColor", "dataList"])) : vue.createCommentVNode("", true)
          ]),
          bodyBottom: vue.withCtx(() => [
            __props.loading ? (vue.openBlock(), vue.createBlock(Loading_vue_vue_type_script_setup_true_name_CardLoading_lang.default, {
              key: 0,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"])) : !__props.empty ? (vue.openBlock(), vue.createBlock(LineEcharts_vue_vue_type_script_setup_true_lang.default, vue.mergeProps({ key: 1 }, _ctx.$attrs, {
              themeColor: __props.themeColor,
              dataList: __props.bottomList
            }), null, 16, ["themeColor", "dataList"])) : (vue.openBlock(), vue.createBlock(Empty_vue_vue_type_script_setup_true_name_HEmpty_lang.default, {
              key: 2,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"]))
          ]),
          _: 2
        }, [
          vue.renderList(vue.unref(filtersSlots), (item) => {
            return {
              name: item,
              fn: vue.withCtx(() => [
                vue.renderSlot(_ctx.$slots, item)
              ])
            };
          })
        ]), 1040, ["themeColor"])
      ], 2);
    };
  }
});
exports.default = _sfc_main;

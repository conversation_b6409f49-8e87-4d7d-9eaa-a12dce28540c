import { createVNode } from "vue";
import Loading3QuartersOutlined$1 from "../../../../../../@ant-design_icons-svg@4.4.2/node_modules/@ant-design/icons-svg/es/asn/Loading3QuartersOutlined.js";
import Icon from "../components/AntdIcon.js";
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? Object(arguments[i]) : {};
    var ownKeys = Object.keys(source);
    if (typeof Object.getOwnPropertySymbols === "function") {
      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {
        return Object.getOwnPropertyDescriptor(source, sym).enumerable;
      }));
    }
    ownKeys.forEach(function(key) {
      _defineProperty(target, key, source[key]);
    });
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var Loading3QuartersOutlined = function Loading3QuartersOutlined2(props, context) {
  var p = _objectSpread({}, props, context.attrs);
  return createVNode(Icon, _objectSpread({}, p, {
    "icon": Loading3QuartersOutlined$1
  }), null);
};
Loading3QuartersOutlined.displayName = "Loading3QuartersOutlined";
Loading3QuartersOutlined.inheritAttrs = false;
export {
  Loading3QuartersOutlined as default
};

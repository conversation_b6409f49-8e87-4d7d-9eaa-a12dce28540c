"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const createSeriesData = require("../helper/createSeriesData.js");
const Series = require("../../model/Series.js");
const tokens = require("../../visual/tokens.js");
var ScatterSeriesModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(ScatterSeriesModel2, _super);
    function ScatterSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ScatterSeriesModel2.type;
      _this.hasSymbolVisual = true;
      return _this;
    }
    ScatterSeriesModel2.prototype.getInitialData = function(option, ecModel) {
      return createSeriesData.default(null, this, {
        useEncodeDefaulter: true
      });
    };
    ScatterSeriesModel2.prototype.getProgressive = function() {
      var progressive = this.option.progressive;
      if (progressive == null) {
        return this.option.large ? 5e3 : this.get("progressive");
      }
      return progressive;
    };
    ScatterSeriesModel2.prototype.getProgressiveThreshold = function() {
      var progressiveThreshold = this.option.progressiveThreshold;
      if (progressiveThreshold == null) {
        return this.option.large ? 1e4 : this.get("progressiveThreshold");
      }
      return progressiveThreshold;
    };
    ScatterSeriesModel2.prototype.brushSelector = function(dataIndex, data, selectors) {
      return selectors.point(data.getItemLayout(dataIndex));
    };
    ScatterSeriesModel2.prototype.getZLevelKey = function() {
      return this.getData().count() > this.getProgressiveThreshold() ? this.id : "";
    };
    ScatterSeriesModel2.type = "series.scatter";
    ScatterSeriesModel2.dependencies = ["grid", "polar", "geo", "singleAxis", "calendar", "matrix"];
    ScatterSeriesModel2.defaultOption = {
      coordinateSystem: "cartesian2d",
      // zlevel: 0,
      z: 2,
      legendHoverLink: true,
      symbolSize: 10,
      // symbolRotate: null,  // 图形旋转控制
      large: false,
      // Available when large is true
      largeThreshold: 2e3,
      // cursor: null,
      itemStyle: {
        opacity: 0.8
        // color: 各异
      },
      emphasis: {
        scale: true
      },
      // If clip the overflow graphics
      // Works on cartesian / polar series
      clip: true,
      select: {
        itemStyle: {
          borderColor: tokens.default.color.primary
        }
      },
      universalTransition: {
        divideShape: "clone"
      }
      // progressive: null
    };
    return ScatterSeriesModel2;
  }(Series.default)
);
exports.default = ScatterSeriesModel;

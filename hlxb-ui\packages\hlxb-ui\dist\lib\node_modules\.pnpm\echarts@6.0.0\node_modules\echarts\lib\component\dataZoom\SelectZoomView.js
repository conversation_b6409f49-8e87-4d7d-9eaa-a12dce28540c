"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const DataZoomView = require("./DataZoomView.js");
var SelectDataZoomView = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(SelectDataZoomView2, _super);
    function SelectDataZoomView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = SelectDataZoomView2.type;
      return _this;
    }
    SelectDataZoomView2.type = "dataZoom.select";
    return SelectDataZoomView2;
  }(DataZoomView.default)
);
exports.default = SelectDataZoomView;

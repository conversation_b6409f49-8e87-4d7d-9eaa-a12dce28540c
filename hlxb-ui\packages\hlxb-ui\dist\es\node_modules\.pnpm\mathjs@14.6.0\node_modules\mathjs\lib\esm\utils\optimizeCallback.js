import typedFunction from "../../../../../../typed-function@4.2.1/node_modules/typed-function/lib/esm/typed-function.js";
import { arraySize, get } from "./array.js";
import { typeOf } from "./is.js";
function optimizeCallback(callback, array, name) {
  var isUnary = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
  if (typedFunction.isTypedFunction(callback)) {
    var numberOfArguments;
    if (isUnary) {
      numberOfArguments = 1;
    } else {
      var firstIndex = (array.isMatrix ? array.size() : arraySize(array)).map(() => 0);
      var firstValue = array.isMatrix ? array.get(firstIndex) : get(array, firstIndex);
      numberOfArguments = _findNumberOfArgumentsTyped(callback, firstValue, firstIndex, array);
    }
    var fastCallback;
    if (array.isMatrix && array.dataType !== "mixed" && array.dataType !== void 0) {
      var singleSignature = _findSingleSignatureWithArity(callback, numberOfArguments);
      fastCallback = singleSignature !== void 0 ? singleSignature : callback;
    } else {
      fastCallback = callback;
    }
    if (numberOfArguments >= 1 && numberOfArguments <= 3) {
      return {
        isUnary: numberOfArguments === 1,
        fn: function fn() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          return _tryFunctionWithArgs(fastCallback, args.slice(0, numberOfArguments), name, callback.name);
        }
      };
    }
    return {
      isUnary: false,
      fn: function fn() {
        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
          args[_key2] = arguments[_key2];
        }
        return _tryFunctionWithArgs(fastCallback, args, name, callback.name);
      }
    };
  }
  if (isUnary === void 0) {
    return {
      isUnary: _findIfCallbackIsUnary(callback),
      fn: callback
    };
  } else {
    return {
      isUnary,
      fn: callback
    };
  }
}
function _findSingleSignatureWithArity(callback, arity) {
  var matchingFunctions = [];
  Object.entries(callback.signatures).forEach((_ref) => {
    var [signature, func] = _ref;
    if (signature.split(",").length === arity) {
      matchingFunctions.push(func);
    }
  });
  if (matchingFunctions.length === 1) {
    return matchingFunctions[0];
  }
}
function _findIfCallbackIsUnary(callback) {
  if (callback.length !== 1) return false;
  var callbackStr = callback.toString();
  if (/arguments/.test(callbackStr)) return false;
  var paramsStr = callbackStr.match(/\(.*?\)/);
  if (/\.\.\./.test(paramsStr)) return false;
  return true;
}
function _findNumberOfArgumentsTyped(callback, value, index, array) {
  var testArgs = [value, index, array];
  for (var i = 3; i > 0; i--) {
    var args = testArgs.slice(0, i);
    if (typedFunction.resolve(callback, args) !== null) {
      return i;
    }
  }
}
function _tryFunctionWithArgs(func, args, mappingFnName, callbackName) {
  try {
    return func(...args);
  } catch (err) {
    _createCallbackError(err, args, mappingFnName, callbackName);
  }
}
function _createCallbackError(err, args, mappingFnName, callbackName) {
  var _err$data;
  if (err instanceof TypeError && ((_err$data = err.data) === null || _err$data === void 0 ? void 0 : _err$data.category) === "wrongType") {
    var argsDesc = [];
    argsDesc.push("value: ".concat(typeOf(args[0])));
    if (args.length >= 2) {
      argsDesc.push("index: ".concat(typeOf(args[1])));
    }
    if (args.length >= 3) {
      argsDesc.push("array: ".concat(typeOf(args[2])));
    }
    throw new TypeError("Function ".concat(mappingFnName, " cannot apply callback arguments ") + "".concat(callbackName, "(").concat(argsDesc.join(", "), ") at index ").concat(JSON.stringify(args[1])));
  } else {
    throw new TypeError("Function ".concat(mappingFnName, " cannot apply callback arguments ") + "to function ".concat(callbackName, ": ").concat(err.message));
  }
}
export {
  optimizeCallback
};

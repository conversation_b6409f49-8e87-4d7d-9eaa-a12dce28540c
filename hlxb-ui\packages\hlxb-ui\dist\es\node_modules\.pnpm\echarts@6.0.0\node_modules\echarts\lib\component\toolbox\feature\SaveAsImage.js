import { __extends } from "../../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import env from "../../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/env.js";
import { ToolboxFeature } from "../featureManager.js";
import tokens from "../../../visual/tokens.js";
var SaveAsImage = (
  /** @class */
  function(_super) {
    __extends(SaveAsImage2, _super);
    function SaveAsImage2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    SaveAsImage2.prototype.onclick = function(ecModel, api) {
      var model = this.model;
      var title = model.get("name") || ecModel.get("title.0.text") || "echarts";
      var isSvg = api.getZr().painter.getType() === "svg";
      var type = isSvg ? "svg" : model.get("type", true) || "png";
      var url = api.getConnectedDataURL({
        type,
        backgroundColor: model.get("backgroundColor", true) || ecModel.get("backgroundColor") || tokens.color.neutral00,
        connectedBackgroundColor: model.get("connectedBackgroundColor"),
        excludeComponents: model.get("excludeComponents"),
        pixelRatio: model.get("pixelRatio")
      });
      var browser = env.browser;
      if (typeof MouseEvent === "function" && (browser.newEdge || !browser.ie && !browser.edge)) {
        var $a = document.createElement("a");
        $a.download = title + "." + type;
        $a.target = "_blank";
        $a.href = url;
        var evt = new MouseEvent("click", {
          // some micro front-end framework， window maybe is a Proxy
          view: document.defaultView,
          bubbles: true,
          cancelable: false
        });
        $a.dispatchEvent(evt);
      } else {
        if (window.navigator.msSaveOrOpenBlob || isSvg) {
          var parts = url.split(",");
          var base64Encoded = parts[0].indexOf("base64") > -1;
          var bstr = isSvg ? decodeURIComponent(parts[1]) : parts[1];
          base64Encoded && (bstr = window.atob(bstr));
          var filename = title + "." + type;
          if (window.navigator.msSaveOrOpenBlob) {
            var n = bstr.length;
            var u8arr = new Uint8Array(n);
            while (n--) {
              u8arr[n] = bstr.charCodeAt(n);
            }
            var blob = new Blob([u8arr]);
            window.navigator.msSaveOrOpenBlob(blob, filename);
          } else {
            var frame = document.createElement("iframe");
            document.body.appendChild(frame);
            var cw = frame.contentWindow;
            var doc = cw.document;
            doc.open("image/svg+xml", "replace");
            doc.write(bstr);
            doc.close();
            cw.focus();
            doc.execCommand("SaveAs", true, filename);
            document.body.removeChild(frame);
          }
        } else {
          var lang = model.get("lang");
          var html = '<body style="margin:0;"><img src="' + url + '" style="max-width:100%;" title="' + (lang && lang[0] || "") + '" /></body>';
          var tab = window.open();
          tab.document.write(html);
          tab.document.title = title;
        }
      }
    };
    SaveAsImage2.getDefaultOption = function(ecModel) {
      var defaultOption = {
        show: true,
        icon: "M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",
        title: ecModel.getLocaleModel().get(["toolbox", "saveAsImage", "title"]),
        type: "png",
        // Default use option.backgroundColor
        // backgroundColor: '#fff',
        connectedBackgroundColor: tokens.color.neutral00,
        name: "",
        excludeComponents: ["toolbox"],
        // use current pixel ratio of device by default
        // pixelRatio: 1,
        lang: ecModel.getLocaleModel().get(["toolbox", "saveAsImage", "lang"])
      };
      return defaultOption;
    };
    return SaveAsImage2;
  }(ToolboxFeature)
);
export {
  SaveAsImage as default
};

import dayjs from "../../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js";
import { merge } from "lodash-es";
import { ref, computed, provide, inject } from "vue";
const useProvideCalendarContext = (props) => {
  var _a;
  const isExpanded = ref(false);
  const currentDate = ref(((_a = props.selectedDate) == null ? void 0 : _a.startOf("month")) || dayjs());
  const options = computed(() => {
    return merge(
      // 默认配置
      {
        showToday: true,
        selectable: true,
        wheelMonthChange: true,
        showExpandButton: true,
        fillPrevNextMonth: true,
        autoCollapseAfterDateSelect: true,
        autoExpandAfterMonthChange: true,
        wheelSensitivity: 150,
        cellClass: () => "",
        cellContent: () => "",
        hasIndicator: () => false,
        indicatorConfig: {
          color: "#fc7c22",
          size: 4,
          position: "bottom-center",
          style: {}
        }
      },
      props.options || {}
    );
  });
  const calendarContext = computed(() => ({
    options: options.value,
    isExpanded: isExpanded.value,
    currentDate: currentDate.value,
    selectedDate: props.selectedDate || dayjs()
  }));
  provide("calendarContext", calendarContext);
  return {
    options,
    isExpanded,
    currentDate,
    calendarContext
  };
};
const useCalendarContext = () => {
  const calendarContext = inject("calendarContext");
  if (!calendarContext) {
    throw new Error("请在HCalendar子组件中使用useCalendarContext");
  }
  const options = computed(() => {
    var _a;
    return (_a = calendarContext.value) == null ? void 0 : _a.options;
  });
  return {
    calendarContext,
    options
  };
};
export {
  useCalendarContext,
  useProvideCalendarContext
};

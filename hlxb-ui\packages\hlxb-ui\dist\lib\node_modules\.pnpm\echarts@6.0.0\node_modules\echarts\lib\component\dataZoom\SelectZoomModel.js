"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const DataZoomModel = require("./DataZoomModel.js");
var SelectDataZoomModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(SelectDataZoomModel2, _super);
    function SelectDataZoomModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = SelectDataZoomModel2.type;
      return _this;
    }
    SelectDataZoomModel2.type = "dataZoom.select";
    return SelectDataZoomModel2;
  }(DataZoomModel.default)
);
exports.default = SelectDataZoomModel;

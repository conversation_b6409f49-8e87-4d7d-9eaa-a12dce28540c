import { isArray, each as each$1, isObject } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
var each = each$1;
function visualMapPreprocessor(option) {
  var visualMap = option && option.visualMap;
  if (!isArray(visualMap)) {
    visualMap = visualMap ? [visualMap] : [];
  }
  each(visualMap, function(opt) {
    if (!opt) {
      return;
    }
    if (has(opt, "splitList") && !has(opt, "pieces")) {
      opt.pieces = opt.splitList;
      delete opt.splitList;
    }
    var pieces = opt.pieces;
    if (pieces && isArray(pieces)) {
      each(pieces, function(piece) {
        if (isObject(piece)) {
          if (has(piece, "start") && !has(piece, "min")) {
            piece.min = piece.start;
          }
          if (has(piece, "end") && !has(piece, "max")) {
            piece.max = piece.end;
          }
        }
      });
    }
  });
}
function has(obj, name) {
  return obj && obj.hasOwnProperty && obj.hasOwnProperty(name);
}
export {
  visualMapPreprocessor as default
};

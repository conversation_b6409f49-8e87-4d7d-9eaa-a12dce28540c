import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentView from "../../view/Component.js";
import { createHashMap, each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import MarkerModel from "./MarkerModel.js";
import { makeInner } from "../../util/model.js";
import { enterBlur, leaveBlur } from "../../util/states.js";
import { retrieveZInfo, traverseUpdateZ } from "../../util/graphic.js";
var inner = makeInner();
var MarkerView = (
  /** @class */
  function(_super) {
    __extends(MarkerView2, _super);
    function MarkerView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkerView2.type;
      return _this;
    }
    MarkerView2.prototype.init = function() {
      this.markerGroupMap = createHashMap();
    };
    MarkerView2.prototype.render = function(markerModel, ecModel, api) {
      var _this = this;
      var markerGroupMap = this.markerGroupMap;
      markerGroupMap.each(function(item) {
        inner(item).keep = false;
      });
      ecModel.eachSeries(function(seriesModel) {
        var markerModel2 = MarkerModel.getMarkerModelFromSeries(seriesModel, _this.type);
        markerModel2 && _this.renderSeries(seriesModel, markerModel2, ecModel, api);
      });
      markerGroupMap.each(function(item) {
        !inner(item).keep && _this.group.remove(item.group);
      });
      updateZ(ecModel, markerGroupMap, this.type);
    };
    MarkerView2.prototype.markKeep = function(drawGroup) {
      inner(drawGroup).keep = true;
    };
    MarkerView2.prototype.toggleBlurSeries = function(seriesModelList, isBlur) {
      var _this = this;
      each(seriesModelList, function(seriesModel) {
        var markerModel = MarkerModel.getMarkerModelFromSeries(seriesModel, _this.type);
        if (markerModel) {
          var data = markerModel.getData();
          data.eachItemGraphicEl(function(el) {
            if (el) {
              isBlur ? enterBlur(el) : leaveBlur(el);
            }
          });
        }
      });
    };
    MarkerView2.type = "marker";
    return MarkerView2;
  }(ComponentView)
);
function updateZ(ecModel, markerGroupMap, type) {
  ecModel.eachSeries(function(seriesModel) {
    var markerModel = MarkerModel.getMarkerModelFromSeries(seriesModel, type);
    var markerDraw = markerGroupMap.get(seriesModel.id);
    if (markerModel && markerDraw && markerDraw.group) {
      var _a = retrieveZInfo(markerModel), z = _a.z, zlevel = _a.zlevel;
      traverseUpdateZ(markerDraw.group, z, zlevel);
    }
  });
}
export {
  MarkerView as default
};

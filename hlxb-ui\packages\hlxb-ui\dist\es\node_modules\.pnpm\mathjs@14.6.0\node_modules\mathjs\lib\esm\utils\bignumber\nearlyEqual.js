function nearlyEqual(a, b) {
  var relTol = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1e-9;
  var absTol = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;
  if (relTol <= 0) {
    throw new Error("Relative tolerance must be greater than 0");
  }
  if (absTol < 0) {
    throw new Error("Absolute tolerance must be at least 0");
  }
  if (a.isNaN() || b.isNaN()) {
    return false;
  }
  if (!a.isFinite() || !b.isFinite()) {
    return a.eq(b);
  }
  if (a.eq(b)) {
    return true;
  }
  return a.minus(b).abs().lte(a.constructor.max(a.constructor.max(a.abs(), b.abs()).mul(relTol), absTol));
}
export {
  nearlyEqual
};

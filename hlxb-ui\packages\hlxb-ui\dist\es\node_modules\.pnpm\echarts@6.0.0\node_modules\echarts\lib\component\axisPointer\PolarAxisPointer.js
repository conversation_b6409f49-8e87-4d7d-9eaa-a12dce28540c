import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import BaseAxisPointer from "./BaseAxisPointer.js";
import { applyTransform } from "../../util/graphic.js";
import { buildElStyle, buildLabelElOption, makeSectorShape, makeLineShape } from "./viewHelper.js";
import { rotate, create, translate } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/matrix.js";
import AxisBuilder from "../axis/AxisBuilder.js";
var PolarAxisPointer = (
  /** @class */
  function(_super) {
    __extends(PolarAxisPointer2, _super);
    function PolarAxisPointer2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    PolarAxisPointer2.prototype.makeElOption = function(elOption, value, axisModel, axisPointerModel, api) {
      var axis = axisModel.axis;
      if (axis.dim === "angle") {
        this.animationThreshold = Math.PI / 18;
      }
      var polar = axis.polar;
      var otherAxis = polar.getOtherAxis(axis);
      var otherExtent = otherAxis.getExtent();
      var coordValue = axis.dataToCoord(value);
      var axisPointerType = axisPointerModel.get("type");
      if (axisPointerType && axisPointerType !== "none") {
        var elStyle = buildElStyle(axisPointerModel);
        var pointerOption = pointerShapeBuilder[axisPointerType](axis, polar, coordValue, otherExtent);
        pointerOption.style = elStyle;
        elOption.graphicKey = pointerOption.type;
        elOption.pointer = pointerOption;
      }
      var labelMargin = axisPointerModel.get(["label", "margin"]);
      var labelPos = getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin);
      buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos);
    };
    return PolarAxisPointer2;
  }(BaseAxisPointer)
);
function getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin) {
  var axis = axisModel.axis;
  var coord = axis.dataToCoord(value);
  var axisAngle = polar.getAngleAxis().getExtent()[0];
  axisAngle = axisAngle / 180 * Math.PI;
  var radiusExtent = polar.getRadiusAxis().getExtent();
  var position;
  var align;
  var verticalAlign;
  if (axis.dim === "radius") {
    var transform = create();
    rotate(transform, transform, axisAngle);
    translate(transform, transform, [polar.cx, polar.cy]);
    position = applyTransform([coord, -labelMargin], transform);
    var labelRotation = axisModel.getModel("axisLabel").get("rotate") || 0;
    var labelLayout = AxisBuilder.innerTextLayout(axisAngle, labelRotation * Math.PI / 180, -1);
    align = labelLayout.textAlign;
    verticalAlign = labelLayout.textVerticalAlign;
  } else {
    var r = radiusExtent[1];
    position = polar.coordToPoint([r + labelMargin, coord]);
    var cx = polar.cx;
    var cy = polar.cy;
    align = Math.abs(position[0] - cx) / r < 0.3 ? "center" : position[0] > cx ? "left" : "right";
    verticalAlign = Math.abs(position[1] - cy) / r < 0.3 ? "middle" : position[1] > cy ? "top" : "bottom";
  }
  return {
    position,
    align,
    verticalAlign
  };
}
var pointerShapeBuilder = {
  line: function(axis, polar, coordValue, otherExtent) {
    return axis.dim === "angle" ? {
      type: "Line",
      shape: makeLineShape(polar.coordToPoint([otherExtent[0], coordValue]), polar.coordToPoint([otherExtent[1], coordValue]))
    } : {
      type: "Circle",
      shape: {
        cx: polar.cx,
        cy: polar.cy,
        r: coordValue
      }
    };
  },
  shadow: function(axis, polar, coordValue, otherExtent) {
    var bandWidth = Math.max(1, axis.getBandWidth());
    var radian = Math.PI / 180;
    return axis.dim === "angle" ? {
      type: "Sector",
      shape: makeSectorShape(
        polar.cx,
        polar.cy,
        otherExtent[0],
        otherExtent[1],
        // In ECharts y is negative if angle is positive
        (-coordValue - bandWidth / 2) * radian,
        (-coordValue + bandWidth / 2) * radian
      )
    } : {
      type: "Sector",
      shape: makeSectorShape(polar.cx, polar.cy, coordValue - bandWidth / 2, coordValue + bandWidth / 2, 0, Math.PI * 2)
    };
  }
};
export {
  PolarAxisPointer as default
};

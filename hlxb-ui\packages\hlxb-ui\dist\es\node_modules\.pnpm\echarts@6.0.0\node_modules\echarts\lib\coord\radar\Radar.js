import IndicatorAxis from "./IndicatorAxis.js";
import IntervalScale from "../../scale/Interval.js";
import { parsePercent } from "../../util/number.js";
import { each, map, isString, isNumber } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { alignScaleTicks } from "../axisAlignTicks.js";
import { createBoxLayoutReference } from "../../util/layout.js";
var Radar = (
  /** @class */
  function() {
    function Radar2(radarModel, ecModel, api) {
      this.dimensions = [];
      this._model = radarModel;
      this._indicatorAxes = map(radarModel.getIndicatorModels(), function(indicatorModel, idx) {
        var dim = "indicator_" + idx;
        var indicatorAxis = new IndicatorAxis(
          dim,
          new IntervalScale()
          // (indicatorModel.get('axisType') === 'log') ? new LogScale() : new IntervalScale()
        );
        indicatorAxis.name = indicatorModel.get("name");
        indicatorAxis.model = indicatorModel;
        indicatorModel.axis = indicatorAxis;
        this.dimensions.push(dim);
        return indicatorAxis;
      }, this);
      this.resize(radarModel, api);
    }
    Radar2.prototype.getIndicatorAxes = function() {
      return this._indicatorAxes;
    };
    Radar2.prototype.dataToPoint = function(value, indicatorIndex) {
      var indicatorAxis = this._indicatorAxes[indicatorIndex];
      return this.coordToPoint(indicatorAxis.dataToCoord(value), indicatorIndex);
    };
    Radar2.prototype.coordToPoint = function(coord, indicatorIndex) {
      var indicatorAxis = this._indicatorAxes[indicatorIndex];
      var angle = indicatorAxis.angle;
      var x = this.cx + coord * Math.cos(angle);
      var y = this.cy - coord * Math.sin(angle);
      return [x, y];
    };
    Radar2.prototype.pointToData = function(pt) {
      var dx = pt[0] - this.cx;
      var dy = pt[1] - this.cy;
      var radius = Math.sqrt(dx * dx + dy * dy);
      dx /= radius;
      dy /= radius;
      var radian = Math.atan2(-dy, dx);
      var minRadianDiff = Infinity;
      var closestAxis;
      var closestAxisIdx = -1;
      for (var i = 0; i < this._indicatorAxes.length; i++) {
        var indicatorAxis = this._indicatorAxes[i];
        var diff = Math.abs(radian - indicatorAxis.angle);
        if (diff < minRadianDiff) {
          closestAxis = indicatorAxis;
          closestAxisIdx = i;
          minRadianDiff = diff;
        }
      }
      return [closestAxisIdx, +(closestAxis && closestAxis.coordToData(radius))];
    };
    Radar2.prototype.resize = function(radarModel, api) {
      var refContainer = createBoxLayoutReference(radarModel, api).refContainer;
      var center = radarModel.get("center");
      var viewSize = Math.min(refContainer.width, refContainer.height) / 2;
      this.cx = parsePercent(center[0], refContainer.width) + refContainer.x;
      this.cy = parsePercent(center[1], refContainer.height) + refContainer.y;
      this.startAngle = radarModel.get("startAngle") * Math.PI / 180;
      var radius = radarModel.get("radius");
      if (isString(radius) || isNumber(radius)) {
        radius = [0, radius];
      }
      this.r0 = parsePercent(radius[0], viewSize);
      this.r = parsePercent(radius[1], viewSize);
      each(this._indicatorAxes, function(indicatorAxis, idx) {
        indicatorAxis.setExtent(this.r0, this.r);
        var angle = this.startAngle + idx * Math.PI * 2 / this._indicatorAxes.length;
        angle = Math.atan2(Math.sin(angle), Math.cos(angle));
        indicatorAxis.angle = angle;
      }, this);
    };
    Radar2.prototype.update = function(ecModel, api) {
      var indicatorAxes = this._indicatorAxes;
      var radarModel = this._model;
      each(indicatorAxes, function(indicatorAxis) {
        indicatorAxis.scale.setExtent(Infinity, -Infinity);
      });
      ecModel.eachSeriesByType("radar", function(radarSeries, idx) {
        if (radarSeries.get("coordinateSystem") !== "radar" || ecModel.getComponent("radar", radarSeries.get("radarIndex")) !== radarModel) {
          return;
        }
        var data = radarSeries.getData();
        each(indicatorAxes, function(indicatorAxis) {
          indicatorAxis.scale.unionExtentFromData(data, data.mapDimension(indicatorAxis.dim));
        });
      }, this);
      var splitNumber = radarModel.get("splitNumber");
      var dummyScale = new IntervalScale();
      dummyScale.setExtent(0, splitNumber);
      dummyScale.setInterval(1);
      each(indicatorAxes, function(indicatorAxis, idx) {
        alignScaleTicks(indicatorAxis.scale, indicatorAxis.model, dummyScale);
      });
    };
    Radar2.prototype.convertToPixel = function(ecModel, finder, value) {
      console.warn("Not implemented.");
      return null;
    };
    Radar2.prototype.convertFromPixel = function(ecModel, finder, pixel) {
      console.warn("Not implemented.");
      return null;
    };
    Radar2.prototype.containPoint = function(point) {
      console.warn("Not implemented.");
      return false;
    };
    Radar2.create = function(ecModel, api) {
      var radarList = [];
      ecModel.eachComponent("radar", function(radarModel) {
        var radar = new Radar2(radarModel, ecModel, api);
        radarList.push(radar);
        radarModel.coordinateSystem = radar;
      });
      ecModel.eachSeriesByType("radar", function(radarSeries) {
        if (radarSeries.get("coordinateSystem") === "radar") {
          radarSeries.coordinateSystem = radarList[radarSeries.get("radarIndex") || 0];
        }
      });
      return radarList;
    };
    Radar2.dimensions = [];
    return Radar2;
  }()
);
export {
  Radar as default
};

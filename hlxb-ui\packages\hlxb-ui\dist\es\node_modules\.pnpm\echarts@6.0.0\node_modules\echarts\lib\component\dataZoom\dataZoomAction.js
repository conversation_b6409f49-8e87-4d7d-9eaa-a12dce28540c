import { findEffectedDataZooms } from "./helper.js";
import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function installDataZoomAction(registers) {
  registers.registerAction("dataZoom", function(payload, ecModel) {
    var effectedModels = findEffectedDataZooms(ecModel, payload);
    each(effectedModels, function(dataZoomModel) {
      dataZoomModel.setRawRange({
        start: payload.start,
        end: payload.end,
        startValue: payload.startValue,
        endValue: payload.endValue
      });
    });
  });
}
export {
  installDataZoomAction as default
};

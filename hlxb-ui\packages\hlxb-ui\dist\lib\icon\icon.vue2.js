"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
require("../assets/js/iconfont.js");
const index = require("../config/index.js");
const _hoisted_1 = ["xlink:href"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{ name: "HlxbIcon" },
  __name: "icon",
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const prefixCls = index.getPrefixCls("icon");
    const styleClass = vue.computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const IconName = vue.computed(() => `#icon-${props.name}`);
    const handlerClick = () => {
      emit("click");
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("svg", {
        class: vue.normalizeClass(styleClass.value),
        "aria-hidden": "true",
        onClick: handlerClick
      }, [
        vue.createElementVNode("use", { "xlink:href": IconName.value }, null, 8, _hoisted_1)
      ], 2);
    };
  }
});
exports.default = _sfc_main;

import { use } from "../../extension.js";
import ScatterSeriesModel from "./ScatterSeries.js";
import ScatterView from "./ScatterView.js";
import { install as install$1 } from "../../component/grid/installSimple.js";
import pointsLayout from "../../layout/points.js";
function install(registers) {
  use(install$1);
  registers.registerSeriesModel(ScatterSeriesModel);
  registers.registerChartView(ScatterView);
  registers.registerLayout(pointsLayout("scatter"));
}
export {
  install
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { getBoundingRect } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/contain/text.js";
import Axis from "../Axis.js";
import { makeInner } from "../../util/model.js";
var inner = makeInner();
var AngleAxis = (
  /** @class */
  function(_super) {
    __extends(AngleAxis2, _super);
    function AngleAxis2(scale, angleExtent) {
      return _super.call(this, "angle", scale, angleExtent || [0, 360]) || this;
    }
    AngleAxis2.prototype.pointToData = function(point, clamp) {
      return this.polar.pointToData(point, clamp)[this.dim === "radius" ? 0 : 1];
    };
    AngleAxis2.prototype.calculateCategoryInterval = function() {
      var axis = this;
      var labelModel = axis.getLabelModel();
      var ordinalScale = axis.scale;
      var ordinalExtent = ordinalScale.getExtent();
      var tickCount = ordinalScale.count();
      if (ordinalExtent[1] - ordinalExtent[0] < 1) {
        return 0;
      }
      var tickValue = ordinalExtent[0];
      var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);
      var unitH = Math.abs(unitSpan);
      var rect = getBoundingRect(tickValue == null ? "" : tickValue + "", labelModel.getFont(), "center", "top");
      var maxH = Math.max(rect.height, 7);
      var dh = maxH / unitH;
      isNaN(dh) && (dh = Infinity);
      var interval = Math.max(0, Math.floor(dh));
      var cache = inner(axis.model);
      var lastAutoInterval = cache.lastAutoInterval;
      var lastTickCount = cache.lastTickCount;
      if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1 && lastAutoInterval > interval) {
        interval = lastAutoInterval;
      } else {
        cache.lastTickCount = tickCount;
        cache.lastAutoInterval = interval;
      }
      return interval;
    };
    return AngleAxis2;
  }(Axis)
);
AngleAxis.prototype.dataToAngle = Axis.prototype.dataToCoord;
AngleAxis.prototype.angleToData = Axis.prototype.coordToData;
export {
  AngleAxis as default
};

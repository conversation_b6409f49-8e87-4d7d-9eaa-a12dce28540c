import { defineComponent, inject, computed, createElementBlock, openBlock, normalizeClass, createElementVNode, withDirectives, vModelRadio, toDisplayString } from "vue";
import { getPrefixCls } from "../config/index.js";
const _hoisted_1 = { class: "r-radio--outer" };
const _hoisted_2 = ["value"];
const _hoisted_3 = { class: "r-radio--label" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{ name: "HlxbRadio" },
  __name: "radio",
  props: {
    modelValue: {
      type: [String, Number]
    },
    label: [String, Number]
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const radioGroup = inject("RadioGroup", null);
    const prefixCls = getPrefixCls("radio");
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const isGroup = computed(() => radioGroup !== null);
    const radioValue = computed({
      get() {
        return isGroup.value ? radioGroup.modelValue : props.modelValue;
      },
      set(val) {
        if (isGroup.value) {
          radioGroup.$emit("update:modelValue", val);
        } else {
          emits("update:modelValue", val);
        }
      }
    });
    const checked = computed(
      () => props.modelValue === props.label || radioGroup && radioGroup.modelValue === props.label
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("label", {
        class: normalizeClass(["r-radio", styleClass.value])
      }, [
        createElementVNode("span", _hoisted_1, [
          createElementVNode("span", {
            class: normalizeClass(["r-radio--inner", { "is-checked": checked.value }])
          }, null, 2),
          withDirectives(createElementVNode("input", {
            class: "r-radio-native",
            type: "radio",
            value: __props.label,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => radioValue.value = $event)
          }, null, 8, _hoisted_2), [
            [vModelRadio, radioValue.value]
          ])
        ]),
        createElementVNode("span", _hoisted_3, toDisplayString(__props.label), 1)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

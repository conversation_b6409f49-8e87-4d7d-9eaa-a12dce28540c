import { factory } from "../../utils/factory.js";
import { deepMap } from "../../utils/collection.js";
var name = "conj";
var dependencies = ["typed"];
var createConj = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed
  } = _ref;
  return typed(name, {
    "number | BigNumber | Fraction": (x) => x,
    Complex: (x) => x.conjugate(),
    Unit: typed.referToSelf((self) => (x) => new x.constructor(self(x.toNumeric()), x.formatUnits())),
    "Array | Matrix": typed.referToSelf((self) => (x) => deepMap(x, self))
  });
});
export {
  createConj
};

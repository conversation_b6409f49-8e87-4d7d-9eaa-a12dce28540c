import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Path from "../Path.js";
import { buildPath } from "../helper/poly.js";
var PolylineShape = /* @__PURE__ */ function() {
  function PolylineShape2() {
    this.points = null;
    this.percent = 1;
    this.smooth = 0;
    this.smoothConstraint = null;
  }
  return PolylineShape2;
}();
var Polyline = function(_super) {
  __extends(Polyline2, _super);
  function Polyline2(opts) {
    return _super.call(this, opts) || this;
  }
  Polyline2.prototype.getDefaultStyle = function() {
    return {
      stroke: "#000",
      fill: null
    };
  };
  Polyline2.prototype.getDefaultShape = function() {
    return new PolylineShape();
  };
  Polyline2.prototype.buildPath = function(ctx, shape) {
    buildPath(ctx, shape, false);
  };
  return Polyline2;
}(Path);
Polyline.prototype.type = "polyline";
export {
  PolylineShape,
  Polyline as default
};

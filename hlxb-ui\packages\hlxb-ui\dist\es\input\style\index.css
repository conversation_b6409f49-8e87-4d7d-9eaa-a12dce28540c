.hlxb-input.inline-container {
  width: 100%;
  display: inline-flex;
}
.hlxb-input.inline-container .r-input {
  appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
.hlxb-input.inline-container .r-input.is-disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
.hlxb-input.inline-container .r-input.is-center {
  text-align: center;
}
.hlxb-input.inline-container .r-input:hover {
  border-color: #c0c4cc;
}
.hlxb-input.inline-container .r-input:focus {
  outline: none;
  border-color: #409eff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar {
  z-index: 11;
  width: 6px;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar:horizontal {
  height: 6px;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 6px;
  background: #b4bccc;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-corner {
  background: #fff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-track {
  background: #fff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 6px;
}
.hlxb-input.inline-container .r-textarea {
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.hlxb-input.inline-container .r-textarea:focus {
  outline: none;
  border-color: #409eff;
}
.hlxb-input.inline-container .r-input-prepend,
.hlxb-input.inline-container .r-input-append {
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 0;
  padding: 0 10px;
  white-space: nowrap;
  width: max-content;
}
.hlxb-input.inline-container.has-prepend .r-input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
.hlxb-input.inline-container.has-append .r-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
.hlxb-input.inline-container.has-prepend .r-input-prepend {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.hlxb-input.inline-container.has-prepend .r-input-append {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.hlxb-input.inline-container.has-append .r-input-append {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.hlxb-input.inline-container .r-input--medium {
  height: 36px;
  line-height: 36px;
}
.hlxb-input.inline-container .r-input--small {
  height: 32px;
  line-height: 32px;
}
.hlxb-input.inline-container .r-input-outer {
  width: 100%;
  position: relative;
}
.hlxb-input.inline-container .r-input-clear {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  cursor: pointer;
}

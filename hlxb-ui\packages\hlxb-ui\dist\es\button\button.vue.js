import { defineComponent, computed, resolveComponent, createElementBlock, openBlock, normalizeClass, createBlock, createCommentVNode, renderSlot } from "vue";
import { getPrefixCls } from "../config/index.js";
const _hoisted_1 = ["disabled", "icon"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{ name: "HlxbButton" },
  __name: "button",
  props: {
    type: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 圆角
    round: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: ""
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const prefixCls = getPrefixCls("button");
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true,
        [`r-button--${props.type}`]: props.type,
        "is-round": props.round,
        "is-disabled": props.disabled
      };
    });
    const handleClick = () => {
      emit("click");
    };
    return (_ctx, _cache) => {
      const _component_hlxb_icon = resolveComponent("hlxb-icon");
      return openBlock(), createElementBlock("button", {
        class: normalizeClass(["r-button", styleClass.value]),
        disabled: __props.disabled,
        icon: __props.icon,
        onClick: handleClick
      }, [
        __props.icon ? (openBlock(), createBlock(_component_hlxb_icon, {
          key: 0,
          name: __props.icon
        }, null, 8, ["name"])) : createCommentVNode("", true),
        renderSlot(_ctx.$slots, "default")
      ], 10, _hoisted_1);
    };
  }
});
export {
  _sfc_main as default
};

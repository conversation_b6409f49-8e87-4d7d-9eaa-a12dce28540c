import __vite_glob_0_0 from "./assets/icons/clear-night.svg.js";
import __vite_glob_0_1 from "./assets/icons/cloudy.svg.js";
import __vite_glob_0_2 from "./assets/icons/foggy.svg.js";
import __vite_glob_0_3 from "./assets/icons/partly-cloudy.svg.js";
import __vite_glob_0_4 from "./assets/icons/rainy.svg.js";
import __vite_glob_0_5 from "./assets/icons/snowy.svg.js";
import __vite_glob_0_6 from "./assets/icons/sunny.svg.js";
import __vite_glob_0_7 from "./assets/icons/thunderstorm.svg.js";
import __vite_glob_0_8 from "./assets/icons/windy.svg.js";
import { defineComponent, computed, createElementBlock, openBlock, normalizeStyle, normalizeClass, unref, createElementVNode } from "vue";
import { getPrefixCls } from "../../config/index.js";
const _hoisted_1 = ["src", "alt"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "WeatherIcon",
  props: {
    code: {},
    size: { default: 64 },
    width: {},
    height: {}
  },
  setup(__props) {
    const prefixCls = getPrefixCls("weather-icon");
    const props = __props;
    const weatherIconMap = {
      // 晴天
      "100": { icon: "sunny", alt: "晴天" },
      // 夜间晴天
      "150": { icon: "clear-night", alt: "晴夜" },
      "151": { icon: "clear-night", alt: "多云夜" },
      "152": { icon: "clear-night", alt: "少云夜" },
      "153": { icon: "clear-night", alt: "晴间多云夜" },
      // 多云
      "101": { icon: "partly-cloudy", alt: "多云" },
      "102": { icon: "partly-cloudy", alt: "少云" },
      "103": { icon: "partly-cloudy", alt: "晴间多云" },
      // 阴天
      "104": { icon: "cloudy", alt: "阴" },
      // 雨天
      "300": { icon: "rainy", alt: "阵雨" },
      "301": { icon: "rainy", alt: "强阵雨" },
      "302": { icon: "thunderstorm", alt: "雷阵雨" },
      "303": { icon: "thunderstorm", alt: "强雷阵雨" },
      "304": { icon: "thunderstorm", alt: "雷阵雨伴有冰雹" },
      "305": { icon: "rainy", alt: "小雨" },
      "306": { icon: "rainy", alt: "中雨" },
      "307": { icon: "rainy", alt: "大雨" },
      "308": { icon: "rainy", alt: "极端降雨" },
      "309": { icon: "rainy", alt: "毛毛雨" },
      "310": { icon: "rainy", alt: "暴雨" },
      "311": { icon: "rainy", alt: "大暴雨" },
      "312": { icon: "rainy", alt: "特大暴雨" },
      "313": { icon: "rainy", alt: "冻雨" },
      // 雪天
      "400": { icon: "snowy", alt: "小雪" },
      "401": { icon: "snowy", alt: "中雪" },
      "402": { icon: "snowy", alt: "大雪" },
      "403": { icon: "snowy", alt: "暴雪" },
      "404": { icon: "snowy", alt: "雨夹雪" },
      "405": { icon: "snowy", alt: "雨雪天气" },
      "406": { icon: "snowy", alt: "阵雨夹雪" },
      "407": { icon: "snowy", alt: "阵雪" },
      // 雾霾
      "500": { icon: "foggy", alt: "薄雾" },
      "501": { icon: "foggy", alt: "雾" },
      "502": { icon: "foggy", alt: "霾" },
      "503": { icon: "foggy", alt: "扬沙" },
      "504": { icon: "foggy", alt: "浮尘" },
      "507": { icon: "foggy", alt: "沙尘暴" },
      "508": { icon: "foggy", alt: "强沙尘暴" },
      // 大风
      "900": { icon: "windy", alt: "热" },
      "901": { icon: "windy", alt: "冷" },
      "999": { icon: "cloudy", alt: "未知" }
    };
    const iconSrc = computed(() => {
      const code = String(props.code);
      const iconInfo = weatherIconMap[code] || weatherIconMap["999"];
      return new URL((/* @__PURE__ */ Object.assign({ "./assets/icons/clear-night.svg": __vite_glob_0_0, "./assets/icons/cloudy.svg": __vite_glob_0_1, "./assets/icons/foggy.svg": __vite_glob_0_2, "./assets/icons/partly-cloudy.svg": __vite_glob_0_3, "./assets/icons/rainy.svg": __vite_glob_0_4, "./assets/icons/snowy.svg": __vite_glob_0_5, "./assets/icons/sunny.svg": __vite_glob_0_6, "./assets/icons/thunderstorm.svg": __vite_glob_0_7, "./assets/icons/windy.svg": __vite_glob_0_8 }))[`./assets/icons/${iconInfo.icon}.svg`], import.meta.url).href;
    });
    const iconAlt = computed(() => {
      const code = String(props.code);
      const iconInfo = weatherIconMap[code] || weatherIconMap["999"];
      return iconInfo.alt;
    });
    const handleImageError = (event) => {
      const target = event.target;
      target.src = new URL("data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20fill='none'%20version='1.1'%20width='100'%20height='100'%20viewBox='0%200%20100%20100'%3e%3cdefs%3e%3cfilter%20id='master_svg0_1004_26213'%20filterUnits='objectBoundingBox'%20color-interpolation-filters='sRGB'%20x='0'%20y='0'%20width='1'%20height='1'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='BackgroundImageFix'%20result='shape'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20result='hardAlpha'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'/%3e%3cfeOffset%20dy='11'%20dx='0'/%3e%3cfeGaussianBlur%20stdDeviation='5'/%3e%3cfeComposite%20in2='hardAlpha'%20operator='arithmetic'%20k2='-1'%20k3='1'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%201%200%200%200%200%201%200%200%200%200%201%200%200%200%201%200'/%3e%3cfeBlend%20mode='normal'%20in2='shape'%20result='effect1_innerShadow'/%3e%3c/filter%3e%3clinearGradient%20x1='0.9363957643508911'%20y1='0.9107142090797424'%20x2='-0.3792312463635389'%20y2='0.2866577914504117'%20id='master_svg1_1004_25313'%3e%3cstop%20offset='0%25'%20stop-color='%23FFFFFF'%20stop-opacity='1'/%3e%3cstop%20offset='100%25'%20stop-color='%23FFFFFF'%20stop-opacity='0.5799999833106995'/%3e%3c/linearGradient%3e%3c/defs%3e%3cg%3e%3cg%3e%3c/g%3e%3cg%20transform='matrix(-1,0,0,1,188,0)'%20filter='url(%23master_svg0_1004_26213)'%3e%3cpath%20d='M104.1602,45.5235C103.93538,44.2042,103.81818,42.8472,103.81818,41.4621C103.81818,28.5043,114.0749,18,126.72720000000001,18C136.1332,18,144.2152,23.8054,147.7433,32.1083C150.6016,29.5811,154.3105,28.0552,158.36360000000002,28.0552C167.40089999999998,28.0552,174.72719999999998,35.6417,174.72719999999998,45C174.72719999999998,45.5212,174.7046,46.0368,174.66,46.5459C179.0069,48.7264,182,53.3018,182,58.5932C182,65.9976,176.1389,72,168.909,72C168.909,72,107.0909,72,107.0909,72C99.861,72,94,65.9976,94,58.5932C94,52.2206,98.34127,46.8865,104.1602,45.5235Z'%20fill='url(%23master_svg1_1004_25313)'%20fill-opacity='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e", import.meta.url).href;
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), "weather-icon-wrapper"]),
        style: normalizeStyle([
          "display: inline-block",
          { width: (_ctx.width ?? _ctx.size) + "px", height: (_ctx.height ?? _ctx.size) + "px" }
        ])
      }, [
        createElementVNode("img", {
          src: iconSrc.value,
          alt: iconAlt.value,
          style: { width: "100%", height: "100%", objectFit: "contain" },
          onError: handleImageError
        }, null, 40, _hoisted_1)
      ], 6);
    };
  }
});
export {
  _sfc_main as default
};

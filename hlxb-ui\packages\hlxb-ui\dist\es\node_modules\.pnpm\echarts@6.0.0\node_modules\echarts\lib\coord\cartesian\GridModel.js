import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentModel from "../../model/Component.js";
import { getLayoutParams, mergeLayoutParam } from "../../util/layout.js";
import tokens from "../../visual/tokens.js";
var OUTER_BOUNDS_DEFAULT = {
  left: 0,
  right: 0,
  top: 0,
  bottom: 0
};
var OUTER_BOUNDS_CLAMP_DEFAULT = ["25%", "25%"];
var GridModel = (
  /** @class */
  function(_super) {
    __extends(GridModel2, _super);
    function GridModel2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    GridModel2.prototype.mergeDefaultAndTheme = function(option, ecModel) {
      var outerBoundsCp = getLayoutParams(option.outerBounds);
      _super.prototype.mergeDefaultAndTheme.apply(this, arguments);
      if (outerBoundsCp && option.outerBounds) {
        mergeLayoutParam(option.outerBounds, outerBoundsCp);
      }
    };
    GridModel2.prototype.mergeOption = function(newOption, ecModel) {
      _super.prototype.mergeOption.apply(this, arguments);
      if (this.option.outerBounds && newOption.outerBounds) {
        mergeLayoutParam(this.option.outerBounds, newOption.outerBounds);
      }
    };
    GridModel2.type = "grid";
    GridModel2.dependencies = ["xAxis", "yAxis"];
    GridModel2.layoutMode = "box";
    GridModel2.defaultOption = {
      show: false,
      // zlevel: 0,
      z: 0,
      left: "15%",
      top: 65,
      right: "10%",
      bottom: 80,
      // If grid size contain label
      containLabel: false,
      outerBoundsMode: "auto",
      outerBounds: OUTER_BOUNDS_DEFAULT,
      outerBoundsContain: "all",
      outerBoundsClampWidth: OUTER_BOUNDS_CLAMP_DEFAULT[0],
      outerBoundsClampHeight: OUTER_BOUNDS_CLAMP_DEFAULT[1],
      // width: {totalWidth} - left - right,
      // height: {totalHeight} - top - bottom,
      backgroundColor: tokens.color.transparent,
      borderWidth: 1,
      borderColor: tokens.color.neutral30
    };
    return GridModel2;
  }(ComponentModel)
);
export {
  OUTER_BOUNDS_CLAMP_DEFAULT,
  OUTER_BOUNDS_DEFAULT,
  GridModel as default
};

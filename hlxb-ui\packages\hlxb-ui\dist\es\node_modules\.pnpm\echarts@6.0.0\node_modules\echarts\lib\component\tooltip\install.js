import { install as install$1 } from "../axisPointer/install.js";
import { use } from "../../extension.js";
import TooltipModel from "./TooltipModel.js";
import TooltipView from "./TooltipView.js";
import { noop } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function install(registers) {
  use(install$1);
  registers.registerComponentModel(TooltipModel);
  registers.registerComponentView(TooltipView);
  registers.registerAction({
    type: "showTip",
    event: "showTip",
    update: "tooltip:manuallyShowTip"
  }, noop);
  registers.registerAction({
    type: "hideTip",
    event: "hideTip",
    update: "tooltip:manuallyHideTip"
  }, noop);
}
export {
  install
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { curry, createHashMap, each, merge } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import createSeriesDataSimply from "../helper/createSeriesDataSimply.js";
import SeriesModel from "../../model/Series.js";
import geoSourceManager from "../../coord/geo/geoSourceManager.js";
import { makeSeriesEncodeForNameBased } from "../../data/helper/sourceHelper.js";
import { createTooltipMarkup } from "../../component/tooltip/tooltipMarkup.js";
import { createSymbol } from "../../util/symbol.js";
import { decideCoordSysUsageKind, CoordinateSystemUsageKind } from "../../core/CoordinateSystem.js";
import tokens from "../../visual/tokens.js";
var MapSeries = (
  /** @class */
  function(_super) {
    __extends(MapSeries2, _super);
    function MapSeries2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MapSeries2.type;
      _this.needsDrawMap = false;
      _this.seriesGroup = [];
      _this.getTooltipPosition = function(dataIndex) {
        if (dataIndex != null) {
          var name_1 = this.getData().getName(dataIndex);
          var geo = this.coordinateSystem;
          var region = geo.getRegion(name_1);
          return region && geo.dataToPoint(region.getCenter());
        }
      };
      return _this;
    }
    MapSeries2.prototype.getInitialData = function(option) {
      var data = createSeriesDataSimply(this, {
        coordDimensions: ["value"],
        encodeDefaulter: curry(makeSeriesEncodeForNameBased, this)
      });
      var dataNameIndexMap = createHashMap();
      var toAppendItems = [];
      for (var i = 0, len = data.count(); i < len; i++) {
        var name_2 = data.getName(i);
        dataNameIndexMap.set(name_2, i);
      }
      var geoSource = geoSourceManager.load(this.getMapType(), this.option.nameMap, this.option.nameProperty);
      each(geoSource.regions, function(region) {
        var name = region.name;
        var dataNameIdx = dataNameIndexMap.get(name);
        var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;
        var dataItem;
        if (dataNameIdx == null) {
          dataItem = {
            name
          };
          toAppendItems.push(dataItem);
        } else {
          dataItem = data.getRawDataItem(dataNameIdx);
        }
        specifiedGeoJSONRegionStyle && merge(dataItem, specifiedGeoJSONRegionStyle);
      });
      data.appendData(toAppendItems);
      return data;
    };
    MapSeries2.prototype.getHostGeoModel = function() {
      if (decideCoordSysUsageKind(this).kind === CoordinateSystemUsageKind.boxCoordSys) {
        return;
      }
      return this.getReferringComponents("geo", {
        useDefault: false,
        enableAll: false,
        enableNone: false
      }).models[0];
    };
    MapSeries2.prototype.getMapType = function() {
      return (this.getHostGeoModel() || this).option.map;
    };
    MapSeries2.prototype.getRawValue = function(dataIndex) {
      var data = this.getData();
      return data.get(data.mapDimension("value"), dataIndex);
    };
    MapSeries2.prototype.getRegionModel = function(regionName) {
      var data = this.getData();
      return data.getItemModel(data.indexOfName(regionName));
    };
    MapSeries2.prototype.formatTooltip = function(dataIndex, multipleSeries, dataType) {
      var data = this.getData();
      var value = this.getRawValue(dataIndex);
      var name = data.getName(dataIndex);
      var seriesGroup = this.seriesGroup;
      var seriesNames = [];
      for (var i = 0; i < seriesGroup.length; i++) {
        var otherIndex = seriesGroup[i].originalData.indexOfName(name);
        var valueDim = data.mapDimension("value");
        if (!isNaN(seriesGroup[i].originalData.get(valueDim, otherIndex))) {
          seriesNames.push(seriesGroup[i].name);
        }
      }
      return createTooltipMarkup("section", {
        header: seriesNames.join(", "),
        noHeader: !seriesNames.length,
        blocks: [createTooltipMarkup("nameValue", {
          name,
          value
        })]
      });
    };
    MapSeries2.prototype.setZoom = function(zoom) {
      this.option.zoom = zoom;
    };
    MapSeries2.prototype.setCenter = function(center) {
      this.option.center = center;
    };
    MapSeries2.prototype.getLegendIcon = function(opt) {
      var iconType = opt.icon || "roundRect";
      var icon = createSymbol(iconType, 0, 0, opt.itemWidth, opt.itemHeight, opt.itemStyle.fill);
      icon.setStyle(opt.itemStyle);
      icon.style.stroke = "none";
      if (iconType.indexOf("empty") > -1) {
        icon.style.stroke = icon.style.fill;
        icon.style.fill = tokens.color.neutral00;
        icon.style.lineWidth = 2;
      }
      return icon;
    };
    MapSeries2.type = "series.map";
    MapSeries2.dependencies = ["geo"];
    MapSeries2.layoutMode = "box";
    MapSeries2.defaultOption = {
      // 一级层叠
      // zlevel: 0,
      // 二级层叠
      z: 2,
      coordinateSystem: "geo",
      // map should be explicitly specified since ec3.
      map: "",
      // If `geoIndex` is not specified, a exclusive geo will be
      // created. Otherwise use the specified geo component, and
      // `map` and `mapType` are ignored.
      // geoIndex: 0,
      // 'center' | 'left' | 'right' | 'x%' | {number}
      left: "center",
      // 'center' | 'top' | 'bottom' | 'x%' | {number}
      top: "center",
      // right
      // bottom
      // width:
      // height
      // Aspect is width / height. Inited to be geoJson bbox aspect
      // This parameter is used for scale this aspect
      // Default value:
      // for geoSVG source: 1,
      // for geoJSON source: 0.75.
      aspectScale: null,
      // Layout with center and size
      // If you want to put map in a fixed size box with right aspect ratio
      // This two properties may be more convenient.
      // layoutCenter: [50%, 50%]
      // layoutSize: 100
      showLegendSymbol: true,
      // Define left-top, right-bottom coords to control view
      // For example, [ [180, 90], [-180, -90] ],
      // higher priority than center and zoom
      boundingCoords: null,
      // Default on center of map
      center: null,
      zoom: 1,
      scaleLimit: null,
      selectedMode: true,
      label: {
        show: false,
        color: tokens.color.tertiary
      },
      // scaleLimit: null,
      itemStyle: {
        borderWidth: 0.5,
        borderColor: tokens.color.border,
        areaColor: tokens.color.background
      },
      emphasis: {
        label: {
          show: true,
          color: tokens.color.primary
        },
        itemStyle: {
          areaColor: tokens.color.highlight
        }
      },
      select: {
        label: {
          show: true,
          color: tokens.color.primary
        },
        itemStyle: {
          color: tokens.color.highlight
        }
      },
      nameProperty: "name"
    };
    return MapSeries2;
  }(SeriesModel)
);
export {
  MapSeries as default
};

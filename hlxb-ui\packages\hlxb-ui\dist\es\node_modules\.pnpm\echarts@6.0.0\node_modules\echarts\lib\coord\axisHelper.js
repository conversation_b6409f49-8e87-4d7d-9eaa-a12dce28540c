import { isString, isFunction, each, keys } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import OrdinalScale from "../scale/Ordinal.js";
import IntervalScale from "../scale/Interval.js";
import Scale from "../scale/Scale.js";
import { prepareLayoutBarSeries, makeColumnLayout, retrieveColumnLayout } from "../layout/barGrid.js";
import TimeScale from "../scale/Time.js";
import LogScale from "../scale/Log.js";
import { getStackedDimension } from "../data/helper/dataStackHelper.js";
import { ensureScaleRawExtentInfo } from "./scaleRawExtentInfo.js";
import { parseTimeAxisLabelFormatter } from "../util/time.js";
import { getScaleBreakHelper } from "../scale/break.js";
import { error } from "../util/log.js";
function getScaleExtent(scale, model) {
  var scaleType = scale.type;
  var rawExtentResult = ensureScaleRawExtentInfo(scale, model, scale.getExtent()).calculate();
  scale.setBlank(rawExtentResult.isBlank);
  var min = rawExtentResult.min;
  var max = rawExtentResult.max;
  var ecModel = model.ecModel;
  if (ecModel && scaleType === "time") {
    var barSeriesModels = prepareLayoutBarSeries("bar", ecModel);
    var isBaseAxisAndHasBarSeries_1 = false;
    each(barSeriesModels, function(seriesModel) {
      isBaseAxisAndHasBarSeries_1 = isBaseAxisAndHasBarSeries_1 || seriesModel.getBaseAxis() === model.axis;
    });
    if (isBaseAxisAndHasBarSeries_1) {
      var barWidthAndOffset = makeColumnLayout(barSeriesModels);
      var adjustedScale = adjustScaleForOverflow(min, max, model, barWidthAndOffset);
      min = adjustedScale.min;
      max = adjustedScale.max;
    }
  }
  return {
    extent: [min, max],
    // "fix" means "fixed", the value should not be
    // changed in the subsequent steps.
    fixMin: rawExtentResult.minFixed,
    fixMax: rawExtentResult.maxFixed
  };
}
function adjustScaleForOverflow(min, max, model, barWidthAndOffset) {
  var axisExtent = model.axis.getExtent();
  var axisLength = Math.abs(axisExtent[1] - axisExtent[0]);
  var barsOnCurrentAxis = retrieveColumnLayout(barWidthAndOffset, model.axis);
  if (barsOnCurrentAxis === void 0) {
    return {
      min,
      max
    };
  }
  var minOverflow = Infinity;
  each(barsOnCurrentAxis, function(item) {
    minOverflow = Math.min(item.offset, minOverflow);
  });
  var maxOverflow = -Infinity;
  each(barsOnCurrentAxis, function(item) {
    maxOverflow = Math.max(item.offset + item.width, maxOverflow);
  });
  minOverflow = Math.abs(minOverflow);
  maxOverflow = Math.abs(maxOverflow);
  var totalOverFlow = minOverflow + maxOverflow;
  var oldRange = max - min;
  var oldRangePercentOfNew = 1 - (minOverflow + maxOverflow) / axisLength;
  var overflowBuffer = oldRange / oldRangePercentOfNew - oldRange;
  max += overflowBuffer * (maxOverflow / totalOverFlow);
  min -= overflowBuffer * (minOverflow / totalOverFlow);
  return {
    min,
    max
  };
}
function niceScaleExtent(scale, inModel) {
  var model = inModel;
  var extentInfo = getScaleExtent(scale, model);
  var extent = extentInfo.extent;
  var splitNumber = model.get("splitNumber");
  if (scale instanceof LogScale) {
    scale.base = model.get("logBase");
  }
  var scaleType = scale.type;
  var interval = model.get("interval");
  var isIntervalOrTime = scaleType === "interval" || scaleType === "time";
  scale.setBreaksFromOption(retrieveAxisBreaksOption(model));
  scale.setExtent(extent[0], extent[1]);
  scale.calcNiceExtent({
    splitNumber,
    fixMin: extentInfo.fixMin,
    fixMax: extentInfo.fixMax,
    minInterval: isIntervalOrTime ? model.get("minInterval") : null,
    maxInterval: isIntervalOrTime ? model.get("maxInterval") : null
  });
  if (interval != null) {
    scale.setInterval && scale.setInterval(interval);
  }
}
function createScaleByModel(model, axisType) {
  axisType = axisType || model.get("type");
  if (axisType) {
    switch (axisType) {
      case "category":
        return new OrdinalScale({
          ordinalMeta: model.getOrdinalMeta ? model.getOrdinalMeta() : model.getCategories(),
          extent: [Infinity, -Infinity]
        });
      case "time":
        return new TimeScale({
          locale: model.ecModel.getLocaleModel(),
          useUTC: model.ecModel.get("useUTC")
        });
      default:
        return new (Scale.getClass(axisType) || IntervalScale)();
    }
  }
}
function ifAxisCrossZero(axis) {
  var dataExtent = axis.scale.getExtent();
  var min = dataExtent[0];
  var max = dataExtent[1];
  return !(min > 0 && max > 0 || min < 0 && max < 0);
}
function makeLabelFormatter(axis) {
  var labelFormatter = axis.getLabelModel().get("formatter");
  if (axis.type === "time") {
    var parsed_1 = parseTimeAxisLabelFormatter(labelFormatter);
    return function(tick, idx) {
      return axis.scale.getFormattedLabel(tick, idx, parsed_1);
    };
  } else if (isString(labelFormatter)) {
    return function(tick) {
      var label = axis.scale.getLabel(tick);
      var text = labelFormatter.replace("{value}", label != null ? label : "");
      return text;
    };
  } else if (isFunction(labelFormatter)) {
    if (axis.type === "category") {
      return function(tick, idx) {
        return labelFormatter(
          getAxisRawValue(axis, tick),
          tick.value - axis.scale.getExtent()[0],
          null
          // Using `null` just for backward compat.
        );
      };
    }
    var scaleBreakHelper_1 = getScaleBreakHelper();
    return function(tick, idx) {
      var extra = null;
      if (scaleBreakHelper_1) {
        extra = scaleBreakHelper_1.makeAxisLabelFormatterParamBreak(extra, tick["break"]);
      }
      return labelFormatter(getAxisRawValue(axis, tick), idx, extra);
    };
  } else {
    return function(tick) {
      return axis.scale.getLabel(tick);
    };
  }
}
function getAxisRawValue(axis, tick) {
  return axis.type === "category" ? axis.scale.getLabel(tick) : tick.value;
}
function getOptionCategoryInterval(model) {
  var interval = model.get("interval");
  return interval == null ? "auto" : interval;
}
function shouldShowAllLabels(axis) {
  return axis.type === "category" && getOptionCategoryInterval(axis.getLabelModel()) === 0;
}
function getDataDimensionsOnAxis(data, axisDim) {
  var dataDimMap = {};
  each(data.mapDimensionsAll(axisDim), function(dataDim) {
    dataDimMap[getStackedDimension(data, dataDim)] = true;
  });
  return keys(dataDimMap);
}
function unionAxisExtentFromData(dataExtent, data, axisDim) {
  if (data) {
    each(getDataDimensionsOnAxis(data, axisDim), function(dim) {
      var seriesExtent = data.getApproximateExtent(dim);
      seriesExtent[0] < dataExtent[0] && (dataExtent[0] = seriesExtent[0]);
      seriesExtent[1] > dataExtent[1] && (dataExtent[1] = seriesExtent[1]);
    });
  }
}
function isNameLocationCenter(nameLocation) {
  return nameLocation === "middle" || nameLocation === "center";
}
function shouldAxisShow(axisModel) {
  return axisModel.getShallow("show");
}
function retrieveAxisBreaksOption(model) {
  var option = model.get("breaks", true);
  if (option != null) {
    {
      if (process.env.NODE_ENV !== "production") {
        error('Must `import {AxisBreak} from "echarts/features.js"; use(AxisBreak);` first if using breaks option.');
      }
      return void 0;
    }
  }
}
export {
  createScaleByModel,
  getAxisRawValue,
  getDataDimensionsOnAxis,
  getOptionCategoryInterval,
  getScaleExtent,
  ifAxisCrossZero,
  isNameLocationCenter,
  makeLabelFormatter,
  niceScaleExtent,
  retrieveAxisBreaksOption,
  shouldAxisShow,
  shouldShowAllLabels,
  unionAxisExtentFromData
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const graphic = require("../../util/graphic.js");
const states = require("../../util/states.js");
const symbol = require("../../util/symbol.js");
const number = require("../../util/number.js");
const Chart = require("../../view/Chart.js");
const labelHelper = require("../helper/labelHelper.js");
const labelStyle = require("../../label/labelStyle.js");
const Image = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Image.js");
const innerStore = require("../../util/innerStore.js");
const createClipPathFromCoordSys = require("../helper/createClipPathFromCoordSys.js");
const Group = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js");
const Circle = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Circle.js");
const Rect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js");
const basicTransition = require("../../animation/basicTransition.js");
var BAR_BORDER_WIDTH_QUERY = ["itemStyle", "borderWidth"];
var LAYOUT_ATTRS = [{
  xy: "x",
  wh: "width",
  index: 0,
  posDesc: ["left", "right"]
}, {
  xy: "y",
  wh: "height",
  index: 1,
  posDesc: ["top", "bottom"]
}];
var pathForLineWidth = new Circle.default();
var PictorialBarView = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(PictorialBarView2, _super);
    function PictorialBarView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = PictorialBarView2.type;
      return _this;
    }
    PictorialBarView2.prototype.render = function(seriesModel, ecModel, api) {
      var group = this.group;
      var data = seriesModel.getData();
      var oldData = this._data;
      var cartesian = seriesModel.coordinateSystem;
      var baseAxis = cartesian.getBaseAxis();
      var isHorizontal = baseAxis.isHorizontal();
      var coordSysRect = cartesian.master.getRect();
      var opt = {
        ecSize: {
          width: api.getWidth(),
          height: api.getHeight()
        },
        seriesModel,
        coordSys: cartesian,
        coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],
        isHorizontal,
        valueDim: LAYOUT_ATTRS[+isHorizontal],
        categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]
      };
      data.diff(oldData).add(function(dataIndex) {
        if (!data.hasValue(dataIndex)) {
          return;
        }
        var itemModel = getItemModel(data, dataIndex);
        var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);
        var bar = createBar(data, opt, symbolMeta);
        data.setItemGraphicEl(dataIndex, bar);
        group.add(bar);
        updateCommon(bar, opt, symbolMeta);
      }).update(function(newIndex, oldIndex) {
        var bar = oldData.getItemGraphicEl(oldIndex);
        if (!data.hasValue(newIndex)) {
          group.remove(bar);
          return;
        }
        var itemModel = getItemModel(data, newIndex);
        var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);
        var pictorialShapeStr = getShapeStr(data, symbolMeta);
        if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {
          group.remove(bar);
          data.setItemGraphicEl(newIndex, null);
          bar = null;
        }
        if (bar) {
          updateBar(bar, opt, symbolMeta);
        } else {
          bar = createBar(data, opt, symbolMeta, true);
        }
        data.setItemGraphicEl(newIndex, bar);
        bar.__pictorialSymbolMeta = symbolMeta;
        group.add(bar);
        updateCommon(bar, opt, symbolMeta);
      }).remove(function(dataIndex) {
        var bar = oldData.getItemGraphicEl(dataIndex);
        bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);
      }).execute();
      var clipPath = seriesModel.get("clip", true) ? createClipPathFromCoordSys.createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;
      if (clipPath) {
        group.setClipPath(clipPath);
      } else {
        group.removeClipPath();
      }
      this._data = data;
      return this.group;
    };
    PictorialBarView2.prototype.remove = function(ecModel, api) {
      var group = this.group;
      var data = this._data;
      if (ecModel.get("animation")) {
        if (data) {
          data.eachItemGraphicEl(function(bar) {
            removeBar(data, innerStore.getECData(bar).dataIndex, ecModel, bar);
          });
        }
      } else {
        group.removeAll();
      }
    };
    PictorialBarView2.type = "pictorialBar";
    return PictorialBarView2;
  }(Chart.default)
);
function getSymbolMeta(data, dataIndex, itemModel, opt) {
  var layout = data.getItemLayout(dataIndex);
  var symbolRepeat = itemModel.get("symbolRepeat");
  var symbolClip = itemModel.get("symbolClip");
  var symbolPosition = itemModel.get("symbolPosition") || "start";
  var symbolRotate = itemModel.get("symbolRotate");
  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;
  var symbolPatternSize = itemModel.get("symbolPatternSize") || 2;
  var isAnimationEnabled2 = itemModel.isAnimationEnabled();
  var symbolMeta = {
    dataIndex,
    layout,
    itemModel,
    symbolType: data.getItemVisual(dataIndex, "symbol") || "circle",
    style: data.getItemVisual(dataIndex, "style"),
    symbolClip,
    symbolRepeat,
    symbolRepeatDirection: itemModel.get("symbolRepeatDirection"),
    symbolPatternSize,
    rotation,
    animationModel: isAnimationEnabled2 ? itemModel : null,
    hoverScale: isAnimationEnabled2 && itemModel.get(["emphasis", "scale"]),
    z2: itemModel.getShallow("z", true) || 0
  };
  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);
  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);
  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);
  var symbolSize = symbolMeta.symbolSize;
  var symbolOffset = symbol.normalizeSymbolOffset(itemModel.get("symbolOffset"), symbolSize);
  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);
  return symbolMeta;
}
function prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {
  var valueDim = opt.valueDim;
  var symbolBoundingData = itemModel.get("symbolBoundingData");
  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());
  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));
  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);
  var boundingLength;
  if (util.isArray(symbolBoundingData)) {
    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];
    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();
    boundingLength = symbolBoundingExtent[pxSignIdx];
  } else if (symbolBoundingData != null) {
    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;
  } else if (symbolRepeat) {
    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;
  } else {
    boundingLength = layout[valueDim.wh];
  }
  outputSymbolMeta.boundingLength = boundingLength;
  if (symbolRepeat) {
    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];
  }
  var isXAxis = valueDim.xy === "x";
  var isInverse = valueAxis.inverse;
  outputSymbolMeta.pxSign = isXAxis && !isInverse || !isXAxis && isInverse ? boundingLength >= 0 ? 1 : -1 : boundingLength > 0 ? 1 : -1;
}
function convertToCoordOnAxis(axis, value) {
  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));
}
function prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {
  var valueDim = opt.valueDim;
  var categoryDim = opt.categoryDim;
  var categorySize = Math.abs(layout[categoryDim.wh]);
  var symbolSize = data.getItemVisual(dataIndex, "symbolSize");
  var parsedSymbolSize;
  if (util.isArray(symbolSize)) {
    parsedSymbolSize = symbolSize.slice();
  } else {
    if (symbolSize == null) {
      parsedSymbolSize = ["100%", "100%"];
    } else {
      parsedSymbolSize = [symbolSize, symbolSize];
    }
  }
  parsedSymbolSize[categoryDim.index] = number.parsePercent(parsedSymbolSize[categoryDim.index], categorySize);
  parsedSymbolSize[valueDim.index] = number.parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));
  outputSymbolMeta.symbolSize = parsedSymbolSize;
  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize];
  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;
}
function prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {
  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;
  if (valueLineWidth) {
    pathForLineWidth.attr({
      scaleX: symbolScale[0],
      scaleY: symbolScale[1],
      rotation
    });
    pathForLineWidth.updateTransform();
    valueLineWidth /= pathForLineWidth.getLineScale();
    valueLineWidth *= symbolScale[opt.valueDim.index];
  }
  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;
}
function prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {
  var categoryDim = opt.categoryDim;
  var valueDim = opt.valueDim;
  var pxSign = outputSymbolMeta.pxSign;
  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);
  var pathLen = unitLength;
  if (symbolRepeat) {
    var absBoundingLength = Math.abs(boundingLength);
    var symbolMargin = util.retrieve(itemModel.get("symbolMargin"), "15%") + "";
    var hasEndGap = false;
    if (symbolMargin.lastIndexOf("!") === symbolMargin.length - 1) {
      hasEndGap = true;
      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);
    }
    var symbolMarginNumeric = number.parsePercent(symbolMargin, symbolSize[valueDim.index]);
    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0);
    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;
    var repeatSpecified = number.isNumeric(symbolRepeat);
    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin);
    var mDiff = absBoundingLength - repeatTimes * unitLength;
    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));
    uLenWithMargin = unitLength + symbolMarginNumeric * 2;
    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;
    if (!repeatSpecified && symbolRepeat !== "fixed") {
      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;
    }
    pathLen = repeatTimes * uLenWithMargin - endFix;
    outputSymbolMeta.repeatTimes = repeatTimes;
    outputSymbolMeta.symbolMargin = symbolMarginNumeric;
  }
  var sizeFix = pxSign * (pathLen / 2);
  var pathPosition = outputSymbolMeta.pathPosition = [];
  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;
  pathPosition[valueDim.index] = symbolPosition === "start" ? sizeFix : symbolPosition === "end" ? boundingLength - sizeFix : boundingLength / 2;
  if (symbolOffset) {
    pathPosition[0] += symbolOffset[0];
    pathPosition[1] += symbolOffset[1];
  }
  var bundlePosition = outputSymbolMeta.bundlePosition = [];
  bundlePosition[categoryDim.index] = layout[categoryDim.xy];
  bundlePosition[valueDim.index] = layout[valueDim.xy];
  var barRectShape = outputSymbolMeta.barRectShape = util.extend({}, layout);
  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));
  barRectShape[categoryDim.wh] = layout[categoryDim.wh];
  var clipShape = outputSymbolMeta.clipShape = {};
  clipShape[categoryDim.xy] = -layout[categoryDim.xy];
  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];
  clipShape[valueDim.xy] = 0;
  clipShape[valueDim.wh] = layout[valueDim.wh];
}
function createPath(symbolMeta) {
  var symbolPatternSize = symbolMeta.symbolPatternSize;
  var path = symbol.createSymbol(
    // Consider texture img, make a big size.
    symbolMeta.symbolType,
    -symbolPatternSize / 2,
    -symbolPatternSize / 2,
    symbolPatternSize,
    symbolPatternSize
  );
  path.attr({
    culling: true
  });
  path.type !== "image" && path.setStyle({
    strokeNoScale: true
  });
  return path;
}
function createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {
  var bundle = bar.__pictorialBundle;
  var symbolSize = symbolMeta.symbolSize;
  var valueLineWidth = symbolMeta.valueLineWidth;
  var pathPosition = symbolMeta.pathPosition;
  var valueDim = opt.valueDim;
  var repeatTimes = symbolMeta.repeatTimes || 0;
  var index = 0;
  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;
  eachPath(bar, function(path2) {
    path2.__pictorialAnimationIndex = index;
    path2.__pictorialRepeatTimes = repeatTimes;
    if (index < repeatTimes) {
      updateAttr(path2, null, makeTarget(index), symbolMeta, isUpdate);
    } else {
      updateAttr(path2, null, {
        scaleX: 0,
        scaleY: 0
      }, symbolMeta, isUpdate, function() {
        bundle.remove(path2);
      });
    }
    index++;
  });
  for (; index < repeatTimes; index++) {
    var path = createPath(symbolMeta);
    path.__pictorialAnimationIndex = index;
    path.__pictorialRepeatTimes = repeatTimes;
    bundle.add(path);
    var target = makeTarget(index);
    updateAttr(path, {
      x: target.x,
      y: target.y,
      scaleX: 0,
      scaleY: 0
    }, {
      scaleX: target.scaleX,
      scaleY: target.scaleY,
      rotation: target.rotation
    }, symbolMeta, isUpdate);
  }
  function makeTarget(index2) {
    var position = pathPosition.slice();
    var pxSign = symbolMeta.pxSign;
    var i = index2;
    if (symbolMeta.symbolRepeatDirection === "start" ? pxSign > 0 : pxSign < 0) {
      i = repeatTimes - 1 - index2;
    }
    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];
    return {
      x: position[0],
      y: position[1],
      scaleX: symbolMeta.symbolScale[0],
      scaleY: symbolMeta.symbolScale[1],
      rotation: symbolMeta.rotation
    };
  }
}
function createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {
  var bundle = bar.__pictorialBundle;
  var mainPath = bar.__pictorialMainPath;
  if (!mainPath) {
    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);
    bundle.add(mainPath);
    updateAttr(mainPath, {
      x: symbolMeta.pathPosition[0],
      y: symbolMeta.pathPosition[1],
      scaleX: 0,
      scaleY: 0,
      rotation: symbolMeta.rotation
    }, {
      scaleX: symbolMeta.symbolScale[0],
      scaleY: symbolMeta.symbolScale[1]
    }, symbolMeta, isUpdate);
  } else {
    updateAttr(mainPath, null, {
      x: symbolMeta.pathPosition[0],
      y: symbolMeta.pathPosition[1],
      scaleX: symbolMeta.symbolScale[0],
      scaleY: symbolMeta.symbolScale[1],
      rotation: symbolMeta.rotation
    }, symbolMeta, isUpdate);
  }
}
function createOrUpdateBarRect(bar, symbolMeta, isUpdate) {
  var rectShape = util.extend({}, symbolMeta.barRectShape);
  var barRect = bar.__pictorialBarRect;
  if (!barRect) {
    barRect = bar.__pictorialBarRect = new Rect.default({
      z2: 2,
      shape: rectShape,
      silent: true,
      style: {
        stroke: "transparent",
        fill: "transparent",
        lineWidth: 0
      }
    });
    barRect.disableMorphing = true;
    bar.add(barRect);
  } else {
    updateAttr(barRect, null, {
      shape: rectShape
    }, symbolMeta, isUpdate);
  }
}
function createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {
  if (symbolMeta.symbolClip) {
    var clipPath = bar.__pictorialClipPath;
    var clipShape = util.extend({}, symbolMeta.clipShape);
    var valueDim = opt.valueDim;
    var animationModel = symbolMeta.animationModel;
    var dataIndex = symbolMeta.dataIndex;
    if (clipPath) {
      basicTransition.updateProps(clipPath, {
        shape: clipShape
      }, animationModel, dataIndex);
    } else {
      clipShape[valueDim.wh] = 0;
      clipPath = new Rect.default({
        shape: clipShape
      });
      bar.__pictorialBundle.setClipPath(clipPath);
      bar.__pictorialClipPath = clipPath;
      var target = {};
      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];
      graphic[isUpdate ? "updateProps" : "initProps"](clipPath, {
        shape: target
      }, animationModel, dataIndex);
    }
  }
}
function getItemModel(data, dataIndex) {
  var itemModel = data.getItemModel(dataIndex);
  itemModel.getAnimationDelayParams = getAnimationDelayParams;
  itemModel.isAnimationEnabled = isAnimationEnabled;
  return itemModel;
}
function getAnimationDelayParams(path) {
  return {
    index: path.__pictorialAnimationIndex,
    count: path.__pictorialRepeatTimes
  };
}
function isAnimationEnabled() {
  return this.parentModel.isAnimationEnabled() && !!this.getShallow("animation");
}
function createBar(data, opt, symbolMeta, isUpdate) {
  var bar = new Group.default();
  var bundle = new Group.default();
  bar.add(bundle);
  bar.__pictorialBundle = bundle;
  bundle.x = symbolMeta.bundlePosition[0];
  bundle.y = symbolMeta.bundlePosition[1];
  if (symbolMeta.symbolRepeat) {
    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);
  } else {
    createOrUpdateSingleSymbol(bar, opt, symbolMeta);
  }
  createOrUpdateBarRect(bar, symbolMeta, isUpdate);
  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);
  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);
  bar.__pictorialSymbolMeta = symbolMeta;
  return bar;
}
function updateBar(bar, opt, symbolMeta) {
  var animationModel = symbolMeta.animationModel;
  var dataIndex = symbolMeta.dataIndex;
  var bundle = bar.__pictorialBundle;
  basicTransition.updateProps(bundle, {
    x: symbolMeta.bundlePosition[0],
    y: symbolMeta.bundlePosition[1]
  }, animationModel, dataIndex);
  if (symbolMeta.symbolRepeat) {
    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);
  } else {
    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);
  }
  createOrUpdateBarRect(bar, symbolMeta, true);
  createOrUpdateClip(bar, opt, symbolMeta, true);
}
function removeBar(data, dataIndex, animationModel, bar) {
  var labelRect = bar.__pictorialBarRect;
  labelRect && labelRect.removeTextContent();
  var paths = [];
  eachPath(bar, function(path) {
    paths.push(path);
  });
  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath);
  bar.__pictorialClipPath && (animationModel = null);
  util.each(paths, function(path) {
    basicTransition.removeElement(path, {
      scaleX: 0,
      scaleY: 0
    }, animationModel, dataIndex, function() {
      bar.parent && bar.parent.remove(bar);
    });
  });
  data.setItemGraphicEl(dataIndex, null);
}
function getShapeStr(data, symbolMeta) {
  return [data.getItemVisual(symbolMeta.dataIndex, "symbol") || "none", !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(":");
}
function eachPath(bar, cb, context) {
  util.each(bar.__pictorialBundle.children(), function(el) {
    el !== bar.__pictorialBarRect && cb.call(context, el);
  });
}
function updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {
  immediateAttrs && el.attr(immediateAttrs);
  if (symbolMeta.symbolClip && !isUpdate) {
    animationAttrs && el.attr(animationAttrs);
  } else {
    animationAttrs && graphic[isUpdate ? "updateProps" : "initProps"](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);
  }
}
function updateCommon(bar, opt, symbolMeta) {
  var dataIndex = symbolMeta.dataIndex;
  var itemModel = symbolMeta.itemModel;
  var emphasisModel = itemModel.getModel("emphasis");
  var emphasisStyle = emphasisModel.getModel("itemStyle").getItemStyle();
  var blurStyle = itemModel.getModel(["blur", "itemStyle"]).getItemStyle();
  var selectStyle = itemModel.getModel(["select", "itemStyle"]).getItemStyle();
  var cursorStyle = itemModel.getShallow("cursor");
  var focus = emphasisModel.get("focus");
  var blurScope = emphasisModel.get("blurScope");
  var hoverScale = emphasisModel.get("scale");
  eachPath(bar, function(path) {
    if (path instanceof Image.default) {
      var pathStyle = path.style;
      path.useStyle(util.extend({
        // TODO other properties like dx, dy ?
        image: pathStyle.image,
        x: pathStyle.x,
        y: pathStyle.y,
        width: pathStyle.width,
        height: pathStyle.height
      }, symbolMeta.style));
    } else {
      path.useStyle(symbolMeta.style);
    }
    var emphasisState = path.ensureState("emphasis");
    emphasisState.style = emphasisStyle;
    if (hoverScale) {
      emphasisState.scaleX = path.scaleX * 1.1;
      emphasisState.scaleY = path.scaleY * 1.1;
    }
    path.ensureState("blur").style = blurStyle;
    path.ensureState("select").style = selectStyle;
    cursorStyle && (path.cursor = cursorStyle);
    path.z2 = symbolMeta.z2;
  });
  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];
  var barRect = bar.__pictorialBarRect;
  barRect.ignoreClip = true;
  labelStyle.setLabelStyle(barRect, labelStyle.getLabelStatesModels(itemModel), {
    labelFetcher: opt.seriesModel,
    labelDataIndex: dataIndex,
    defaultText: labelHelper.getDefaultLabel(opt.seriesModel.getData(), dataIndex),
    inheritColor: symbolMeta.style.fill,
    defaultOpacity: symbolMeta.style.opacity,
    defaultOutsidePosition: barPositionOutside
  });
  states.toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get("disabled"));
}
function toIntTimes(times) {
  var roundedTimes = Math.round(times);
  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);
}
exports.default = PictorialBarView;

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { isArray, map } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import ComponentModel from "../../model/Component.js";
import { getLayoutParams, sizeCalculable, mergeLayoutParam } from "../../util/layout.js";
import tokens from "../../visual/tokens.js";
var CalendarModel = (
  /** @class */
  function(_super) {
    __extends(CalendarModel2, _super);
    function CalendarModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = CalendarModel2.type;
      return _this;
    }
    CalendarModel2.prototype.init = function(option, parentModel, ecModel) {
      var inputPositionParams = getLayoutParams(option);
      _super.prototype.init.apply(this, arguments);
      mergeAndNormalizeLayoutParams(option, inputPositionParams);
    };
    CalendarModel2.prototype.mergeOption = function(option) {
      _super.prototype.mergeOption.apply(this, arguments);
      mergeAndNormalizeLayoutParams(this.option, option);
    };
    CalendarModel2.prototype.getCellSize = function() {
      return this.option.cellSize;
    };
    CalendarModel2.type = "calendar";
    CalendarModel2.layoutMode = "box";
    CalendarModel2.defaultOption = {
      // zlevel: 0,
      // TODO: theoretically, the z of the calendar should be lower
      // than series, but we don't want the series to be displayed
      // on top of the borders like month split line. To align with
      // the effect of previous versions, we set the z to 2 for now
      // until better solution is found.
      z: 2,
      left: 80,
      top: 60,
      cellSize: 20,
      // horizontal vertical
      orient: "horizontal",
      // month separate line style
      splitLine: {
        show: true,
        lineStyle: {
          color: tokens.color.axisLine,
          width: 1,
          type: "solid"
        }
      },
      // rect style  temporarily unused emphasis
      itemStyle: {
        color: tokens.color.neutral00,
        borderWidth: 1,
        borderColor: tokens.color.neutral10
      },
      // week text style
      dayLabel: {
        show: true,
        firstDay: 0,
        // start end
        position: "start",
        margin: tokens.size.s,
        color: tokens.color.secondary
      },
      // month text style
      monthLabel: {
        show: true,
        // start end
        position: "start",
        margin: tokens.size.s,
        // center or left
        align: "center",
        formatter: null,
        color: tokens.color.secondary
      },
      // year text style
      yearLabel: {
        show: true,
        // top bottom left right
        position: null,
        margin: tokens.size.xl,
        formatter: null,
        color: tokens.color.quaternary,
        fontFamily: "sans-serif",
        fontWeight: "bolder",
        fontSize: 20
      }
    };
    return CalendarModel2;
  }(ComponentModel)
);
function mergeAndNormalizeLayoutParams(target, raw) {
  var cellSize = target.cellSize;
  var cellSizeArr;
  if (!isArray(cellSize)) {
    cellSizeArr = target.cellSize = [cellSize, cellSize];
  } else {
    cellSizeArr = cellSize;
  }
  if (cellSizeArr.length === 1) {
    cellSizeArr[1] = cellSizeArr[0];
  }
  var ignoreSize = map([0, 1], function(hvIdx) {
    if (sizeCalculable(raw, hvIdx)) {
      cellSizeArr[hvIdx] = "auto";
    }
    return cellSizeArr[hvIdx] != null && cellSizeArr[hvIdx] !== "auto";
  });
  mergeLayoutParam(target, raw, {
    type: "box",
    ignoreSize
  });
}
export {
  CalendarModel as default
};

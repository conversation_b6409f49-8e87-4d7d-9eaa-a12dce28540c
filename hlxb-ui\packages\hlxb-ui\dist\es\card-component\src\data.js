const colorRgba = [
  "rgba(45,130,254,0.8)",
  "rgba(253,165,78,0.8)",
  "rgba(31,195,164,0.8)",
  "rgba(97,195,31,0.8)",
  "rgba(78,96,253,0.8)",
  "rgba(45,48,254,0.8)",
  "rgba(115,45,254,0.8)",
  "rgba(31,195,105,0.8)",
  "rgba(254,139,45,0.8)",
  "rgba(254,197,45,0.8)",
  "rgba(254,244,45,0.8)",
  "rgba(45,202,254,0.8)",
  "rgba(45,226,254,0.8)",
  "rgba(84,112,198,0.8)",
  "rgba(145,204,117,0.8)",
  "rgba(250,200,88,0.8)",
  "rgba(238,102,102,0.8)",
  "rgba(115,192,222,0.8)",
  "rgba(59,162,114,0.8)",
  "rgba(252,132,82,0.8)",
  "rgba(154,96,180,0.8)",
  "rgba(234,124,204,0.8)"
];
const colorRgbaStar = [
  "rgba(45,130,254, 0.08)",
  "rgba(253,165,78,0.08)",
  "rgba(31,195,164,0.08)",
  "rgba(97,195,31,0.08)",
  "rgba(78,96,253,0.08)",
  "rgba(45,48,254,0.08)",
  "rgba(115,45,254,0.08)",
  "rgba(31,195,105,0.08)",
  "rgba(254,139,45,0.08)",
  "rgba(254,197,45,0.08)",
  "rgba(254,244,45,0.08)",
  "rgba(45,202,254,0.08)",
  "rgba(45,226,254,0.08)",
  "rgba(84,112,198,0.08)",
  "rgba(145,204,117,0.08)",
  "rgba(250,200,88,0.08)",
  "rgba(238,102,102,0.08)",
  "rgba(115,192,222,0.08)",
  "rgba(59,162,114,0.08)",
  "rgba(252,132,82,0.08)",
  "rgba(154,96,180,0.08)",
  "rgba(234,124,204,0.08)"
];
const colorRgbaEnd = [
  "rgba(45,130,254, 0)",
  "rgba(253,165,78,0)",
  "rgba(31,195,164,0)",
  "rgba(97,195,31,0)",
  "rgba(78,96,253,0)",
  "rgba(45,48,254,0)",
  "rgba(115,45,254,0)",
  "rgba(31,195,105,0)",
  "rgba(254,139,45,0)",
  "rgba(254,197,45,0)",
  "rgba(254,244,45,0)",
  "rgba(45,202,254,0)",
  "rgba(45,226,254,0)",
  "rgba(84,112,198,0)",
  "rgba(145,204,117,0)",
  "rgba(250,200,88,0)",
  "rgba(238,102,102,0)",
  "rgba(115,192,222,0)",
  "rgba(59,162,114,0)",
  "rgba(252,132,82,0)",
  "rgba(154,96,180,0)",
  "rgba(234,124,204,0)"
];
const linechartsData = [
  {
    indexName: "电价",
    indexCode: "E_PD_M_M_ZYDDH_JS_H_M",
    unitName: "元",
    type: "line",
    data: [
      5459.36,
      5474.6,
      5596.57,
      5496.15,
      5580.98,
      5474.6,
      5622.18,
      5611.82,
      5622.93,
      5597.89,
      5597.91,
      5502.64,
      5597.89,
      5597.91,
      5502.64,
      5597.89,
      5597.91,
      5502.64,
      5597.89,
      5597.91,
      5502.64,
      5597.89,
      5597.91,
      5502.64
    ],
    XAxis: [
      "00:00",
      "01:00",
      "02:00",
      "03:00",
      "04:00",
      "05:00",
      "06:00",
      "07:00",
      "08:00",
      "09:00",
      "10:00",
      "11:00",
      "12:00",
      "13:00",
      "14:00",
      "15:00",
      "16:00",
      "17:00",
      "18:00",
      "19:00",
      "20:00",
      "21:00",
      "22:00",
      "23:00"
    ]
  },
  {
    indexName: "温度",
    indexCode: "E_PD_M_M_SCGLYDDH_JS_H_M",
    unitName: "℃",
    type: "line",
    data: [
      461.51,
      499.25,
      421.85,
      414.17,
      489.81,
      414.17,
      496.07,
      407.94,
      416.38,
      402.58,
      499.56,
      468.68,
      402.58,
      499.56,
      468.68,
      402.58,
      499.56,
      468.68,
      402.58,
      499.56,
      468.68,
      402.58,
      499.56,
      468.68
    ],
    XAxis: [
      "00:00",
      "01:00",
      "02:00",
      "03:00",
      "04:00",
      "05:00",
      "06:00",
      "07:00",
      "08:00",
      "09:00",
      "10:00",
      "11:00",
      "12:00",
      "13:00",
      "14:00",
      "15:00",
      "16:00",
      "17:00",
      "18:00",
      "19:00",
      "20:00",
      "21:00",
      "22:00",
      "23:00"
    ]
  },
  {
    indexName: "储能",
    indexCode: "qtyd_jsxs",
    unitName: "kW·h",
    type: "line",
    data: [
      4997.85,
      4975.35,
      5174.72,
      5081.98,
      5091.17,
      5174.72,
      5174.72,
      5203.88,
      5174.72,
      5195.31,
      5098.35,
      5033.96,
      5195.31,
      5098.35,
      5033.96,
      5195.31,
      5098.35,
      5033.96,
      5195.31,
      5098.35,
      5033.96,
      5195.31,
      5098.35,
      5033.96
    ],
    XAxis: [
      "00:00",
      "01:00",
      "02:00",
      "03:00",
      "04:00",
      "05:00",
      "06:00",
      "07:00",
      "08:00",
      "09:00",
      "10:00",
      "11:00",
      "12:00",
      "13:00",
      "14:00",
      "15:00",
      "16:00",
      "17:00",
      "18:00",
      "19:00",
      "20:00",
      "21:00",
      "22:00",
      "23:00"
    ]
  }
];
export {
  colorRgba,
  colorRgbaEnd,
  colorRgbaStar,
  linechartsData
};

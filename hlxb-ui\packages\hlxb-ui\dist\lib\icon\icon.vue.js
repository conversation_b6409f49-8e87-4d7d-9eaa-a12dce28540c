"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const icon_vue_vue_type_script_setup_true_lang = require("./icon.vue2.js");
;/* empty css           */
const _pluginVue_exportHelper = require("../_virtual/_plugin-vue_export-helper.js");
const hlxbIcon = /* @__PURE__ */ _pluginVue_exportHelper.default(icon_vue_vue_type_script_setup_true_lang.default, [["__scopeId", "data-v-d7cc5ab6"]]);
exports.default = hlxbIcon;

import { __extends } from "../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { map } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import Scale from "./Scale.js";
import { quantity, round, getPrecision } from "../util/number.js";
import IntervalScale from "./Interval.js";
import { logTransform, getIntervalPrecision } from "./helper.js";
var fixRound = round;
var mathFloor = Math.floor;
var mathCeil = Math.ceil;
var mathPow = Math.pow;
var mathLog = Math.log;
var LogScale = (
  /** @class */
  function(_super) {
    __extends(LogScale2, _super);
    function LogScale2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = "log";
      _this.base = 10;
      _this._originalScale = new IntervalScale();
      return _this;
    }
    LogScale2.prototype.getTicks = function(opt) {
      opt = opt || {};
      var extent = this._extent.slice();
      var originalExtent = this._originalScale.getExtent();
      var ticks = _super.prototype.getTicks.call(this, opt);
      var base = this.base;
      this._originalScale._innerGetBreaks();
      return map(ticks, function(tick) {
        var val = tick.value;
        var roundingCriterion = null;
        var powVal = mathPow(base, val);
        if (val === extent[0] && this._fixMin) {
          roundingCriterion = originalExtent[0];
        } else if (val === extent[1] && this._fixMax) {
          roundingCriterion = originalExtent[1];
        }
        var vBreak;
        if (roundingCriterion != null) {
          powVal = fixRoundingError(powVal, roundingCriterion);
        }
        return {
          value: powVal,
          "break": vBreak
        };
      }, this);
    };
    LogScale2.prototype._getNonTransBreaks = function() {
      return this._originalScale._innerGetBreaks();
    };
    LogScale2.prototype.setExtent = function(start, end) {
      this._originalScale.setExtent(start, end);
      var loggedExtent = logTransform(this.base, [start, end]);
      _super.prototype.setExtent.call(this, loggedExtent[0], loggedExtent[1]);
    };
    LogScale2.prototype.getExtent = function() {
      var base = this.base;
      var extent = _super.prototype.getExtent.call(this);
      extent[0] = mathPow(base, extent[0]);
      extent[1] = mathPow(base, extent[1]);
      var originalExtent = this._originalScale.getExtent();
      this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));
      this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));
      return extent;
    };
    LogScale2.prototype.unionExtentFromData = function(data, dim) {
      this._originalScale.unionExtentFromData(data, dim);
      var loggedOther = logTransform(this.base, data.getApproximateExtent(dim), true);
      this._innerUnionExtent(loggedOther);
    };
    LogScale2.prototype.calcNiceTicks = function(approxTickNum) {
      approxTickNum = approxTickNum || 10;
      var extent = this._extent.slice();
      var span = this._getExtentSpanWithBreaks();
      if (!isFinite(span) || span <= 0) {
        return;
      }
      var interval = quantity(span);
      var err = approxTickNum / span * interval;
      if (err <= 0.5) {
        interval *= 10;
      }
      while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {
        interval *= 10;
      }
      var niceExtent = [fixRound(mathCeil(extent[0] / interval) * interval), fixRound(mathFloor(extent[1] / interval) * interval)];
      this._interval = interval;
      this._intervalPrecision = getIntervalPrecision(interval);
      this._niceExtent = niceExtent;
    };
    LogScale2.prototype.calcNiceExtent = function(opt) {
      _super.prototype.calcNiceExtent.call(this, opt);
      this._fixMin = opt.fixMin;
      this._fixMax = opt.fixMax;
    };
    LogScale2.prototype.contain = function(val) {
      val = mathLog(val) / mathLog(this.base);
      return _super.prototype.contain.call(this, val);
    };
    LogScale2.prototype.normalize = function(val) {
      val = mathLog(val) / mathLog(this.base);
      return _super.prototype.normalize.call(this, val);
    };
    LogScale2.prototype.scale = function(val) {
      val = _super.prototype.scale.call(this, val);
      return mathPow(this.base, val);
    };
    LogScale2.prototype.setBreaksFromOption = function(breakOptionList) {
      {
        return;
      }
    };
    LogScale2.type = "log";
    return LogScale2;
  }(IntervalScale)
);
function fixRoundingError(val, originalVal) {
  return fixRound(val, getPrecision(originalVal));
}
Scale.registerClass(LogScale);
export {
  LogScale as default
};

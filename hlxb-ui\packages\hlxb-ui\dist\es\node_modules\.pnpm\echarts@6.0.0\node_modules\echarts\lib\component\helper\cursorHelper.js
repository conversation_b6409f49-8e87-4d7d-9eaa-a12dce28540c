import { retrieveZInfo } from "../../util/graphic.js";
var IRRELEVANT_EXCLUDES = {
  "axisPointer": 1,
  "tooltip": 1,
  "brush": 1
};
function onIrrelevantElement(e, api, targetComponent) {
  var eventElComponent = api.getComponentByElement(e.topTarget);
  if (!eventElComponent || eventElComponent === targetComponent || IRRELEVANT_EXCLUDES.hasOwnProperty(eventElComponent.mainType)) {
    return false;
  }
  var eventElCoordSys = eventElComponent.coordinateSystem;
  if (!eventElCoordSys || eventElCoordSys.model === targetComponent) {
    return false;
  }
  var eventElCmptZInfo = retrieveZInfo(eventElComponent);
  var targetCmptZInfo = retrieveZInfo(targetComponent);
  if ((eventElCmptZInfo.zlevel - targetCmptZInfo.zlevel || eventElCmptZInfo.z - targetCmptZInfo.z) <= 0) {
    return false;
  }
  return true;
}
export {
  onIrrelevantElement
};

import _extends from "../../../../../../@babel_runtime@7.28.3/node_modules/@babel/runtime/helpers/esm/extends.js";
import { DEFAULT_CONFIG } from "../core/config.js";
import { NUMBER_OPTIONS, MATRIX_OPTIONS } from "../core/function/config.js";
var config = function config2(options) {
  if (options) {
    throw new Error("The global config is readonly. \nPlease create a mathjs instance if you want to change the default configuration. \nExample:\n\n  import { create, all } from 'mathjs';\n  const mathjs = create(all);\n  mathjs.config({ number: 'BigNumber' });\n");
  }
  return Object.freeze(DEFAULT_CONFIG);
};
_extends(config, DEFAULT_CONFIG, {
  MATRIX_OPTIONS,
  NUMBER_OPTIONS
});
export {
  config
};

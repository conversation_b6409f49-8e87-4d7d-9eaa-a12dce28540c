"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
require("../../../card/index.js");
const vue = require("vue");
const data = require("../../../card/src/data.js");
function useFilterSlots() {
  const slots = vue.useSlots();
  const filtersSlots = vue.computed(() => {
    return data.filterAllowedSlots(slots, data.cardHeaderDate);
  });
  return {
    filtersSlots
  };
}
exports.useFilterSlots = useFilterSlots;

import BoundingRect from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js";
import { onIrrelevantElement } from "./cursorHelper.js";
import { clipPointsByRect } from "../../util/graphic.js";
function makeRectPanelClipPath(rect) {
  rect = normalizeRect(rect);
  return function(localPoints) {
    return clipPointsByRect(localPoints, rect);
  };
}
function makeLinearBrushOtherExtent(rect, specifiedXYIndex) {
  rect = normalizeRect(rect);
  return function(xyIndex) {
    var idx = specifiedXYIndex != null ? specifiedXYIndex : xyIndex;
    var brushWidth = idx ? rect.width : rect.height;
    var base = idx ? rect.x : rect.y;
    return [base, base + (brushWidth || 0)];
  };
}
function makeRectIsTargetByCursor(rect, api, targetModel) {
  var boundingRect = normalizeRect(rect);
  return function(e, localCursorPoint) {
    return boundingRect.contain(localCursorPoint[0], localCursorPoint[1]) && !onIrrelevantElement(e, api, targetModel);
  };
}
function normalizeRect(rect) {
  return BoundingRect.create(rect);
}
export {
  makeLinearBrushOtherExtent,
  makeRectIsTargetByCursor,
  makeRectPanelClipPath
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const createDimensions = require("../../data/helper/createDimensions.js");
const SeriesData = require("../../data/SeriesData.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
function createSeriesDataSimply(seriesModel, opt, nameList) {
  opt = util.isArray(opt) && {
    coordDimensions: opt
  } || util.extend({
    encodeDefine: seriesModel.getEncode()
  }, opt);
  var source = seriesModel.getSource();
  var dimensions = createDimensions.default(source, opt).dimensions;
  var list = new SeriesData.default(dimensions, seriesModel);
  list.initData(source, nameList);
  return list;
}
exports.default = createSeriesDataSimply;

import RadarModel from "../../coord/radar/RadarModel.js";
import RadarView from "./RadarView.js";
import Radar from "../../coord/radar/Radar.js";
function install(registers) {
  registers.registerCoordinateSystem("radar", Radar);
  registers.registerComponentModel(RadarModel);
  registers.registerComponentView(RadarView);
  registers.registerVisual({
    seriesType: "radar",
    reset: function(seriesModel) {
      var data = seriesModel.getData();
      data.each(function(idx) {
        data.setItemVisual(idx, "legendIcon", "roundRect");
      });
      data.setVisual("legendIcon", "roundRect");
    }
  });
}
export {
  install
};

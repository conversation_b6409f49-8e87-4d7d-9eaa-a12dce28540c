import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each, curry, isString, bind, isFunction } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getBoundingRect } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/contain/text.js";
import { createIcon, setTooltipConfig } from "../../util/graphic.js";
import { enterEmphasis, leaveEmphasis } from "../../util/states.js";
import Model from "../../model/Model.js";
import DataDiffer from "../../data/DataDiffer.js";
import { makeBackground } from "../helper/listComponent.js";
import ComponentView from "../../view/Component.js";
import { getFeature, ToolboxFeature } from "./featureManager.js";
import { getUID } from "../../util/component.js";
import ZRText from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
import { getFont } from "../../label/labelStyle.js";
import { createBoxLayoutReference, getLayoutRect, box, positionElement } from "../../util/layout.js";
import tokens from "../../visual/tokens.js";
var ToolboxView = (
  /** @class */
  function(_super) {
    __extends(ToolboxView2, _super);
    function ToolboxView2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    ToolboxView2.prototype.render = function(toolboxModel, ecModel, api, payload) {
      var group = this.group;
      group.removeAll();
      if (!toolboxModel.get("show")) {
        return;
      }
      var itemSize = +toolboxModel.get("itemSize");
      var isVertical = toolboxModel.get("orient") === "vertical";
      var featureOpts = toolboxModel.get("feature") || {};
      var features = this._features || (this._features = {});
      var featureNames = [];
      each(featureOpts, function(opt, name) {
        featureNames.push(name);
      });
      new DataDiffer(this._featureNames || [], featureNames).add(processFeature).update(processFeature).remove(curry(processFeature, null)).execute();
      this._featureNames = featureNames;
      function processFeature(newIndex, oldIndex) {
        var featureName = featureNames[newIndex];
        var oldName = featureNames[oldIndex];
        var featureOpt = featureOpts[featureName];
        var featureModel = new Model(featureOpt, toolboxModel, toolboxModel.ecModel);
        var feature;
        if (payload && payload.newTitle != null && payload.featureName === featureName) {
          featureOpt.title = payload.newTitle;
        }
        if (featureName && !oldName) {
          if (isUserFeatureName(featureName)) {
            feature = {
              onclick: featureModel.option.onclick,
              featureName
            };
          } else {
            var Feature = getFeature(featureName);
            if (!Feature) {
              return;
            }
            feature = new Feature();
          }
          features[featureName] = feature;
        } else {
          feature = features[oldName];
          if (!feature) {
            return;
          }
        }
        feature.uid = getUID("toolbox-feature");
        feature.model = featureModel;
        feature.ecModel = ecModel;
        feature.api = api;
        var isToolboxFeature = feature instanceof ToolboxFeature;
        if (!featureName && oldName) {
          isToolboxFeature && feature.dispose && feature.dispose(ecModel, api);
          return;
        }
        if (!featureModel.get("show") || isToolboxFeature && feature.unusable) {
          isToolboxFeature && feature.remove && feature.remove(ecModel, api);
          return;
        }
        createIconPaths(featureModel, feature, featureName);
        featureModel.setIconStatus = function(iconName, status) {
          var option = this.option;
          var iconPaths = this.iconPaths;
          option.iconStatus = option.iconStatus || {};
          option.iconStatus[iconName] = status;
          if (iconPaths[iconName]) {
            (status === "emphasis" ? enterEmphasis : leaveEmphasis)(iconPaths[iconName]);
          }
        };
        if (feature instanceof ToolboxFeature) {
          if (feature.render) {
            feature.render(featureModel, ecModel, api, payload);
          }
        }
      }
      function createIconPaths(featureModel, feature, featureName) {
        var iconStyleModel = featureModel.getModel("iconStyle");
        var iconStyleEmphasisModel = featureModel.getModel(["emphasis", "iconStyle"]);
        var icons = feature instanceof ToolboxFeature && feature.getIcons ? feature.getIcons() : featureModel.get("icon");
        var titles = featureModel.get("title") || {};
        var iconsMap;
        var titlesMap;
        if (isString(icons)) {
          iconsMap = {};
          iconsMap[featureName] = icons;
        } else {
          iconsMap = icons;
        }
        if (isString(titles)) {
          titlesMap = {};
          titlesMap[featureName] = titles;
        } else {
          titlesMap = titles;
        }
        var iconPaths = featureModel.iconPaths = {};
        each(iconsMap, function(iconStr, iconName) {
          var path = createIcon(iconStr, {}, {
            x: -itemSize / 2,
            y: -itemSize / 2,
            width: itemSize,
            height: itemSize
          });
          path.setStyle(iconStyleModel.getItemStyle());
          var pathEmphasisState = path.ensureState("emphasis");
          pathEmphasisState.style = iconStyleEmphasisModel.getItemStyle();
          var textContent = new ZRText({
            style: {
              text: titlesMap[iconName],
              align: iconStyleEmphasisModel.get("textAlign"),
              borderRadius: iconStyleEmphasisModel.get("textBorderRadius"),
              padding: iconStyleEmphasisModel.get("textPadding"),
              fill: null,
              font: getFont({
                fontStyle: iconStyleEmphasisModel.get("textFontStyle"),
                fontFamily: iconStyleEmphasisModel.get("textFontFamily"),
                fontSize: iconStyleEmphasisModel.get("textFontSize"),
                fontWeight: iconStyleEmphasisModel.get("textFontWeight")
              }, ecModel)
            },
            ignore: true
          });
          path.setTextContent(textContent);
          setTooltipConfig({
            el: path,
            componentModel: toolboxModel,
            itemName: iconName,
            formatterParamsExtra: {
              title: titlesMap[iconName]
            }
          });
          path.__title = titlesMap[iconName];
          path.on("mouseover", function() {
            var hoverStyle = iconStyleEmphasisModel.getItemStyle();
            var defaultTextPosition = isVertical ? toolboxModel.get("right") == null && toolboxModel.get("left") !== "right" ? "right" : "left" : toolboxModel.get("bottom") == null && toolboxModel.get("top") !== "bottom" ? "bottom" : "top";
            textContent.setStyle({
              fill: iconStyleEmphasisModel.get("textFill") || hoverStyle.fill || hoverStyle.stroke || tokens.color.neutral99,
              backgroundColor: iconStyleEmphasisModel.get("textBackgroundColor")
            });
            path.setTextConfig({
              position: iconStyleEmphasisModel.get("textPosition") || defaultTextPosition
            });
            textContent.ignore = !toolboxModel.get("showTitle");
            api.enterEmphasis(this);
          }).on("mouseout", function() {
            if (featureModel.get(["iconStatus", iconName]) !== "emphasis") {
              api.leaveEmphasis(this);
            }
            textContent.hide();
          });
          (featureModel.get(["iconStatus", iconName]) === "emphasis" ? enterEmphasis : leaveEmphasis)(path);
          group.add(path);
          path.on("click", bind(feature.onclick, feature, ecModel, api, iconName));
          iconPaths[iconName] = path;
        });
      }
      var refContainer = createBoxLayoutReference(toolboxModel, api).refContainer;
      var boxLayoutParams = toolboxModel.getBoxLayoutParams();
      var padding = toolboxModel.get("padding");
      var viewRect = getLayoutRect(boxLayoutParams, refContainer, padding);
      box(toolboxModel.get("orient"), group, toolboxModel.get("itemGap"), viewRect.width, viewRect.height);
      positionElement(group, boxLayoutParams, refContainer, padding);
      group.add(makeBackground(group.getBoundingRect(), toolboxModel));
      isVertical || group.eachChild(function(icon) {
        var titleText = icon.__title;
        var emphasisState = icon.ensureState("emphasis");
        var emphasisTextConfig = emphasisState.textConfig || (emphasisState.textConfig = {});
        var textContent = icon.getTextContent();
        var emphasisTextState = textContent && textContent.ensureState("emphasis");
        if (emphasisTextState && !isFunction(emphasisTextState) && titleText) {
          var emphasisTextStyle = emphasisTextState.style || (emphasisTextState.style = {});
          var rect = getBoundingRect(titleText, ZRText.makeFont(emphasisTextStyle));
          var offsetX = icon.x + group.x;
          var offsetY = icon.y + group.y + itemSize;
          var needPutOnTop = false;
          if (offsetY + rect.height > api.getHeight()) {
            emphasisTextConfig.position = "top";
            needPutOnTop = true;
          }
          var topOffset = needPutOnTop ? -5 - rect.height : itemSize + 10;
          if (offsetX + rect.width / 2 > api.getWidth()) {
            emphasisTextConfig.position = ["100%", topOffset];
            emphasisTextStyle.align = "right";
          } else if (offsetX - rect.width / 2 < 0) {
            emphasisTextConfig.position = [0, topOffset];
            emphasisTextStyle.align = "left";
          }
        }
      });
    };
    ToolboxView2.prototype.updateView = function(toolboxModel, ecModel, api, payload) {
      each(this._features, function(feature) {
        feature instanceof ToolboxFeature && feature.updateView && feature.updateView(feature.model, ecModel, api, payload);
      });
    };
    ToolboxView2.prototype.remove = function(ecModel, api) {
      each(this._features, function(feature) {
        feature instanceof ToolboxFeature && feature.remove && feature.remove(ecModel, api);
      });
      this.group.removeAll();
    };
    ToolboxView2.prototype.dispose = function(ecModel, api) {
      each(this._features, function(feature) {
        feature instanceof ToolboxFeature && feature.dispose && feature.dispose(ecModel, api);
      });
    };
    ToolboxView2.type = "toolbox";
    return ToolboxView2;
  }(ComponentView)
);
function isUserFeatureName(featureName) {
  return featureName.indexOf("my") === 0;
}
export {
  ToolboxView as default
};

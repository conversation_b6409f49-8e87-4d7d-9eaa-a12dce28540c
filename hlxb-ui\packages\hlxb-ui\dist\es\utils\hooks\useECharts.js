import { useTimeoutFn } from "./core/useTimeout.js";
import { ref, computed, toValue, watch, unref, nextTick } from "vue";
import { useEventListener } from "./event/useEventListener.js";
import { useBreakpoint } from "./event/useBreakpoint.js";
import "../lib/echarts.js";
import { tryOnUnmounted, useDebounceFn } from "../../node_modules/.pnpm/@vueuse_shared@13.7.0_vue@3.5.20_typescript@4.9.5_/node_modules/@vueuse/shared/index.js";
import * as core from "../../node_modules/.pnpm/echarts@6.0.0/node_modules/echarts/core.js";
function useECharts(elRef, theme = "default") {
  let chartInstance = null;
  let resizeFn = resize;
  const cacheOptions = ref({});
  let removeResizeFn = () => {
  };
  resizeFn = useDebounceFn(resize, 200);
  const getOptions = computed(() => {
    if (toValue(theme) !== "dark") {
      return cacheOptions.value;
    }
    return {
      backgroundColor: "transparent",
      ...cacheOptions.value
    };
  });
  function initCharts(t = toValue(theme)) {
    const el = unref(elRef);
    if (!el || !unref(el)) {
      return;
    }
    chartInstance = core.init(el, t);
    const { removeEvent } = useEventListener({
      el: window,
      name: "resize",
      listener: resizeFn
    });
    removeResizeFn = removeEvent;
    const { widthRef, screenEnum } = useBreakpoint();
    if (unref(widthRef) <= screenEnum.MD || el.offsetHeight === 0) {
      useTimeoutFn(() => {
        resizeFn();
      }, 30);
    }
  }
  function setOptions(options, clear = true) {
    var _a;
    cacheOptions.value = options;
    if (((_a = unref(elRef)) == null ? void 0 : _a.offsetHeight) === 0) {
      useTimeoutFn(() => {
        setOptions(unref(getOptions));
      }, 30);
      return;
    }
    nextTick(() => {
      useTimeoutFn(() => {
        if (!chartInstance) {
          initCharts(toValue(theme));
          if (!chartInstance) return;
        }
        clear && (chartInstance == null ? void 0 : chartInstance.clear());
        chartInstance == null ? void 0 : chartInstance.setOption(unref(getOptions));
      }, 30);
    });
  }
  function resize() {
    chartInstance == null ? void 0 : chartInstance.resize({
      animation: {
        duration: 300,
        easing: "quadraticIn"
      }
    });
  }
  watch(
    () => toValue(theme),
    (theme2) => {
      if (chartInstance) {
        chartInstance.dispose();
        initCharts(toValue(theme2));
        setOptions(cacheOptions.value);
      }
    }
  );
  tryOnUnmounted(() => {
    if (!chartInstance) return;
    removeResizeFn();
    chartInstance.dispose();
    chartInstance = null;
  });
  function getInstance() {
    if (!chartInstance) {
      initCharts(toValue(theme));
    }
    return chartInstance;
  }
  return {
    setOptions,
    resize,
    echarts: core,
    getInstance
  };
}
export {
  useECharts
};

import { defineComponent, createElementBlock, openBlock, normalizeClass, unref, createVNode, renderSlot, normalizeProps, guardReactiveProps, createSlots, withCtx } from "vue";
import { Empty } from "ant-design-vue";
import _sfc_main$1 from "./EmptyIcon.vue.js";
import { getPrefixCls } from "../config/index.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbEmpty"
  },
  __name: "index",
  setup(__props) {
    const prefixCls = getPrefixCls("empty");
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), "flex justify-center items-center flex-col"]),
        style: { "color": "#999999" }
      }, [
        createVNode(unref(Empty), normalizeProps(guardReactiveProps(_ctx.$attrs)), createSlots({
          description: withCtx(() => [
            renderSlot(_ctx.$slots, "description")
          ]),
          _: 2
        }, [
          !_ctx.$attrs.image ? {
            name: "image",
            fn: withCtx(() => [
              renderSlot(_ctx.$slots, "image", {}, () => [
                createVNode(_sfc_main$1)
              ])
            ]),
            key: "0"
          } : void 0
        ]), 1040),
        renderSlot(_ctx.$slots, "bottom")
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

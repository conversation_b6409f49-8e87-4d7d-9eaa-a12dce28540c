import HlxbButton from "./button/index.js";
import HlxbIcon from "./icon/index.js";
import HlxbInput from "./input/index.js";
import HlxbInputNumber from "./input-number/index.js";
import HlxbRadio from "./radio/index.js";
import HlxbRadioGroup from "./radio-group/index.js";
import HlxbCheckbox from "./checkbox/index.js";
import HlxbCheckboxGroup from "./checkbox-group/index.js";
import HlxbCalendar from "./calendar/index.js";
import HlxbEmpty from "./empty/index.js";
import { HlxbEmptyIcon } from "./empty/index.js";
import HlxbGrid from "./grid/index.js";
import HlxbSwiper from "./swiper/index.js";
import HlxbWeather from "./weather/index.js";
import { HlxbCard, HlxbCardBody, HlxbCardHeader } from "./card/index.js";
import { HlxbRanking, HlxbRankingSimple, HlxbEnergySummary, HlxbDrugSummary, HlxbHorizontaSmallSquare, HlxbCardLoading, HlxbCardEmpty } from "./card-component/src/basicComponents/index.js";
import { HlxbBarEcharts, HlxbLineEcharts, HlxbPieEcharts } from "./card-component/src/charts/index.js";
import { HlxbPieSimpleCard, HlxbPiePlusCard, HlxbLineSimpleCard, HlxbLinePlusCard, HlxbBarSimpleCard, HlxbBarPlusCard, HlxbRankingSimpleCard, HlxbRankingPlusCard, HlxbSummarySimpleCard } from "./card-component/src/combinationCards/index.js";
import { HlxbUIStyleImport } from "./resolver.js";
import { default as default2 } from "./weather/src/WeatherIcon.vue.js";
import { generateFullMonthDates } from "./calendar/src/hooks/utils.js";
import { Swiper, SwiperSlide } from "./node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/swiper-vue.js";
const components = [
  HlxbButton,
  HlxbIcon,
  HlxbInput,
  HlxbInputNumber,
  HlxbRadio,
  HlxbRadioGroup,
  HlxbCheckbox,
  HlxbCheckboxGroup,
  HlxbCalendar,
  HlxbEmpty,
  HlxbGrid,
  HlxbSwiper,
  HlxbWeather,
  HlxbCard,
  HlxbCardBody,
  HlxbCardHeader,
  HlxbRanking,
  HlxbRankingSimple,
  HlxbEnergySummary,
  HlxbDrugSummary,
  HlxbHorizontaSmallSquare,
  HlxbCardLoading,
  HlxbCardEmpty,
  HlxbBarEcharts,
  HlxbLineEcharts,
  HlxbPieEcharts,
  HlxbPieSimpleCard,
  HlxbPiePlusCard,
  HlxbLineSimpleCard,
  HlxbLinePlusCard,
  HlxbBarSimpleCard,
  HlxbBarPlusCard,
  HlxbRankingSimpleCard,
  HlxbRankingPlusCard,
  HlxbSummarySimpleCard
];
const install = (app) => {
  components.forEach((component) => {
    if (!!component.install) {
      app.use(component);
    }
  });
  return app;
};
const index = { install, version: "0.3.10" };
export {
  HlxbBarEcharts,
  HlxbBarPlusCard,
  HlxbBarSimpleCard,
  HlxbButton,
  HlxbCalendar,
  HlxbCard,
  HlxbCardBody,
  HlxbCardEmpty,
  HlxbCardHeader,
  HlxbCardLoading,
  HlxbCheckbox,
  HlxbCheckboxGroup,
  HlxbDrugSummary,
  HlxbEmpty,
  HlxbEmptyIcon,
  HlxbEnergySummary,
  HlxbGrid,
  HlxbHorizontaSmallSquare,
  HlxbIcon,
  HlxbInput,
  HlxbInputNumber,
  HlxbLineEcharts,
  HlxbLinePlusCard,
  HlxbLineSimpleCard,
  HlxbPieEcharts,
  HlxbPiePlusCard,
  HlxbPieSimpleCard,
  HlxbRadio,
  HlxbRadioGroup,
  HlxbRanking,
  HlxbRankingPlusCard,
  HlxbRankingSimple,
  HlxbRankingSimpleCard,
  HlxbSummarySimpleCard,
  HlxbSwiper,
  HlxbUIStyleImport,
  HlxbWeather,
  Swiper,
  SwiperSlide,
  default2 as WeatherIcon,
  index as default,
  generateFullMonthDates,
  install
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import DataZoomModel from "./DataZoomModel.js";
import { inheritDefaultOption } from "../../util/component.js";
var InsideZoomModel = (
  /** @class */
  function(_super) {
    __extends(InsideZoomModel2, _super);
    function InsideZoomModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = InsideZoomModel2.type;
      return _this;
    }
    InsideZoomModel2.type = "dataZoom.inside";
    InsideZoomModel2.defaultOption = inheritDefaultOption(DataZoomModel.defaultOption, {
      disabled: false,
      zoomLock: false,
      zoomOnMouseWheel: true,
      moveOnMouseMove: true,
      moveOnMouseWheel: false,
      preventDefaultMouseMove: true
    });
    return InsideZoomModel2;
  }(DataZoomModel)
);
export {
  InsideZoomModel as default
};

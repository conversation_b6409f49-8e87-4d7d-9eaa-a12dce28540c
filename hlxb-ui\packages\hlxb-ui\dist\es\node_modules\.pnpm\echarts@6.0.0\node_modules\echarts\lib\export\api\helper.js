import { mixin } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import createSeriesData from "../../chart/helper/createSeriesData.js";
import { createScaleByModel, niceScaleExtent } from "../../coord/axisHelper.js";
import { AxisModelCommonMixin } from "../../coord/axisModelCommonMixin.js";
import Model from "../../model/Model.js";
import { getLayoutRect } from "../../util/layout.js";
import { getStackedDimension, enableDataStack, isDimensionStacked } from "../../data/helper/dataStackHelper.js";
import { getECData } from "../../util/innerStore.js";
import { createTextStyle as createTextStyle$1 } from "../../label/labelStyle.js";
import { createDimensions } from "../../data/helper/createDimensions.js";
import { createSymbol } from "../../util/symbol.js";
import { enableHoverEmphasis } from "../../util/states.js";
function createList(seriesModel) {
  return createSeriesData(null, seriesModel);
}
var dataStack = {
  isDimensionStacked,
  enableDataStack,
  getStackedDimension
};
function createScale(dataExtent, option) {
  var axisModel = option;
  if (!(option instanceof Model)) {
    axisModel = new Model(option);
  }
  var scale = createScaleByModel(axisModel);
  scale.setExtent(dataExtent[0], dataExtent[1]);
  niceScaleExtent(scale, axisModel);
  return scale;
}
function mixinAxisModelCommonMethods(Model2) {
  mixin(Model2, AxisModelCommonMixin);
}
function createTextStyle(textStyleModel, opts) {
  opts = opts || {};
  return createTextStyle$1(textStyleModel, null, null, opts.state !== "normal");
}
export {
  createDimensions,
  createList,
  createScale,
  createSymbol,
  createTextStyle,
  dataStack,
  enableHoverEmphasis,
  getECData,
  getLayoutRect,
  mixinAxisModelCommonMethods
};

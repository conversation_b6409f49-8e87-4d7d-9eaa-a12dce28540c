import { defineComponent, ref, watch, createElementBlock, openBlock, normalizeClass, unref, createElementVNode, Fragment, renderList, createCommentVNode, toDisplayString, normalizeStyle } from "vue";
import enImg from "../assets/images/active_r.png.js";
import { getPrefixCls } from "../../../config/index.js";
const _hoisted_1 = { class: "item-list" };
const _hoisted_2 = ["onClick"];
const _hoisted_3 = { class: "top-title" };
const _hoisted_4 = { class: "item-index" };
const _hoisted_5 = { class: "num-index" };
const _hoisted_6 = { class: "label" };
const _hoisted_7 = { class: "item-content" };
const _hoisted_8 = { class: "value" };
const _hoisted_9 = { class: "unit" };
const _hoisted_10 = ["src"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbRanking"
  },
  __name: "Ranking",
  props: {
    className: {
      type: String,
      default: "ranking"
    },
    dataList: {
      type: Array,
      default: () => []
    },
    // 是否点击联动
    clickFlags: {
      type: Boolean,
      default: true
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  emits: ["setItem"],
  setup(__props, { emit: __emit }) {
    const prefixCls = getPrefixCls("ranking");
    const props = __props;
    const setIndex = ref(null);
    const itemVal = ref("");
    const emit = __emit;
    function setItem(item, index) {
      console.log("item", item);
      if (props.clickFlags) {
        itemVal.value = item.tag;
        setIndex.value = index;
        emit("setItem", { value: itemVal.value });
      }
    }
    function getWidth(index) {
      const lengthN = props.dataList.length;
      if (lengthN > 0) {
        const setupN = Math.floor(100 / lengthN);
        const widthN = Math.floor(setupN * (lengthN - index));
        return `${widthN}%`;
      } else {
        return "100%";
      }
    }
    async function getTimeList() {
    }
    watch(
      () => props.dataList,
      async (val) => {
        setIndex.value = null;
        console.log("ranking123", val);
      }
    );
    getTimeList();
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        createElementVNode("div", _hoisted_1, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(props.dataList, (item, index) => {
            return openBlock(), createElementBlock("div", {
              key: index,
              onClick: ($event) => __props.clickFlags ? setItem(item, index) : "",
              class: normalizeClass({
                item: true,
                activeItem: __props.clickFlags && index === setIndex.value,
                hoverItem: __props.clickFlags
              })
            }, [
              createElementVNode("div", _hoisted_3, [
                createElementVNode("div", _hoisted_4, [
                  createElementVNode("div", _hoisted_5, toDisplayString(index + 1), 1),
                  createElementVNode("div", _hoisted_6, toDisplayString(item.indexName), 1)
                ]),
                createElementVNode("div", _hoisted_7, [
                  createElementVNode("div", _hoisted_8, toDisplayString(item.value || "-"), 1),
                  createElementVNode("div", _hoisted_9, toDisplayString(item.unitName), 1)
                ])
              ]),
              createElementVNode("div", {
                class: normalizeClass(["bar-box", index < 3 ? "abar-box" : ""])
              }, [
                createElementVNode("div", {
                  style: normalizeStyle({ width: getWidth(index) }),
                  class: normalizeClass(["bar-conter", index < 3 ? "abar-conter" : ""])
                }, null, 6)
              ], 2),
              __props.clickFlags && index === setIndex.value ? (openBlock(), createElementBlock("img", {
                key: 0,
                class: "img_box",
                src: unref(enImg)
              }, null, 8, _hoisted_10)) : createCommentVNode("", true)
            ], 10, _hoisted_2);
          }), 128))
        ])
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

import { defineComponent, getCurrentInstance, provide, renderSlot } from "vue";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCheckboxGroup"
  },
  __name: "checkboxGroup",
  props: {
    modelValue: [<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose }) {
    const instance = getCurrentInstance();
    const props = __props;
    provide("CheckboxGroup", instance == null ? void 0 : instance.proxy);
    __expose({
      modelValue: props.modelValue
    });
    return (_ctx, _cache) => {
      return renderSlot(_ctx.$slots, "default", {}, void 0, true);
    };
  }
});
export {
  _sfc_main as default
};

import { defineComponent, computed, createElementBlock, openBlock, normalizeClass, createElementVNode } from "vue";
import "../assets/js/iconfont.js";
import { getPrefixCls } from "../config/index.js";
const _hoisted_1 = ["xlink:href"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{ name: "HlxbIcon" },
  __name: "icon",
  props: {
    name: {
      type: String,
      default: ""
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const prefixCls = getPrefixCls("icon");
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const IconName = computed(() => `#icon-${props.name}`);
    const handlerClick = () => {
      emit("click");
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("svg", {
        class: normalizeClass(styleClass.value),
        "aria-hidden": "true",
        onClick: handlerClick
      }, [
        createElementVNode("use", { "xlink:href": IconName.value }, null, 8, _hoisted_1)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

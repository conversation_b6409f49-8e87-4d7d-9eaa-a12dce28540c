"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCheckboxGroup"
  },
  __name: "checkboxGroup",
  props: {
    modelValue: [Boolean, Array]
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose }) {
    const instance = vue.getCurrentInstance();
    const props = __props;
    vue.provide("CheckboxGroup", instance == null ? void 0 : instance.proxy);
    __expose({
      modelValue: props.modelValue
    });
    return (_ctx, _cache) => {
      return vue.renderSlot(_ctx.$slots, "default", {}, void 0, true);
    };
  }
});
exports.default = _sfc_main;

import { defaults } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function installTimelineAction(registers) {
  registers.registerAction({
    type: "timelineChange",
    event: "timelineChanged",
    update: "prepareAndUpdate"
  }, function(payload, ecModel, api) {
    var timelineModel = ecModel.getComponent("timeline");
    if (timelineModel && payload.currentIndex != null) {
      timelineModel.setCurrentIndex(payload.currentIndex);
      if (!timelineModel.get("loop", true) && timelineModel.isIndexMax() && timelineModel.getPlayState()) {
        timelineModel.setPlayState(false);
        api.dispatchAction({
          type: "timelinePlayChange",
          playState: false,
          from: payload.from
        });
      }
    }
    ecModel.resetOption("timeline", {
      replaceMerge: timelineModel.get("replaceMerge", true)
    });
    return defaults({
      currentIndex: timelineModel.option.currentIndex
    }, payload);
  });
  registers.registerAction({
    type: "timelinePlayChange",
    event: "timelinePlayChanged",
    update: "update"
  }, function(payload, ecModel) {
    var timelineModel = ecModel.getComponent("timeline");
    if (timelineModel && payload.playState != null) {
      timelineModel.setPlayState(payload.playState);
    }
  });
}
export {
  installTimelineAction
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import SymbolDraw from "../../chart/helper/SymbolDraw.js";
import { parsePercent } from "../../util/number.js";
import SeriesData from "../../data/SeriesData.js";
import { dataTransform, dataFilter, createMarkerDimValueGetter } from "./markerHelper.js";
import MarkerView from "./MarkerView.js";
import MarkerModel from "./MarkerModel.js";
import { isFunction, retrieve2, map, extend, curry, filter } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getECData } from "../../util/innerStore.js";
import { getVisualFromData } from "../../visual/helper.js";
function updateMarkerLayout(mpData, seriesModel, api) {
  var coordSys = seriesModel.coordinateSystem;
  var apiWidth = api.getWidth();
  var apiHeight = api.getHeight();
  var coordRect = coordSys && coordSys.getArea && coordSys.getArea();
  mpData.each(function(idx) {
    var itemModel = mpData.getItemModel(idx);
    var isRelativeToCoordinate = itemModel.get("relativeTo") === "coordinate";
    var width = isRelativeToCoordinate ? coordRect ? coordRect.width : 0 : apiWidth;
    var height = isRelativeToCoordinate ? coordRect ? coordRect.height : 0 : apiHeight;
    var left = isRelativeToCoordinate && coordRect ? coordRect.x : 0;
    var top = isRelativeToCoordinate && coordRect ? coordRect.y : 0;
    var point;
    var xPx = parsePercent(itemModel.get("x"), width) + left;
    var yPx = parsePercent(itemModel.get("y"), height) + top;
    if (!isNaN(xPx) && !isNaN(yPx)) {
      point = [xPx, yPx];
    } else if (seriesModel.getMarkerPosition) {
      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));
    } else if (coordSys) {
      var x = mpData.get(coordSys.dimensions[0], idx);
      var y = mpData.get(coordSys.dimensions[1], idx);
      point = coordSys.dataToPoint([x, y]);
    }
    if (!isNaN(xPx)) {
      point[0] = xPx;
    }
    if (!isNaN(yPx)) {
      point[1] = yPx;
    }
    mpData.setItemLayout(idx, point);
  });
}
var MarkPointView = (
  /** @class */
  function(_super) {
    __extends(MarkPointView2, _super);
    function MarkPointView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkPointView2.type;
      return _this;
    }
    MarkPointView2.prototype.updateTransform = function(markPointModel, ecModel, api) {
      ecModel.eachSeries(function(seriesModel) {
        var mpModel = MarkerModel.getMarkerModelFromSeries(seriesModel, "markPoint");
        if (mpModel) {
          updateMarkerLayout(mpModel.getData(), seriesModel, api);
          this.markerGroupMap.get(seriesModel.id).updateLayout();
        }
      }, this);
    };
    MarkPointView2.prototype.renderSeries = function(seriesModel, mpModel, ecModel, api) {
      var coordSys = seriesModel.coordinateSystem;
      var seriesId = seriesModel.id;
      var seriesData = seriesModel.getData();
      var symbolDrawMap = this.markerGroupMap;
      var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw());
      var mpData = createData(coordSys, seriesModel, mpModel);
      mpModel.setData(mpData);
      updateMarkerLayout(mpModel.getData(), seriesModel, api);
      mpData.each(function(idx) {
        var itemModel = mpData.getItemModel(idx);
        var symbol = itemModel.getShallow("symbol");
        var symbolSize = itemModel.getShallow("symbolSize");
        var symbolRotate = itemModel.getShallow("symbolRotate");
        var symbolOffset = itemModel.getShallow("symbolOffset");
        var symbolKeepAspect = itemModel.getShallow("symbolKeepAspect");
        if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {
          var rawIdx = mpModel.getRawValue(idx);
          var dataParams = mpModel.getDataParams(idx);
          if (isFunction(symbol)) {
            symbol = symbol(rawIdx, dataParams);
          }
          if (isFunction(symbolSize)) {
            symbolSize = symbolSize(rawIdx, dataParams);
          }
          if (isFunction(symbolRotate)) {
            symbolRotate = symbolRotate(rawIdx, dataParams);
          }
          if (isFunction(symbolOffset)) {
            symbolOffset = symbolOffset(rawIdx, dataParams);
          }
        }
        var style = itemModel.getModel("itemStyle").getItemStyle();
        var z2 = itemModel.get("z2");
        var color = getVisualFromData(seriesData, "color");
        if (!style.fill) {
          style.fill = color;
        }
        mpData.setItemVisual(idx, {
          z2: retrieve2(z2, 0),
          symbol,
          symbolSize,
          symbolRotate,
          symbolOffset,
          symbolKeepAspect,
          style
        });
      });
      symbolDraw.updateData(mpData);
      this.group.add(symbolDraw.group);
      mpData.eachItemGraphicEl(function(el) {
        el.traverse(function(child) {
          getECData(child).dataModel = mpModel;
        });
      });
      this.markKeep(symbolDraw);
      symbolDraw.group.silent = mpModel.get("silent") || seriesModel.get("silent");
    };
    MarkPointView2.type = "markPoint";
    return MarkPointView2;
  }(MarkerView)
);
function createData(coordSys, seriesModel, mpModel) {
  var coordDimsInfos;
  if (coordSys) {
    coordDimsInfos = map(coordSys && coordSys.dimensions, function(coordDim) {
      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};
      return extend(extend({}, info), {
        name: coordDim,
        // DON'T use ordinalMeta to parse and collect ordinal.
        ordinalMeta: null
      });
    });
  } else {
    coordDimsInfos = [{
      name: "value",
      type: "float"
    }];
  }
  var mpData = new SeriesData(coordDimsInfos, mpModel);
  var dataOpt = map(mpModel.get("data"), curry(dataTransform, seriesModel));
  if (coordSys) {
    dataOpt = filter(dataOpt, curry(dataFilter, coordSys));
  }
  var dimValueGetter = createMarkerDimValueGetter(!!coordSys, coordDimsInfos);
  mpData.initData(dataOpt, null, dimValueGetter);
  return mpData;
}
export {
  MarkPointView as default
};

import { defineComponent, createElementBlock, openBlock, normalizeClass, normalizeStyle, unref, Fragment, renderList, renderSlot } from "vue";
import { cardBodyDate } from "./data.js";
import { getPrefixCls } from "../../config/index.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCardBody",
    inheritAttrs: false
  },
  __name: "CardBody",
  props: {
    // 卡片主体的额外类名，类型为字符串，默认值为空字符串
    bodyClassName: {
      type: String,
      default: ""
    },
    // 卡片主体的样式对象，类型为 CSSProperties，默认值为空对象
    bodyStyleDate: {
      type: Object,
      default: () => {
      }
    },
    // 深色模式 浅色 大屏等其他主题色
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("card-body");
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        style: normalizeStyle(__props.bodyStyleDate ? __props.bodyStyleDate : ""),
        class: normalizeClass([unref(prefixCls), __props.bodyClassName ? __props.bodyClassName : "", __props.themeColor])
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(cardBodyDate).slots, (data) => {
          return renderSlot(_ctx.$slots, data, { key: data });
        }), 128))
      ], 6);
    };
  }
});
export {
  _sfc_main as default
};

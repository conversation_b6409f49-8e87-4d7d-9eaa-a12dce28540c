import { factory } from "../../utils/factory.js";
import { addNumber } from "../../plain/number/arithmetic.js";
var name = "addScalar";
var dependencies = ["typed"];
var createAddScalar = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed
  } = _ref;
  return typed(name, {
    "number, number": addNumber,
    "Complex, Complex": function Complex_Complex(x, y) {
      return x.add(y);
    },
    "BigNumber, BigNumber": function BigNumber_BigNumber(x, y) {
      return x.plus(y);
    },
    "bigint, bigint": function bigint_bigint(x, y) {
      return x + y;
    },
    "Fraction, Fraction": function Fraction_Fraction(x, y) {
      return x.add(y);
    },
    "Unit, Unit": typed.referToSelf((self) => (x, y) => {
      if (x.value === null || x.value === void 0) {
        throw new Error("Parameter x contains a unit with undefined value");
      }
      if (y.value === null || y.value === void 0) {
        throw new Error("Parameter y contains a unit with undefined value");
      }
      if (!x.equalBase(y)) throw new Error("Units do not match");
      var res = x.clone();
      res.value = typed.find(self, [res.valueType(), y.valueType()])(res.value, y.value);
      res.fixPrefix = false;
      return res;
    })
  });
});
export {
  createAddScalar
};

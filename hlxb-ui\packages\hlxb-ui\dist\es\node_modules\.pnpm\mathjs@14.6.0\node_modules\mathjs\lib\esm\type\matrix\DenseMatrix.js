import { isArray, isIndex, isMatrix, isCollection, isBigNumber, isNumber, isString, typeOf } from "../../utils/is.js";
import { getArrayDataType, get, validateIndex, arraySize, broadcastTo, unsqueeze, resize, reshape, processSizesWildcard, validate } from "../../utils/array.js";
import { format } from "../../utils/string.js";
import { isInteger } from "../../utils/number.js";
import { deepStrictEqual, clone } from "../../utils/object.js";
import { DimensionError } from "../../error/DimensionError.js";
import { factory } from "../../utils/factory.js";
import { optimizeCallback } from "../../utils/optimizeCallback.js";
var name = "DenseMatrix";
var dependencies = ["Matrix"];
var createDenseMatrixClass = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    Matrix
  } = _ref;
  function DenseMatrix(data, datatype) {
    if (!(this instanceof DenseMatrix)) {
      throw new SyntaxError("Constructor must be called with the new operator");
    }
    if (datatype && !isString(datatype)) {
      throw new Error("Invalid datatype: " + datatype);
    }
    if (isMatrix(data)) {
      if (data.type === "DenseMatrix") {
        this._data = clone(data._data);
        this._size = clone(data._size);
        this._datatype = datatype || data._datatype;
      } else {
        this._data = data.toArray();
        this._size = data.size();
        this._datatype = datatype || data._datatype;
      }
    } else if (data && isArray(data.data) && isArray(data.size)) {
      this._data = data.data;
      this._size = data.size;
      validate(this._data, this._size);
      this._datatype = datatype || data.datatype;
    } else if (isArray(data)) {
      this._data = preprocess(data);
      this._size = arraySize(this._data);
      validate(this._data, this._size);
      this._datatype = datatype;
    } else if (data) {
      throw new TypeError("Unsupported type of data (" + typeOf(data) + ")");
    } else {
      this._data = [];
      this._size = [0];
      this._datatype = datatype;
    }
  }
  DenseMatrix.prototype = new Matrix();
  DenseMatrix.prototype.createDenseMatrix = function(data, datatype) {
    return new DenseMatrix(data, datatype);
  };
  Object.defineProperty(DenseMatrix, "name", {
    value: "DenseMatrix"
  });
  DenseMatrix.prototype.constructor = DenseMatrix;
  DenseMatrix.prototype.type = "DenseMatrix";
  DenseMatrix.prototype.isDenseMatrix = true;
  DenseMatrix.prototype.getDataType = function() {
    return getArrayDataType(this._data, typeOf);
  };
  DenseMatrix.prototype.storage = function() {
    return "dense";
  };
  DenseMatrix.prototype.datatype = function() {
    return this._datatype;
  };
  DenseMatrix.prototype.create = function(data, datatype) {
    return new DenseMatrix(data, datatype);
  };
  DenseMatrix.prototype.subset = function(index, replacement, defaultValue) {
    switch (arguments.length) {
      case 1:
        return _get(this, index);
      case 2:
      case 3:
        return _set(this, index, replacement, defaultValue);
      default:
        throw new SyntaxError("Wrong number of arguments");
    }
  };
  DenseMatrix.prototype.get = function(index) {
    return get(this._data, index);
  };
  DenseMatrix.prototype.set = function(index, value, defaultValue) {
    if (!isArray(index)) {
      throw new TypeError("Array expected");
    }
    if (index.length < this._size.length) {
      throw new DimensionError(index.length, this._size.length, "<");
    }
    var i, ii, indexI;
    var size = index.map(function(i2) {
      return i2 + 1;
    });
    _fit(this, size, defaultValue);
    var data = this._data;
    for (i = 0, ii = index.length - 1; i < ii; i++) {
      indexI = index[i];
      validateIndex(indexI, data.length);
      data = data[indexI];
    }
    indexI = index[index.length - 1];
    validateIndex(indexI, data.length);
    data[indexI] = value;
    return this;
  };
  function _get(matrix, index) {
    if (!isIndex(index)) {
      throw new TypeError("Invalid index");
    }
    var isScalar = index.isScalar();
    if (isScalar) {
      return matrix.get(index.min());
    } else {
      var size = index.size();
      if (size.length !== matrix._size.length) {
        throw new DimensionError(size.length, matrix._size.length);
      }
      var min = index.min();
      var max = index.max();
      for (var i = 0, ii = matrix._size.length; i < ii; i++) {
        validateIndex(min[i], matrix._size[i]);
        validateIndex(max[i], matrix._size[i]);
      }
      var returnMatrix = new DenseMatrix([]);
      var submatrix = _getSubmatrix(matrix._data, index);
      returnMatrix._size = submatrix.size;
      returnMatrix._datatype = matrix._datatype;
      returnMatrix._data = submatrix.data;
      return returnMatrix;
    }
  }
  function _getSubmatrix(data, index) {
    var maxDepth = index.size().length - 1;
    var size = Array(maxDepth);
    return {
      data: getSubmatrixRecursive(data),
      size
    };
    function getSubmatrixRecursive(data2) {
      var depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      var ranges = index.dimension(depth);
      size[depth] = ranges.size()[0];
      if (depth < maxDepth) {
        return ranges.map((rangeIndex) => {
          validateIndex(rangeIndex, data2.length);
          return getSubmatrixRecursive(data2[rangeIndex], depth + 1);
        }).valueOf();
      } else {
        return ranges.map((rangeIndex) => {
          validateIndex(rangeIndex, data2.length);
          return data2[rangeIndex];
        }).valueOf();
      }
    }
  }
  function _set(matrix, index, submatrix, defaultValue) {
    if (!index || index.isIndex !== true) {
      throw new TypeError("Invalid index");
    }
    var iSize = index.size();
    var isScalar = index.isScalar();
    var sSize;
    if (isMatrix(submatrix)) {
      sSize = submatrix.size();
      submatrix = submatrix.valueOf();
    } else {
      sSize = arraySize(submatrix);
    }
    if (isScalar) {
      if (sSize.length !== 0) {
        throw new TypeError("Scalar expected");
      }
      matrix.set(index.min(), submatrix, defaultValue);
    } else {
      if (!deepStrictEqual(sSize, iSize)) {
        try {
          if (sSize.length === 0) {
            submatrix = broadcastTo([submatrix], iSize);
          } else {
            submatrix = broadcastTo(submatrix, iSize);
          }
          sSize = arraySize(submatrix);
        } catch (_unused) {
        }
      }
      if (iSize.length < matrix._size.length) {
        throw new DimensionError(iSize.length, matrix._size.length, "<");
      }
      if (sSize.length < iSize.length) {
        var i = 0;
        var outer = 0;
        while (iSize[i] === 1 && sSize[i] === 1) {
          i++;
        }
        while (iSize[i] === 1) {
          outer++;
          i++;
        }
        submatrix = unsqueeze(submatrix, iSize.length, outer, sSize);
      }
      if (!deepStrictEqual(iSize, sSize)) {
        throw new DimensionError(iSize, sSize, ">");
      }
      var size = index.max().map(function(i2) {
        return i2 + 1;
      });
      _fit(matrix, size, defaultValue);
      _setSubmatrix(matrix._data, index, submatrix);
    }
    return matrix;
  }
  function _setSubmatrix(data, index, submatrix) {
    var maxDepth = index.size().length - 1;
    setSubmatrixRecursive(data, submatrix);
    function setSubmatrixRecursive(data2, submatrix2) {
      var depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;
      var range = index.dimension(depth);
      if (depth < maxDepth) {
        range.forEach((rangeIndex, i) => {
          validateIndex(rangeIndex, data2.length);
          setSubmatrixRecursive(data2[rangeIndex], submatrix2[i[0]], depth + 1);
        });
      } else {
        range.forEach((rangeIndex, i) => {
          validateIndex(rangeIndex, data2.length);
          data2[rangeIndex] = submatrix2[i[0]];
        });
      }
    }
  }
  DenseMatrix.prototype.resize = function(size, defaultValue, copy) {
    if (!isCollection(size)) {
      throw new TypeError("Array or Matrix expected");
    }
    var sizeArray = size.valueOf().map((value) => {
      return Array.isArray(value) && value.length === 1 ? value[0] : value;
    });
    var m = copy ? this.clone() : this;
    return _resize(m, sizeArray, defaultValue);
  };
  function _resize(matrix, size, defaultValue) {
    if (size.length === 0) {
      var v = matrix._data;
      while (isArray(v)) {
        v = v[0];
      }
      return v;
    }
    matrix._size = size.slice(0);
    matrix._data = resize(matrix._data, matrix._size, defaultValue);
    return matrix;
  }
  DenseMatrix.prototype.reshape = function(size, copy) {
    var m = copy ? this.clone() : this;
    m._data = reshape(m._data, size);
    var currentLength = m._size.reduce((length, size2) => length * size2);
    m._size = processSizesWildcard(size, currentLength);
    return m;
  };
  function _fit(matrix, size, defaultValue) {
    var newSize = matrix._size.slice(0);
    var changed = false;
    while (newSize.length < size.length) {
      newSize.push(0);
      changed = true;
    }
    for (var i = 0, ii = size.length; i < ii; i++) {
      if (size[i] > newSize[i]) {
        newSize[i] = size[i];
        changed = true;
      }
    }
    if (changed) {
      _resize(matrix, newSize, defaultValue);
    }
  }
  DenseMatrix.prototype.clone = function() {
    var m = new DenseMatrix({
      data: clone(this._data),
      size: clone(this._size),
      datatype: this._datatype
    });
    return m;
  };
  DenseMatrix.prototype.size = function() {
    return this._size.slice(0);
  };
  DenseMatrix.prototype.map = function(callback) {
    var isUnary = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
    var me = this;
    var maxDepth = me._size.length - 1;
    if (maxDepth < 0) return me.clone();
    var fastCallback = optimizeCallback(callback, me, "map", isUnary);
    var fastCallbackFn = fastCallback.fn;
    var result = me.create(void 0, me._datatype);
    result._size = me._size;
    if (isUnary || fastCallback.isUnary) {
      result._data = iterateUnary(me._data);
      return result;
    }
    if (maxDepth === 0) {
      var inputData = me.valueOf();
      var data = Array(inputData.length);
      for (var i = 0; i < inputData.length; i++) {
        data[i] = fastCallbackFn(inputData[i], [i], me);
      }
      result._data = data;
      return result;
    }
    var index = [];
    result._data = iterate(me._data);
    return result;
    function iterate(data2) {
      var depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      var result2 = Array(data2.length);
      if (depth < maxDepth) {
        for (var _i = 0; _i < data2.length; _i++) {
          index[depth] = _i;
          result2[_i] = iterate(data2[_i], depth + 1);
        }
      } else {
        for (var _i2 = 0; _i2 < data2.length; _i2++) {
          index[depth] = _i2;
          result2[_i2] = fastCallbackFn(data2[_i2], index.slice(), me);
        }
      }
      return result2;
    }
    function iterateUnary(data2) {
      var depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      var result2 = Array(data2.length);
      if (depth < maxDepth) {
        for (var _i3 = 0; _i3 < data2.length; _i3++) {
          result2[_i3] = iterateUnary(data2[_i3], depth + 1);
        }
      } else {
        for (var _i4 = 0; _i4 < data2.length; _i4++) {
          result2[_i4] = fastCallbackFn(data2[_i4]);
        }
      }
      return result2;
    }
  };
  DenseMatrix.prototype.forEach = function(callback) {
    var isUnary = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
    var me = this;
    var maxDepth = me._size.length - 1;
    if (maxDepth < 0) return;
    var fastCallback = optimizeCallback(callback, me, "map", isUnary);
    var fastCallbackFn = fastCallback.fn;
    if (isUnary || fastCallback.isUnary) {
      iterateUnary(me._data);
      return;
    }
    if (maxDepth === 0) {
      for (var i = 0; i < me._data.length; i++) {
        fastCallbackFn(me._data[i], [i], me);
      }
      return;
    }
    var index = [];
    iterate(me._data);
    function iterate(data) {
      var depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      if (depth < maxDepth) {
        for (var _i5 = 0; _i5 < data.length; _i5++) {
          index[depth] = _i5;
          iterate(data[_i5], depth + 1);
        }
      } else {
        for (var _i6 = 0; _i6 < data.length; _i6++) {
          index[depth] = _i6;
          fastCallbackFn(data[_i6], index.slice(), me);
        }
      }
    }
    function iterateUnary(data) {
      var depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;
      if (depth < maxDepth) {
        for (var _i7 = 0; _i7 < data.length; _i7++) {
          iterateUnary(data[_i7], depth + 1);
        }
      } else {
        for (var _i8 = 0; _i8 < data.length; _i8++) {
          fastCallbackFn(data[_i8]);
        }
      }
    }
  };
  DenseMatrix.prototype[Symbol.iterator] = function* () {
    var maxDepth = this._size.length - 1;
    if (maxDepth < 0) {
      return;
    }
    if (maxDepth === 0) {
      for (var i = 0; i < this._data.length; i++) {
        yield {
          value: this._data[i],
          index: [i]
        };
      }
      return;
    }
    var index = [];
    var _recurse = function* recurse(value, depth) {
      if (depth < maxDepth) {
        for (var _i9 = 0; _i9 < value.length; _i9++) {
          index[depth] = _i9;
          yield* _recurse(value[_i9], depth + 1);
        }
      } else {
        for (var _i0 = 0; _i0 < value.length; _i0++) {
          index[depth] = _i0;
          yield {
            value: value[_i0],
            index: index.slice()
          };
        }
      }
    };
    yield* _recurse(this._data, 0);
  };
  DenseMatrix.prototype.rows = function() {
    var result = [];
    var s = this.size();
    if (s.length !== 2) {
      throw new TypeError("Rows can only be returned for a 2D matrix.");
    }
    var data = this._data;
    for (var row of data) {
      result.push(new DenseMatrix([row], this._datatype));
    }
    return result;
  };
  DenseMatrix.prototype.columns = function() {
    var _this = this;
    var result = [];
    var s = this.size();
    if (s.length !== 2) {
      throw new TypeError("Rows can only be returned for a 2D matrix.");
    }
    var data = this._data;
    var _loop = function _loop2(i2) {
      var col = data.map((row) => [row[i2]]);
      result.push(new DenseMatrix(col, _this._datatype));
    };
    for (var i = 0; i < s[1]; i++) {
      _loop(i);
    }
    return result;
  };
  DenseMatrix.prototype.toArray = function() {
    return clone(this._data);
  };
  DenseMatrix.prototype.valueOf = function() {
    return this._data;
  };
  DenseMatrix.prototype.format = function(options) {
    return format(this._data, options);
  };
  DenseMatrix.prototype.toString = function() {
    return format(this._data);
  };
  DenseMatrix.prototype.toJSON = function() {
    return {
      mathjs: "DenseMatrix",
      data: this._data,
      size: this._size,
      datatype: this._datatype
    };
  };
  DenseMatrix.prototype.diagonal = function(k) {
    if (k) {
      if (isBigNumber(k)) {
        k = k.toNumber();
      }
      if (!isNumber(k) || !isInteger(k)) {
        throw new TypeError("The parameter k must be an integer number");
      }
    } else {
      k = 0;
    }
    var kSuper = k > 0 ? k : 0;
    var kSub = k < 0 ? -k : 0;
    var rows = this._size[0];
    var columns = this._size[1];
    var n = Math.min(rows - kSub, columns - kSuper);
    var data = [];
    for (var i = 0; i < n; i++) {
      data[i] = this._data[i + kSub][i + kSuper];
    }
    return new DenseMatrix({
      data,
      size: [n],
      datatype: this._datatype
    });
  };
  DenseMatrix.diagonal = function(size, value, k, defaultValue) {
    if (!isArray(size)) {
      throw new TypeError("Array expected, size parameter");
    }
    if (size.length !== 2) {
      throw new Error("Only two dimensions matrix are supported");
    }
    size = size.map(function(s) {
      if (isBigNumber(s)) {
        s = s.toNumber();
      }
      if (!isNumber(s) || !isInteger(s) || s < 1) {
        throw new Error("Size values must be positive integers");
      }
      return s;
    });
    if (k) {
      if (isBigNumber(k)) {
        k = k.toNumber();
      }
      if (!isNumber(k) || !isInteger(k)) {
        throw new TypeError("The parameter k must be an integer number");
      }
    } else {
      k = 0;
    }
    var kSuper = k > 0 ? k : 0;
    var kSub = k < 0 ? -k : 0;
    var rows = size[0];
    var columns = size[1];
    var n = Math.min(rows - kSub, columns - kSuper);
    var _value;
    if (isArray(value)) {
      if (value.length !== n) {
        throw new Error("Invalid value array length");
      }
      _value = function _value2(i) {
        return value[i];
      };
    } else if (isMatrix(value)) {
      var ms = value.size();
      if (ms.length !== 1 || ms[0] !== n) {
        throw new Error("Invalid matrix length");
      }
      _value = function _value2(i) {
        return value.get([i]);
      };
    } else {
      _value = function _value2() {
        return value;
      };
    }
    if (!defaultValue) {
      defaultValue = isBigNumber(_value(0)) ? _value(0).mul(0) : 0;
    }
    var data = [];
    if (size.length > 0) {
      data = resize(data, size, defaultValue);
      for (var d = 0; d < n; d++) {
        data[d + kSub][d + kSuper] = _value(d);
      }
    }
    return new DenseMatrix({
      data,
      size: [rows, columns]
    });
  };
  DenseMatrix.fromJSON = function(json) {
    return new DenseMatrix(json);
  };
  DenseMatrix.prototype.swapRows = function(i, j) {
    if (!isNumber(i) || !isInteger(i) || !isNumber(j) || !isInteger(j)) {
      throw new Error("Row index must be positive integers");
    }
    if (this._size.length !== 2) {
      throw new Error("Only two dimensional matrix is supported");
    }
    validateIndex(i, this._size[0]);
    validateIndex(j, this._size[0]);
    DenseMatrix._swapRows(i, j, this._data);
    return this;
  };
  DenseMatrix._swapRows = function(i, j, data) {
    var vi = data[i];
    data[i] = data[j];
    data[j] = vi;
  };
  function preprocess(data) {
    if (isMatrix(data)) {
      return preprocess(data.valueOf());
    }
    if (isArray(data)) {
      return data.map(preprocess);
    }
    return data;
  }
  return DenseMatrix;
}, {
  isClass: true
});
export {
  createDenseMatrixClass
};

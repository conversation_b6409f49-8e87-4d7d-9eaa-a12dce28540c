import { error } from "../util/log.js";
var implsStore = {};
function registerImpl(name, impl) {
  if (process.env.NODE_ENV !== "production") {
    if (implsStore[name]) {
      error("Already has an implementation of " + name + ".");
    }
  }
  implsStore[name] = impl;
}
function getImpl(name) {
  if (process.env.NODE_ENV !== "production") {
    if (!implsStore[name]) {
      error("Implementation of " + name + " doesn't exists.");
    }
  }
  return implsStore[name];
}
export {
  getImpl,
  registerImpl
};

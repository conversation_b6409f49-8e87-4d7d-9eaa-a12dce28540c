import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentModel from "../../model/Component.js";
import SeriesData from "../../data/SeriesData.js";
import { each, isObject, clone } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { convertOptionIdName, getDataItemValue } from "../../util/model.js";
import tokens from "../../visual/tokens.js";
var TimelineModel = (
  /** @class */
  function(_super) {
    __extends(TimelineModel2, _super);
    function TimelineModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = TimelineModel2.type;
      _this.layoutMode = "box";
      return _this;
    }
    TimelineModel2.prototype.init = function(option, parentModel, ecModel) {
      this.mergeDefaultAndTheme(option, ecModel);
      this._initData();
    };
    TimelineModel2.prototype.mergeOption = function(option) {
      _super.prototype.mergeOption.apply(this, arguments);
      this._initData();
    };
    TimelineModel2.prototype.setCurrentIndex = function(currentIndex) {
      if (currentIndex == null) {
        currentIndex = this.option.currentIndex;
      }
      var count = this._data.count();
      if (this.option.loop) {
        currentIndex = (currentIndex % count + count) % count;
      } else {
        currentIndex >= count && (currentIndex = count - 1);
        currentIndex < 0 && (currentIndex = 0);
      }
      this.option.currentIndex = currentIndex;
    };
    TimelineModel2.prototype.getCurrentIndex = function() {
      return this.option.currentIndex;
    };
    TimelineModel2.prototype.isIndexMax = function() {
      return this.getCurrentIndex() >= this._data.count() - 1;
    };
    TimelineModel2.prototype.setPlayState = function(state) {
      this.option.autoPlay = !!state;
    };
    TimelineModel2.prototype.getPlayState = function() {
      return !!this.option.autoPlay;
    };
    TimelineModel2.prototype._initData = function() {
      var thisOption = this.option;
      var dataArr = thisOption.data || [];
      var axisType = thisOption.axisType;
      var names = this._names = [];
      var processedDataArr;
      if (axisType === "category") {
        processedDataArr = [];
        each(dataArr, function(item, index) {
          var value = convertOptionIdName(getDataItemValue(item), "");
          var newItem;
          if (isObject(item)) {
            newItem = clone(item);
            newItem.value = index;
          } else {
            newItem = index;
          }
          processedDataArr.push(newItem);
          names.push(value);
        });
      } else {
        processedDataArr = dataArr;
      }
      var dimType = {
        category: "ordinal",
        time: "time",
        value: "number"
      }[axisType] || "number";
      var data = this._data = new SeriesData([{
        name: "value",
        type: dimType
      }], this);
      data.initData(processedDataArr, names);
    };
    TimelineModel2.prototype.getData = function() {
      return this._data;
    };
    TimelineModel2.prototype.getCategories = function() {
      if (this.get("axisType") === "category") {
        return this._names.slice();
      }
    };
    TimelineModel2.type = "timeline";
    TimelineModel2.defaultOption = {
      // zlevel: 0,                  // 一级层叠
      z: 4,
      show: true,
      axisType: "time",
      realtime: true,
      left: "20%",
      top: null,
      right: "20%",
      bottom: 0,
      width: null,
      height: 40,
      padding: tokens.size.m,
      controlPosition: "left",
      autoPlay: false,
      rewind: false,
      loop: true,
      playInterval: 2e3,
      currentIndex: 0,
      itemStyle: {},
      label: {
        color: tokens.color.secondary
      },
      data: []
    };
    return TimelineModel2;
  }(ComponentModel)
);
export {
  TimelineModel as default
};

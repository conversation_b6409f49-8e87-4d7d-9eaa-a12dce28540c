"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
var screenEnum = /* @__PURE__ */ ((screenEnum2) => {
  screenEnum2[screenEnum2["XS"] = 480] = "XS";
  screenEnum2[screenEnum2["SM"] = 576] = "SM";
  screenEnum2[screenEnum2["MD"] = 768] = "MD";
  screenEnum2[screenEnum2["LG"] = 992] = "LG";
  screenEnum2[screenEnum2["IPC"] = 1024] = "IPC";
  screenEnum2[screenEnum2["XL"] = 1200] = "XL";
  screenEnum2[screenEnum2["XXL"] = 1600] = "XXL";
  return screenEnum2;
})(screenEnum || {});
const screenMap = /* @__PURE__ */ new Map();
screenMap.set(
  "XS",
  480
  /* XS */
);
screenMap.set(
  "SM",
  576
  /* SM */
);
screenMap.set(
  "MD",
  768
  /* MD */
);
screenMap.set(
  "LG",
  992
  /* LG */
);
screenMap.set(
  "IPC",
  1024
  /* IPC */
);
screenMap.set(
  "XL",
  1200
  /* XL */
);
screenMap.set(
  "XXL",
  1600
  /* XXL */
);
exports.screenEnum = screenEnum;
exports.screenMap = screenMap;

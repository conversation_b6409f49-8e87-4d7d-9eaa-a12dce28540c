import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each, assert, isObject, filter, extend, merge } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { mappingToExists } from "../../util/model.js";
import ComponentModel from "../../model/Component.js";
import { mergeLayoutParam, copyLayoutParams } from "../../util/layout.js";
function setKeyInfoToNewElOption(resultItem, newElOption) {
  var existElOption = resultItem.existing;
  newElOption.id = resultItem.keyInfo.id;
  !newElOption.type && existElOption && (newElOption.type = existElOption.type);
  if (newElOption.parentId == null) {
    var newElParentOption = newElOption.parentOption;
    if (newElParentOption) {
      newElOption.parentId = newElParentOption.id;
    } else if (existElOption) {
      newElOption.parentId = existElOption.parentId;
    }
  }
  newElOption.parentOption = null;
}
function isSetLoc(obj, props) {
  var isSet;
  each(props, function(prop) {
    obj[prop] != null && obj[prop] !== "auto" && (isSet = true);
  });
  return isSet;
}
function mergeNewElOptionToExist(existList, index, newElOption) {
  var newElOptCopy = extend({}, newElOption);
  var existElOption = existList[index];
  var $action = newElOption.$action || "merge";
  if ($action === "merge") {
    if (existElOption) {
      if (process.env.NODE_ENV !== "production") {
        var newType = newElOption.type;
        assert(!newType || existElOption.type === newType, 'Please set $action: "replace" to change `type`');
      }
      merge(existElOption, newElOptCopy, true);
      mergeLayoutParam(existElOption, newElOptCopy, {
        ignoreSize: true
      });
      copyLayoutParams(newElOption, existElOption);
      copyTransitionInfo(newElOption, existElOption);
      copyTransitionInfo(newElOption, existElOption, "shape");
      copyTransitionInfo(newElOption, existElOption, "style");
      copyTransitionInfo(newElOption, existElOption, "extra");
      newElOption.clipPath = existElOption.clipPath;
    } else {
      existList[index] = newElOptCopy;
    }
  } else if ($action === "replace") {
    existList[index] = newElOptCopy;
  } else if ($action === "remove") {
    existElOption && (existList[index] = null);
  }
}
var TRANSITION_PROPS_TO_COPY = ["transition", "enterFrom", "leaveTo"];
var ROOT_TRANSITION_PROPS_TO_COPY = TRANSITION_PROPS_TO_COPY.concat(["enterAnimation", "updateAnimation", "leaveAnimation"]);
function copyTransitionInfo(target, source, targetProp) {
  if (targetProp) {
    if (!target[targetProp] && source[targetProp]) {
      target[targetProp] = {};
    }
    target = target[targetProp];
    source = source[targetProp];
  }
  if (!target || !source) {
    return;
  }
  var props = targetProp ? TRANSITION_PROPS_TO_COPY : ROOT_TRANSITION_PROPS_TO_COPY;
  for (var i = 0; i < props.length; i++) {
    var prop = props[i];
    if (target[prop] == null && source[prop] != null) {
      target[prop] = source[prop];
    }
  }
}
function setLayoutInfoToExist(existItem, newElOption) {
  if (!existItem) {
    return;
  }
  existItem.hv = newElOption.hv = [
    // Rigid body, don't care about `width`.
    isSetLoc(newElOption, ["left", "right"]),
    // Rigid body, don't care about `height`.
    isSetLoc(newElOption, ["top", "bottom"])
  ];
  if (existItem.type === "group") {
    var existingGroupOpt = existItem;
    var newGroupOpt = newElOption;
    existingGroupOpt.width == null && (existingGroupOpt.width = newGroupOpt.width = 0);
    existingGroupOpt.height == null && (existingGroupOpt.height = newGroupOpt.height = 0);
  }
}
var GraphicComponentModel = (
  /** @class */
  function(_super) {
    __extends(GraphicComponentModel2, _super);
    function GraphicComponentModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = GraphicComponentModel2.type;
      _this.preventAutoZ = true;
      return _this;
    }
    GraphicComponentModel2.prototype.mergeOption = function(option, ecModel) {
      var elements = this.option.elements;
      this.option.elements = null;
      _super.prototype.mergeOption.call(this, option, ecModel);
      this.option.elements = elements;
    };
    GraphicComponentModel2.prototype.optionUpdated = function(newOption, isInit) {
      var thisOption = this.option;
      var newList = (isInit ? thisOption : newOption).elements;
      var existList = thisOption.elements = isInit ? [] : thisOption.elements;
      var flattenedList = [];
      this._flatten(newList, flattenedList, null);
      var mappingResult = mappingToExists(existList, flattenedList, "normalMerge");
      var elOptionsToUpdate = this._elOptionsToUpdate = [];
      each(mappingResult, function(resultItem, index) {
        var newElOption = resultItem.newOption;
        if (process.env.NODE_ENV !== "production") {
          assert(isObject(newElOption) || resultItem.existing, "Empty graphic option definition");
        }
        if (!newElOption) {
          return;
        }
        elOptionsToUpdate.push(newElOption);
        setKeyInfoToNewElOption(resultItem, newElOption);
        mergeNewElOptionToExist(existList, index, newElOption);
        setLayoutInfoToExist(existList[index], newElOption);
      }, this);
      thisOption.elements = filter(existList, function(item) {
        item && delete item.$action;
        return item != null;
      });
    };
    GraphicComponentModel2.prototype._flatten = function(optionList, result, parentOption) {
      each(optionList, function(option) {
        if (!option) {
          return;
        }
        if (parentOption) {
          option.parentOption = parentOption;
        }
        result.push(option);
        var children = option.children;
        if (children && children.length) {
          this._flatten(children, result, option);
        }
        delete option.children;
      }, this);
    };
    GraphicComponentModel2.prototype.useElOptionsToUpdate = function() {
      var els = this._elOptionsToUpdate;
      this._elOptionsToUpdate = null;
      return els;
    };
    GraphicComponentModel2.type = "graphic";
    GraphicComponentModel2.defaultOption = {
      elements: []
      // parentId: null
    };
    return GraphicComponentModel2;
  }(ComponentModel)
);
export {
  GraphicComponentModel,
  setKeyInfoToNewElOption
};

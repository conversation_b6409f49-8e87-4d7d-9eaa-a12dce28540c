import { defineComponent, ref, watch, createElement<PERSON><PERSON>, openBlock, createElementVNode, nextTick } from "vue";
import { useECharts } from "../../../../utils/hooks/useECharts.js";
import { useResizeObserver } from "../../../../node_modules/.pnpm/@vueuse_core@13.7.0_vue@3.5.20_typescript@4.9.5_/node_modules/@vueuse/core/index.js";
import { optionFormate } from "./data.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "PieEcharts",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    // 图例num宽度
    legendNumWidth: {
      type: Number,
      default: 0
    },
    // 饼图标题
    titleText: {
      type: String,
      default: ""
    },
    // 饼图类型
    pieType: {
      type: String,
      // vertical || horizontal
      default: "horizontal"
    },
    // 是否展示图例中的总价标签
    tagFlag: {
      type: Boolean,
      // true || false
      default: false
    },
    // 价格列表
    priceList: {
      type: Array,
      default: () => []
    },
    // 颜色模式
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const chartRef = ref();
    const wrapperRef = ref(null);
    let chartWidth = ref(0);
    let chartHeight = ref(0);
    useResizeObserver(wrapperRef, (entries) => {
      const entry = entries[0];
      const { width, height } = entry.contentRect;
      chartWidth.value = width;
      chartHeight.value = height;
    });
    const props = __props;
    const { setOptions } = useECharts(chartRef);
    async function handleSetVisitChart() {
      if (props.dataList instanceof Array && props.dataList.length) {
        await nextTick();
        setOptions(optionFormate(props, chartHeight, chartWidth));
      }
    }
    watch(
      () => props.dataList,
      (val) => {
        if (val instanceof Array && val.length) {
          handleSetVisitChart();
        }
      },
      {
        immediate: true,
        deep: true
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        ref_key: "wrapperRef",
        ref: wrapperRef,
        style: { "height": "100%", "width": "100%", "flex": "1" }
      }, [
        createElementVNode("div", {
          ref_key: "chartRef",
          ref: chartRef,
          style: { "height": "100%", "width": "100%" }
        }, null, 512)
      ], 512);
    };
  }
});
export {
  _sfc_main as default
};

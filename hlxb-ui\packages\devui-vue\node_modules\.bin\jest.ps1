#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="F:\work\code\test\zujian\hlxb-ui-twelve\hlxb-ui\node_modules\.pnpm\jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898\node_modules\jest\bin\node_modules;F:\work\code\test\zujian\hlxb-ui-twelve\hlxb-ui\node_modules\.pnpm\jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898\node_modules\jest\node_modules;F:\work\code\test\zujian\hlxb-ui-twelve\hlxb-ui\node_modules\.pnpm\jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898\node_modules;F:\work\code\test\zujian\hlxb-ui-twelve\hlxb-ui\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898/node_modules/jest/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898/node_modules/jest/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/jest@27.5.1_ts-node@10.9.2__30a3d53c922031d088c5b106a095f898/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../jest/bin/jest.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../jest/bin/jest.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../jest/bin/jest.js" $args
  } else {
    & "node$exe"  "$basedir/../jest/bin/jest.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret

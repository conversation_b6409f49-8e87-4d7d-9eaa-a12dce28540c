import { isArray, map } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { parsePercent } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/contain/text.js";
function getSectorCornerRadius(model, shape, zeroIfNull) {
  var cornerRadius = model.get("borderRadius");
  if (cornerRadius == null) {
    return zeroIfNull ? {
      cornerRadius: 0
    } : null;
  }
  if (!isArray(cornerRadius)) {
    cornerRadius = [cornerRadius, cornerRadius, cornerRadius, cornerRadius];
  }
  var dr = Math.abs(shape.r || 0 - shape.r0 || 0);
  return {
    cornerRadius: map(cornerRadius, function(cr) {
      return parsePercent(cr, dr);
    })
  };
}
export {
  getSectorCornerRadius
};

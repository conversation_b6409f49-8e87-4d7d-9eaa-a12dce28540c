import { visualMapActionInfo, visualMapActionHander } from "./visualMapAction.js";
import { visualMapEncodingHandlers } from "./visualEncoding.js";
import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import visualMapPreprocessor from "./preprocessor.js";
var installed = false;
function installCommon(registers) {
  if (installed) {
    return;
  }
  installed = true;
  registers.registerSubTypeDefaulter("visualMap", function(option) {
    return !option.categories && (!(option.pieces ? option.pieces.length > 0 : option.splitNumber > 0) || option.calculable) ? "continuous" : "piecewise";
  });
  registers.registerAction(visualMapActionInfo, visualMapActionHander);
  each(visualMapEncodingHandlers, function(handler) {
    registers.registerVisual(registers.PRIORITY.VISUAL.COMPONENT, handler);
  });
  registers.registerPreprocessor(visualMapPreprocessor);
}
export {
  installCommon as default
};

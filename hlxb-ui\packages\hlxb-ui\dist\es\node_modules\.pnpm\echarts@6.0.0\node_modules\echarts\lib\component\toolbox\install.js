import { use } from "../../extension.js";
import { install as install$1 } from "../dataZoom/installDataZoomSelect.js";
import ToolboxModel from "./ToolboxModel.js";
import ToolboxView from "./ToolboxView.js";
import { registerFeature } from "./featureManager.js";
import SaveAsImage from "./feature/SaveAsImage.js";
import MagicType from "./feature/MagicType.js";
import DataView from "./feature/DataView.js";
import RestoreOption from "./feature/Restore.js";
import DataZoomFeature from "./feature/DataZoom.js";
function install(registers) {
  registers.registerComponentModel(ToolboxModel);
  registers.registerComponentView(ToolboxView);
  registerFeature("saveAsImage", SaveAsImage);
  registerFeature("magicType", MagicType);
  registerFeature("dataView", DataView);
  registerFeature("dataZoom", DataZoomFeature);
  registerFeature("restore", RestoreOption);
  use(install$1);
}
export {
  install
};

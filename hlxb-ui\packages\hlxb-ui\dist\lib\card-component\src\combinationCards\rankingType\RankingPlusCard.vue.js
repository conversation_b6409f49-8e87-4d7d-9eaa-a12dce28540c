"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index$2 = require("../../../../card/index.js");
const Ranking_vue_vue_type_script_setup_true_lang = require("../../basicComponents/Ranking.vue.js");
;/* empty css                                  */
const Empty_vue_vue_type_script_setup_true_name_HEmpty_lang = require("../../basicComponents/Empty.vue.js");
;/* empty css                                */
const Loading_vue_vue_type_script_setup_true_name_CardLoading_lang = require("../../basicComponents/Loading.vue.js");
;/* empty css                                  */
const index = require("../../hooks/index.js");
const index$1 = require("../../../../config/index.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbRankingPlusCard",
    inheritAttrs: false
  },
  __name: "RankingPlusCard",
  props: {
    // 底部排名数据列表，类型为 pieDataListType 数组，默认值为空数组
    bottomList: {
      type: Array,
      default: () => []
    },
    // 数据是否为空 默认值为 false
    empty: {
      type: Boolean,
      default: false
    },
    // 点击标志位，控制排名项点击行为，类型为布尔值，默认值为 false
    clickFlags: {
      type: Boolean,
      default: false
    },
    // 是否显示加载状态组件，类型为布尔值，默认值为 false
    loading: {
      type: Boolean,
      default: false
    },
    // 颜色模式
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  emits: ["ranking"],
  setup(__props, { emit: __emit }) {
    const prefixCls = index$1.getPrefixCls("card-combination-ranking-plus");
    const emit = __emit;
    const setItem = (item) => {
      emit("ranking", item);
    };
    const { filtersSlots } = index.useFilterSlots();
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        vue.createVNode(vue.unref(index$2.HlxbCard), vue.mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), vue.createSlots({
          defaultBody: vue.withCtx(() => [
            __props.loading ? (vue.openBlock(), vue.createBlock(Loading_vue_vue_type_script_setup_true_name_CardLoading_lang.default, {
              key: 0,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"])) : !__props.empty ? (vue.openBlock(), vue.createBlock(Ranking_vue_vue_type_script_setup_true_lang.default, vue.mergeProps({
              key: 1,
              onSetItem: setItem,
              themeColor: __props.themeColor
            }, _ctx.$attrs, {
              dataList: __props.bottomList,
              clickFlags: __props.clickFlags
            }), null, 16, ["themeColor", "dataList", "clickFlags"])) : (vue.openBlock(), vue.createBlock(Empty_vue_vue_type_script_setup_true_name_HEmpty_lang.default, {
              key: 2,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"]))
          ]),
          _: 2
        }, [
          vue.renderList(vue.unref(filtersSlots), (item) => {
            return {
              name: item,
              fn: vue.withCtx(() => [
                vue.renderSlot(_ctx.$slots, item)
              ])
            };
          })
        ]), 1040, ["themeColor"])
      ], 2);
    };
  }
});
exports.default = _sfc_main;

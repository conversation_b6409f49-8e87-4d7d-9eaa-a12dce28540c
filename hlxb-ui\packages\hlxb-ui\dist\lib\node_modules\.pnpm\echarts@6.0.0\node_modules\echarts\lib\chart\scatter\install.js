"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const extension = require("../../extension.js");
const ScatterSeries = require("./ScatterSeries.js");
const ScatterView = require("./ScatterView.js");
const installSimple = require("../../component/grid/installSimple.js");
const points = require("../../layout/points.js");
function install(registers) {
  extension.use(installSimple.install);
  registers.registerSeriesModel(ScatterSeries.default);
  registers.registerChartView(ScatterView.default);
  registers.registerLayout(points.default("scatter"));
}
exports.install = install;

import checkMarkerInSeries from "./checkMarkerInSeries.js";
import MarkPointModel from "./MarkPointModel.js";
import MarkPointView from "./MarkPointView.js";
function install(registers) {
  registers.registerComponentModel(MarkPointModel);
  registers.registerComponentView(MarkPointView);
  registers.registerPreprocessor(function(opt) {
    if (checkMarkerInSeries(opt.series, "markPoint")) {
      opt.markPoint = opt.markPoint || {};
    }
  });
}
export {
  install
};

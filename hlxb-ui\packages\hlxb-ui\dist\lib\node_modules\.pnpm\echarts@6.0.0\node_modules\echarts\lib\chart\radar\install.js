"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const extension = require("../../extension.js");
const radarLayout = require("./radarLayout.js");
const dataFilter = require("../../processor/dataFilter.js");
const backwardCompat = require("./backwardCompat.js");
const RadarView = require("./RadarView.js");
const RadarSeries = require("./RadarSeries.js");
const install$1 = require("../../component/radar/install.js");
function install(registers) {
  extension.use(install$1.install);
  registers.registerChartView(RadarView.default);
  registers.registerSeriesModel(RadarSeries.default);
  registers.registerLayout(radarLayout.default);
  registers.registerProcessor(dataFilter.default("radar"));
  registers.registerPreprocessor(backwardCompat.default);
}
exports.install = install;

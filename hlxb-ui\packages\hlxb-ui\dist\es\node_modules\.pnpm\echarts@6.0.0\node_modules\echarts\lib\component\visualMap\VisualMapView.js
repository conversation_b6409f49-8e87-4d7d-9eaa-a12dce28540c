import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import "../../util/graphic.js";
import { normalizeCssArray } from "../../util/format.js";
import { createBoxLayoutReference, positionElement } from "../../util/layout.js";
import VisualMapping from "../../visual/VisualMapping.js";
import ComponentView from "../../view/Component.js";
import Rect from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js";
var VisualMapView = (
  /** @class */
  function(_super) {
    __extends(VisualMapView2, _super);
    function VisualMapView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = VisualMapView2.type;
      _this.autoPositionValues = {
        left: 1,
        right: 1,
        top: 1,
        bottom: 1
      };
      return _this;
    }
    VisualMapView2.prototype.init = function(ecModel, api) {
      this.ecModel = ecModel;
      this.api = api;
    };
    VisualMapView2.prototype.render = function(visualMapModel, ecModel, api, payload) {
      this.visualMapModel = visualMapModel;
      if (visualMapModel.get("show") === false) {
        this.group.removeAll();
        return;
      }
      this.doRender(visualMapModel, ecModel, api, payload);
    };
    VisualMapView2.prototype.renderBackground = function(group) {
      var visualMapModel = this.visualMapModel;
      var padding = normalizeCssArray(visualMapModel.get("padding") || 0);
      var rect = group.getBoundingRect();
      group.add(new Rect({
        z2: -1,
        silent: true,
        shape: {
          x: rect.x - padding[3],
          y: rect.y - padding[0],
          width: rect.width + padding[3] + padding[1],
          height: rect.height + padding[0] + padding[2]
        },
        style: {
          fill: visualMapModel.get("backgroundColor"),
          stroke: visualMapModel.get("borderColor"),
          lineWidth: visualMapModel.get("borderWidth")
        }
      }));
    };
    VisualMapView2.prototype.getControllerVisual = function(targetValue, visualCluster, opts) {
      opts = opts || {};
      var forceState = opts.forceState;
      var visualMapModel = this.visualMapModel;
      var visualObj = {};
      if (visualCluster === "color") {
        var defaultColor = visualMapModel.get("contentColor");
        visualObj.color = defaultColor;
      }
      function getter(key) {
        return visualObj[key];
      }
      function setter(key, value) {
        visualObj[key] = value;
      }
      var mappings = visualMapModel.controllerVisuals[forceState || visualMapModel.getValueState(targetValue)];
      var visualTypes = VisualMapping.prepareVisualTypes(mappings);
      each(visualTypes, function(type) {
        var visualMapping = mappings[type];
        if (opts.convertOpacityToAlpha && type === "opacity") {
          type = "colorAlpha";
          visualMapping = mappings.__alphaForOpacity;
        }
        if (VisualMapping.dependsOn(type, visualCluster)) {
          visualMapping && visualMapping.applyVisual(targetValue, getter, setter);
        }
      });
      return visualObj[visualCluster];
    };
    VisualMapView2.prototype.positionGroup = function(group) {
      var model = this.visualMapModel;
      var api = this.api;
      var refContainer = createBoxLayoutReference(model, api).refContainer;
      positionElement(group, model.getBoxLayoutParams(), refContainer);
    };
    VisualMapView2.prototype.doRender = function(visualMapModel, ecModel, api, payload) {
    };
    VisualMapView2.type = "visualMap";
    return VisualMapView2;
  }(ComponentView)
);
export {
  VisualMapView as default
};

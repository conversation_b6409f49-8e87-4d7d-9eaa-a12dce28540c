import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { merge, each, filter } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import ComponentModel from "../../model/Component.js";
var ParallelModel = (
  /** @class */
  function(_super) {
    __extends(ParallelModel2, _super);
    function ParallelModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ParallelModel2.type;
      return _this;
    }
    ParallelModel2.prototype.init = function() {
      _super.prototype.init.apply(this, arguments);
      this.mergeOption({});
    };
    ParallelModel2.prototype.mergeOption = function(newOption) {
      var thisOption = this.option;
      newOption && merge(thisOption, newOption, true);
      this._initDimensions();
    };
    ParallelModel2.prototype.contains = function(model, ecModel) {
      var parallelIndex = model.get("parallelIndex");
      return parallelIndex != null && ecModel.getComponent("parallel", parallelIndex) === this;
    };
    ParallelModel2.prototype.setAxisExpand = function(opt) {
      each(["axisExpandable", "axisExpandCenter", "axisExpandCount", "axisExpandWidth", "axisExpandWindow"], function(name) {
        if (opt.hasOwnProperty(name)) {
          this.option[name] = opt[name];
        }
      }, this);
    };
    ParallelModel2.prototype._initDimensions = function() {
      var dimensions = this.dimensions = [];
      var parallelAxisIndex = this.parallelAxisIndex = [];
      var axisModels = filter(this.ecModel.queryComponents({
        mainType: "parallelAxis"
      }), function(axisModel) {
        return (axisModel.get("parallelIndex") || 0) === this.componentIndex;
      }, this);
      each(axisModels, function(axisModel) {
        dimensions.push("dim" + axisModel.get("dim"));
        parallelAxisIndex.push(axisModel.componentIndex);
      });
    };
    ParallelModel2.type = "parallel";
    ParallelModel2.dependencies = ["parallelAxis"];
    ParallelModel2.layoutMode = "box";
    ParallelModel2.defaultOption = {
      // zlevel: 0,
      z: 0,
      left: 80,
      top: 60,
      right: 80,
      bottom: 60,
      // width: {totalWidth} - left - right,
      // height: {totalHeight} - top - bottom,
      layout: "horizontal",
      // FIXME
      // naming?
      axisExpandable: false,
      axisExpandCenter: null,
      axisExpandCount: 0,
      axisExpandWidth: 50,
      axisExpandRate: 17,
      axisExpandDebounce: 50,
      // [out, in, jumpTarget]. In percentage. If use [null, 0.05], null means full.
      // Do not doc to user until necessary.
      axisExpandSlideTriggerArea: [-0.15, 0.05, 0.4],
      axisExpandTriggerOn: "click",
      parallelAxisDefault: null
    };
    return ParallelModel2;
  }(ComponentModel)
);
export {
  ParallelModel as default
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { mixin } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import ComponentModel from "../../model/Component.js";
import { AxisModelCommonMixin } from "../axisModelCommonMixin.js";
import { SINGLE_REFERRING } from "../../util/model.js";
var CartesianAxisModel = (
  /** @class */
  function(_super) {
    __extends(CartesianAxisModel2, _super);
    function CartesianAxisModel2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    CartesianAxisModel2.prototype.getCoordSysModel = function() {
      return this.getReferringComponents("grid", SINGLE_REFERRING).models[0];
    };
    CartesianAxisModel2.type = "cartesian2dAxis";
    return CartesianAxisModel2;
  }(ComponentModel)
);
mixin(CartesianAxisModel, AxisModelCommonMixin);
export {
  CartesianAxisModel,
  CartesianAxisModel as default
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const prefixCls = require("../hooks/prefixCls.js");
const DoubleDownIcon_vue_vue_type_script_setup_true_lang = require("./DoubleDownIcon.vue2.js");
const useCalendarContext = require("../hooks/useCalendarContext.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ExpandButton"
  },
  __name: "ExpandButton",
  emits: ["toggle-expand"],
  setup(__props, { emit: __emit }) {
    const { calendarContext } = useCalendarContext.useCalendarContext();
    const emit = __emit;
    const handleClick = () => {
      emit("toggle-expand");
    };
    return (_ctx, _cache) => {
      return vue.unref(calendarContext).options.showExpandButton ? (vue.openBlock(), vue.createElementBlock("div", {
        key: 0,
        onClick: handleClick,
        class: vue.normalizeClass(vue.unref(prefixCls.expandBtnPrefixCls))
      }, [
        vue.renderSlot(_ctx.$slots, "expand-button", {
          isExpanded: vue.unref(calendarContext).isExpanded
        }, () => [
          vue.createTextVNode(vue.toDisplayString(vue.unref(calendarContext).isExpanded ? "收起" : "展开") + " ", 1),
          vue.createVNode(DoubleDownIcon_vue_vue_type_script_setup_true_lang.default, {
            isExpanded: vue.unref(calendarContext).isExpanded
          }, null, 8, ["isExpanded"])
        ])
      ], 2)) : vue.createCommentVNode("", true);
    };
  }
});
exports.default = _sfc_main;

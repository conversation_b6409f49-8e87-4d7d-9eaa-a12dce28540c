import { each as each$1 } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { makeInner } from "../../util/model.js";
var each = each$1;
var inner = makeInner();
function push(ecModel, newSnapshot) {
  var storedSnapshots = getStoreSnapshots(ecModel);
  each(newSnapshot, function(batchItem, dataZoomId) {
    var i = storedSnapshots.length - 1;
    for (; i >= 0; i--) {
      var snapshot = storedSnapshots[i];
      if (snapshot[dataZoomId]) {
        break;
      }
    }
    if (i < 0) {
      var dataZoomModel = ecModel.queryComponents({
        mainType: "dataZoom",
        subType: "select",
        id: dataZoomId
      })[0];
      if (dataZoomModel) {
        var percentRange = dataZoomModel.getPercentRange();
        storedSnapshots[0][dataZoomId] = {
          dataZoomId,
          start: percentRange[0],
          end: percentRange[1]
        };
      }
    }
  });
  storedSnapshots.push(newSnapshot);
}
function pop(ecModel) {
  var storedSnapshots = getStoreSnapshots(ecModel);
  var head = storedSnapshots[storedSnapshots.length - 1];
  storedSnapshots.length > 1 && storedSnapshots.pop();
  var snapshot = {};
  each(head, function(batchItem, dataZoomId) {
    for (var i = storedSnapshots.length - 1; i >= 0; i--) {
      batchItem = storedSnapshots[i][dataZoomId];
      if (batchItem) {
        snapshot[dataZoomId] = batchItem;
        break;
      }
    }
  });
  return snapshot;
}
function clear(ecModel) {
  inner(ecModel).snapshots = null;
}
function count(ecModel) {
  return getStoreSnapshots(ecModel).length;
}
function getStoreSnapshots(ecModel) {
  var store = inner(ecModel);
  if (!store.snapshots) {
    store.snapshots = [{}];
  }
  return store.snapshots;
}
export {
  clear,
  count,
  pop,
  push
};

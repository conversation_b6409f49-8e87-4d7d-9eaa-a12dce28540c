"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../config/index.js");
const _hoisted_1 = { class: "r-checkbox--outer" };
const _hoisted_2 = ["value"];
const _hoisted_3 = { class: "r-checkbox__label" };
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCheckbox"
  },
  __name: "checkbox",
  props: {
    label: String,
    modelValue: [<PERSON><PERSON><PERSON>, Array]
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const checkboxGroup = vue.inject("CheckboxGroup", null);
    const prefixCls = index.getPrefixCls("checkbox");
    const isGroup = vue.computed(() => checkboxGroup !== null);
    const CKValue = vue.computed({
      get() {
        return isGroup.value ? checkboxGroup.modelValue : props.modelValue;
      },
      set(val) {
        if (isGroup.value) {
          checkboxGroup.$emit("update:modelValue", val);
        } else {
          emits("update:modelValue", val);
        }
      }
    });
    const isChecked = vue.computed(() => {
      var _a;
      if (isGroup.value) {
        return checkboxGroup.modelValue.includes(props.label);
      } else {
        if (typeof props.modelValue === "boolean") {
          return props.modelValue;
        }
        return (_a = props.modelValue) == null ? void 0 : _a.includes(props.label);
      }
    });
    const styleClass = vue.computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("label", {
        class: vue.normalizeClass(["r-checkbox", styleClass.value])
      }, [
        vue.createElementVNode("span", _hoisted_1, [
          vue.createElementVNode("span", {
            class: vue.normalizeClass(["r-checkbox--inner", { "is-checked": isChecked.value }])
          }, null, 2),
          vue.withDirectives(vue.createElementVNode("input", {
            type: "checkbox",
            class: "r-checkbox-native",
            value: __props.label,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => CKValue.value = $event)
          }, null, 8, _hoisted_2), [
            [vue.vModelCheckbox, CKValue.value]
          ])
        ]),
        vue.createElementVNode("span", _hoisted_3, vue.toDisplayString(__props.label), 1)
      ], 2);
    };
  }
});
exports.default = _sfc_main;

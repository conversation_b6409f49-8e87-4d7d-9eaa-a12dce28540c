import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import DataZoomView from "./DataZoomView.js";
var SelectDataZoomView = (
  /** @class */
  function(_super) {
    __extends(SelectDataZoomView2, _super);
    function SelectDataZoomView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = SelectDataZoomView2.type;
      return _this;
    }
    SelectDataZoomView2.type = "dataZoom.select";
    return SelectDataZoomView2;
  }(DataZoomView)
);
export {
  SelectDataZoomView as default
};

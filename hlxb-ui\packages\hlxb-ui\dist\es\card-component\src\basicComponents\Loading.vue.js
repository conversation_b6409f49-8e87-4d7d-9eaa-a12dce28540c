import { defineComponent, createElementBlock, openBlock, normalizeClass, unref, createVNode } from "vue";
import { getPrefixCls } from "../../../config/index.js";
import Loading3QuartersOutlined from "../../../node_modules/.pnpm/@ant-design_icons-vue@6.1.0_vue@3.5.20_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/icons/Loading3QuartersOutlined.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCardLoading"
  },
  __name: "Loading",
  props: {
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("card-loading");
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.themeColor])
      }, [
        createVNode(unref(Loading3QuartersOutlined), {
          style: { "font-size": "24px", "color": "var(--theme-color)", "margin": "0 auto", "display": "block" },
          spin: ""
        })
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

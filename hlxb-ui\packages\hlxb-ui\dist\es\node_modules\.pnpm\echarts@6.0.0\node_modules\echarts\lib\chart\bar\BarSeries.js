import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import BaseBarSeriesModel from "./BaseBarSeries.js";
import createSeriesData from "../helper/createSeriesData.js";
import { inheritDefaultOption } from "../../util/component.js";
import tokens from "../../visual/tokens.js";
var BarSeriesModel = (
  /** @class */
  function(_super) {
    __extends(BarSeriesModel2, _super);
    function BarSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = BarSeriesModel2.type;
      return _this;
    }
    BarSeriesModel2.prototype.getInitialData = function() {
      return createSeriesData(null, this, {
        useEncodeDefaulter: true,
        createInvertedIndices: !!this.get("realtimeSort", true) || null
      });
    };
    BarSeriesModel2.prototype.getProgressive = function() {
      return this.get("large") ? this.get("progressive") : false;
    };
    BarSeriesModel2.prototype.getProgressiveThreshold = function() {
      var progressiveThreshold = this.get("progressiveThreshold");
      var largeThreshold = this.get("largeThreshold");
      if (largeThreshold > progressiveThreshold) {
        progressiveThreshold = largeThreshold;
      }
      return progressiveThreshold;
    };
    BarSeriesModel2.prototype.brushSelector = function(dataIndex, data, selectors) {
      return selectors.rect(data.getItemLayout(dataIndex));
    };
    BarSeriesModel2.type = "series.bar";
    BarSeriesModel2.dependencies = ["grid", "polar"];
    BarSeriesModel2.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {
      // If clipped
      // Only available on cartesian2d
      clip: true,
      roundCap: false,
      showBackground: false,
      backgroundStyle: {
        color: "rgba(180, 180, 180, 0.2)",
        borderColor: null,
        borderWidth: 0,
        borderType: "solid",
        borderRadius: 0,
        shadowBlur: 0,
        shadowColor: null,
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        opacity: 1
      },
      select: {
        itemStyle: {
          borderColor: tokens.color.primary,
          borderWidth: 2
        }
      },
      realtimeSort: false
    });
    return BarSeriesModel2;
  }(BaseBarSeriesModel)
);
export {
  BarSeriesModel as default
};

import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function mapSymbolLayout(ecModel) {
  var processedMapType = {};
  ecModel.eachSeriesByType("map", function(mapSeries) {
    var mapType = mapSeries.getMapType();
    if (mapSeries.getHostGeoModel() || processedMapType[mapType]) {
      return;
    }
    var mapSymbolOffsets = {};
    each(mapSeries.seriesGroup, function(subMapSeries) {
      var geo = subMapSeries.coordinateSystem;
      var data2 = subMapSeries.originalData;
      if (subMapSeries.get("showLegendSymbol") && ecModel.getComponent("legend")) {
        data2.each(data2.mapDimension("value"), function(value, idx) {
          var name = data2.getName(idx);
          var region = geo.getRegion(name);
          if (!region || isNaN(value)) {
            return;
          }
          var offset = mapSymbolOffsets[name] || 0;
          var point = geo.dataToPoint(region.getCenter());
          mapSymbolOffsets[name] = offset + 1;
          data2.setItemLayout(idx, {
            point,
            offset
          });
        });
      }
    });
    var data = mapSeries.getData();
    data.each(function(idx) {
      var name = data.getName(idx);
      var layout = data.getItemLayout(idx) || {};
      layout.showLabel = !mapSymbolOffsets[name];
      data.setItemLayout(idx, layout);
    });
    processedMapType[mapType] = true;
  });
}
export {
  mapSymbolLayout as default
};

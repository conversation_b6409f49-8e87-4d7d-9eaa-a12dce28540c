"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const prefixCls = require("../hooks/prefixCls.js");
const useCalendarContext = require("../hooks/useCalendarContext.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "DateCell"
  },
  __name: "DateCell",
  props: {
    dateInfo: {}
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const { calendarContext, options } = useCalendarContext.useCalendarContext();
    const emit = __emit;
    const handleClick = () => {
      emit("click", props.dateInfo.date, props.dateInfo);
    };
    const customClass = vue.computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.cellClass) {
        return options.value.cellClass(props.dateInfo);
      }
      return "";
    });
    const cellContent = vue.computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.cellContent) {
        return options.value.cellContent(props.dateInfo);
      }
      return props.dateInfo.date.date().toString();
    });
    const showIndicator = vue.computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.hasIndicator) {
        return options.value.hasIndicator(props.dateInfo.dateString);
      }
      return false;
    });
    const indicatorPositionClass = vue.computed(() => {
      var _a, _b;
      const position = ((_b = (_a = options.value) == null ? void 0 : _a.indicatorConfig) == null ? void 0 : _b.position) || "bottom-center";
      return `indicator-${position}`;
    });
    const indicatorStyle = vue.computed(() => {
      var _a;
      const config = ((_a = options.value) == null ? void 0 : _a.indicatorConfig) || {};
      const defaultStyle = {
        backgroundColor: config.color || "#fc7c22",
        width: `${config.size || 4}px`,
        height: `${config.size || 4}px`
      };
      return {
        ...defaultStyle,
        ...config.style
      };
    });
    return (_ctx, _cache) => {
      var _a;
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([
          vue.unref(prefixCls.dateCellPrefixCls),
          {
            "current-month": _ctx.dateInfo.isCurrentMonth,
            "other-month": !_ctx.dateInfo.isCurrentMonth,
            selected: _ctx.dateInfo.date.isSame(vue.unref(calendarContext).selectedDate, "day"),
            today: ((_a = vue.unref(options)) == null ? void 0 : _a.showToday) && _ctx.dateInfo.isToday
          },
          customClass.value
        ]),
        onClick: handleClick
      }, [
        vue.renderSlot(_ctx.$slots, "date-cell", { dateItem: _ctx.dateInfo }, () => [
          vue.createTextVNode(vue.toDisplayString(cellContent.value), 1)
        ]),
        showIndicator.value ? (vue.openBlock(), vue.createElementBlock("div", {
          key: 0,
          class: vue.normalizeClass(["calendar-indicator", indicatorPositionClass.value]),
          style: vue.normalizeStyle(indicatorStyle.value)
        }, null, 6)) : vue.createCommentVNode("", true)
      ], 2);
    };
  }
});
exports.default = _sfc_main;

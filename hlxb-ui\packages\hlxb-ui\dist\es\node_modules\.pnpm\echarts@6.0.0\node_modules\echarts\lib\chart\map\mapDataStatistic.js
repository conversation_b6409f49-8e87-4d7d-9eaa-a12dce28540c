import { each, map } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function dataStatistics(datas, statisticType) {
  var dataNameMap = {};
  each(datas, function(data) {
    data.each(data.mapDimension("value"), function(value, idx) {
      var mapKey = "ec-" + data.getName(idx);
      dataNameMap[mapKey] = dataNameMap[mapKey] || [];
      if (!isNaN(value)) {
        dataNameMap[mapKey].push(value);
      }
    });
  });
  return datas[0].map(datas[0].mapDimension("value"), function(value, idx) {
    var mapKey = "ec-" + datas[0].getName(idx);
    var sum = 0;
    var min = Infinity;
    var max = -Infinity;
    var len = dataNameMap[mapKey].length;
    for (var i = 0; i < len; i++) {
      min = Math.min(min, dataNameMap[mapKey][i]);
      max = Math.max(max, dataNameMap[mapKey][i]);
      sum += dataNameMap[mapKey][i];
    }
    var result;
    if (statisticType === "min") {
      result = min;
    } else if (statisticType === "max") {
      result = max;
    } else if (statisticType === "average") {
      result = sum / len;
    } else {
      result = sum;
    }
    return len === 0 ? NaN : result;
  });
}
function mapDataStatistic(ecModel) {
  var seriesGroups = {};
  ecModel.eachSeriesByType("map", function(seriesModel) {
    var hostGeoModel = seriesModel.getHostGeoModel();
    var key = hostGeoModel ? "o" + hostGeoModel.id : "i" + seriesModel.getMapType();
    (seriesGroups[key] = seriesGroups[key] || []).push(seriesModel);
  });
  each(seriesGroups, function(seriesList, key) {
    var data = dataStatistics(map(seriesList, function(seriesModel) {
      return seriesModel.getData();
    }), seriesList[0].get("mapValueCalculation"));
    for (var i = 0; i < seriesList.length; i++) {
      seriesList[i].originalData = seriesList[i].getData();
    }
    for (var i = 0; i < seriesList.length; i++) {
      seriesList[i].seriesGroup = seriesList;
      seriesList[i].needsDrawMap = i === 0 && !seriesList[i].getHostGeoModel();
      seriesList[i].setData(data.cloneShallow());
      seriesList[i].mainSeries = seriesList[0];
    }
  });
}
export {
  mapDataStatistic as default
};

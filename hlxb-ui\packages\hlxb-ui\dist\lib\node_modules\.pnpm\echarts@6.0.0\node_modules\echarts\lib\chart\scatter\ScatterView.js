"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const SymbolDraw = require("../helper/SymbolDraw.js");
const LargeSymbolDraw = require("../helper/LargeSymbolDraw.js");
const points = require("../../layout/points.js");
const Chart = require("../../view/Chart.js");
var ScatterView = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(ScatterView2, _super);
    function ScatterView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ScatterView2.type;
      return _this;
    }
    ScatterView2.prototype.render = function(seriesModel, ecModel, api) {
      var data = seriesModel.getData();
      var symbolDraw = this._updateSymbolDraw(data, seriesModel);
      symbolDraw.updateData(data, {
        // TODO
        // If this parameter should be a shape or a bounding volume
        // shape will be more general.
        // But bounding volume like bounding rect will be much faster in the contain calculation
        clipShape: this._getClipShape(seriesModel)
      });
      this._finished = true;
    };
    ScatterView2.prototype.incrementalPrepareRender = function(seriesModel, ecModel, api) {
      var data = seriesModel.getData();
      var symbolDraw = this._updateSymbolDraw(data, seriesModel);
      symbolDraw.incrementalPrepareUpdate(data);
      this._finished = false;
    };
    ScatterView2.prototype.incrementalRender = function(taskParams, seriesModel, ecModel) {
      this._symbolDraw.incrementalUpdate(taskParams, seriesModel.getData(), {
        clipShape: this._getClipShape(seriesModel)
      });
      this._finished = taskParams.end === seriesModel.getData().count();
    };
    ScatterView2.prototype.updateTransform = function(seriesModel, ecModel, api) {
      var data = seriesModel.getData();
      this.group.dirty();
      if (!this._finished || data.count() > 1e4) {
        return {
          update: true
        };
      } else {
        var res = points.default("").reset(seriesModel, ecModel, api);
        if (res.progress) {
          res.progress({
            start: 0,
            end: data.count(),
            count: data.count()
          }, data);
        }
        this._symbolDraw.updateLayout(data);
      }
    };
    ScatterView2.prototype.eachRendered = function(cb) {
      this._symbolDraw && this._symbolDraw.eachRendered(cb);
    };
    ScatterView2.prototype._getClipShape = function(seriesModel) {
      if (!seriesModel.get("clip", true)) {
        return;
      }
      var coordSys = seriesModel.coordinateSystem;
      return coordSys && coordSys.getArea && coordSys.getArea(0.1);
    };
    ScatterView2.prototype._updateSymbolDraw = function(data, seriesModel) {
      var symbolDraw = this._symbolDraw;
      var pipelineContext = seriesModel.pipelineContext;
      var isLargeDraw = pipelineContext.large;
      if (!symbolDraw || isLargeDraw !== this._isLargeDraw) {
        symbolDraw && symbolDraw.remove();
        symbolDraw = this._symbolDraw = isLargeDraw ? new LargeSymbolDraw.default() : new SymbolDraw.default();
        this._isLargeDraw = isLargeDraw;
        this.group.removeAll();
      }
      this.group.add(symbolDraw.group);
      return symbolDraw;
    };
    ScatterView2.prototype.remove = function(ecModel, api) {
      this._symbolDraw && this._symbolDraw.remove(true);
      this._symbolDraw = null;
    };
    ScatterView2.prototype.dispose = function() {
    };
    ScatterView2.type = "scatter";
    return ScatterView2;
  }(Chart.default)
);
exports.default = ScatterView;

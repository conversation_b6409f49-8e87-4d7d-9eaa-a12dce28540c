import { PRIORITY, registerMap, registerLoading, registerTransform, registerVisual, registerLayout, registerCoordinateSystem, registerAction, registerUpdateLifecycle, registerPostUpdate, registerPostInit, registerProcessor, registerPreprocessor } from "./core/echarts.js";
import ComponentView from "./view/Component.js";
import ChartView from "./view/Chart.js";
import ComponentModel from "./model/Component.js";
import SeriesModel from "./model/Series.js";
import { isArray, each, indexOf, isFunction } from "../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { registerImpl } from "./core/impl.js";
import { registerPainter } from "../../../../zrender@6.0.0/node_modules/zrender/lib/zrender.js";
var extensions = [];
var extensionRegisters = {
  registerPreprocessor,
  registerProcessor,
  registerPostInit,
  registerPostUpdate,
  registerUpdateLifecycle,
  registerAction,
  registerCoordinateSystem,
  registerLayout,
  registerVisual,
  registerTransform,
  registerLoading,
  registerMap,
  registerImpl,
  PRIORITY,
  ComponentModel,
  ComponentView,
  SeriesModel,
  ChartView,
  // TODO Use ComponentModel and SeriesModel instead of Constructor
  registerComponentModel: function(ComponentModelClass) {
    ComponentModel.registerClass(ComponentModelClass);
  },
  registerComponentView: function(ComponentViewClass) {
    ComponentView.registerClass(ComponentViewClass);
  },
  registerSeriesModel: function(SeriesModelClass) {
    SeriesModel.registerClass(SeriesModelClass);
  },
  registerChartView: function(ChartViewClass) {
    ChartView.registerClass(ChartViewClass);
  },
  registerCustomSeries: function(seriesType, renderItem) {
  },
  registerSubTypeDefaulter: function(componentType, defaulter) {
    ComponentModel.registerSubTypeDefaulter(componentType, defaulter);
  },
  registerPainter: function(painterType, PainterCtor) {
    registerPainter(painterType, PainterCtor);
  }
};
function use(ext) {
  if (isArray(ext)) {
    each(ext, function(singleExt) {
      use(singleExt);
    });
    return;
  }
  if (indexOf(extensions, ext) >= 0) {
    return;
  }
  extensions.push(ext);
  if (isFunction(ext)) {
    ext = {
      install: ext
    };
  }
  ext.install(extensionRegisters);
}
export {
  use
};

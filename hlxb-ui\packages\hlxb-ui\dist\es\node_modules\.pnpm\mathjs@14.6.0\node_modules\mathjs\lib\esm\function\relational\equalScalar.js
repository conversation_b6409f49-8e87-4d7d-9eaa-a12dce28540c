import { nearlyEqual } from "../../utils/bignumber/nearlyEqual.js";
import { nearlyEqual as nearlyEqual$1 } from "../../utils/number.js";
import { factory } from "../../utils/factory.js";
import { complexEquals } from "../../utils/complex.js";
import { createCompareUnits } from "./compareUnits.js";
var name = "equalScalar";
var dependencies = ["typed", "config"];
var createEqualScalar = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed,
    config
  } = _ref;
  var compareUnits = createCompareUnits({
    typed
  });
  return typed(name, {
    "boolean, boolean": function boolean_boolean(x, y) {
      return x === y;
    },
    "number, number": function number_number(x, y) {
      return nearlyEqual$1(x, y, config.relTol, config.absTol);
    },
    "BigNumber, BigNumber": function BigNumber_BigNumber(x, y) {
      return x.eq(y) || nearlyEqual(x, y, config.relTol, config.absTol);
    },
    "bigint, bigint": function bigint_bigint(x, y) {
      return x === y;
    },
    "Fraction, Fraction": function Fraction_Fraction(x, y) {
      return x.equals(y);
    },
    "Complex, Complex": function Complex_Complex(x, y) {
      return complexEquals(x, y, config.relTol, config.absTol);
    }
  }, compareUnits);
});
factory(name, ["typed", "config"], (_ref2) => {
  var {
    typed,
    config
  } = _ref2;
  return typed(name, {
    "number, number": function number_number(x, y) {
      return nearlyEqual$1(x, y, config.relTol, config.absTol);
    }
  });
});
export {
  createEqualScalar
};

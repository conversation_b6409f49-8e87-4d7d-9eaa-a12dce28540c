"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const LineSeries = require("./LineSeries.js");
const LineView = require("./LineView.js");
const points = require("../../layout/points.js");
const dataSample = require("../../processor/dataSample.js");
function install(registers) {
  registers.registerChartView(LineView.default);
  registers.registerSeriesModel(LineSeries.default);
  registers.registerLayout(points.default("line", true));
  registers.registerVisual({
    seriesType: "line",
    reset: function(seriesModel) {
      var data = seriesModel.getData();
      var lineStyle = seriesModel.getModel("lineStyle").getLineStyle();
      if (lineStyle && !lineStyle.stroke) {
        lineStyle.stroke = data.getVisual("style").fill;
      }
      data.setVisual("legendLineStyle", lineStyle);
    }
  });
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample.default("line"));
}
exports.install = install;

import PictorialBarView from "./PictorialBarView.js";
import PictorialBarSeriesModel from "./PictorialBarSeries.js";
import { layout, createProgressiveLayout } from "../../layout/barGrid.js";
import { curry } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function install(registers) {
  registers.registerChartView(PictorialBarView);
  registers.registerSeriesModel(PictorialBarSeriesModel);
  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, curry(layout, "pictorialBar"));
  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout("pictorialBar"));
}
export {
  install
};

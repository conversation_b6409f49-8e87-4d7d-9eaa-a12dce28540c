.hlxb-calendar {
  margin: 0 20px;
  border-bottom: #ebebeb 1px solid;
}
.hlxb-calendar-date-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
  font-weight: bold;
}
.hlxb-calendar-date-cell:hover {
  background-color: #f3f4f6;
}
.hlxb-calendar-date-cell.current-month {
  color: #333;
}
.hlxb-calendar-date-cell.other-month {
  color: #999;
}
.hlxb-calendar-date-cell.selected {
  border: var(--theme-color) 1px solid;
}
.hlxb-calendar-date-cell.today {
  background-color: var(--theme-color);
  color: white;
}
.hlxb-calendar-date-cell .calendar-indicator {
  position: absolute;
  border-radius: 50%;
  z-index: 3;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-center {
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-right {
  bottom: 2px;
  right: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-left {
  bottom: 2px;
  left: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-top-right {
  top: 2px;
  right: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-top-left {
  top: 2px;
  left: 2px;
}
.hlxb-calendar-date-grid {
  min-height: 0;
  position: relative;
}
.hlxb-calendar-date-grid .bg-month {
  position: absolute;
  z-index: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 160px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.06);
}
.hlxb-calendar-date-grid .date-week {
  display: flex;
  justify-content: space-around;
  height: 32px;
  margin-top: 8px;
}
.hlxb-calendar-expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  color: #999;
  cursor: pointer;
  position: relative;
  z-index: 2;
  transition: all 0.5s;
}
.hlxb-calendar-expand-btn:hover {
  color: var(--theme-color);
}
.hlxb-calendar-expand-btn .double-down-icon {
  margin-left: 4px;
  transition: all 0.5s;
}
.hlxb-calendar-expand-btn .double-down-icon.isExpanded {
  transform: rotateZ(180deg);
}
.hlxb-calendar-weekdays-header {
  display: flex;
  justify-content: space-around;
  height: 32px;
  background-color: #f2f3f5;
  border-radius: 4px;
}
.hlxb-calendar-weekdays-header .weekday {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  height: 100%;
  width: 32px;
}

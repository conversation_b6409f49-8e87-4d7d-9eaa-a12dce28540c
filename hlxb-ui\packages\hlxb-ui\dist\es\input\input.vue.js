import { defineComponent, useAttrs, useSlots, computed, resolveComponent, createElementBlock, openBlock, normalizeClass, withDirectives, mergeProps, vModelText, createCommentVNode, createElementVNode, unref, renderSlot, createVNode } from "vue";
import { getPrefixCls } from "../config/index.js";
const _hoisted_1 = ["disabled"];
const _hoisted_2 = {
  key: 0,
  class: "r-input-prepend"
};
const _hoisted_3 = { class: "r-input-outer" };
const _hoisted_4 = ["disabled"];
const _hoisted_5 = {
  key: 1,
  class: "r-input-append"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{ name: "HlxbInput" },
  __name: "input",
  props: {
    modelValue: {
      type: [String, Number],
      defalut: ""
    },
    type: {
      type: String,
      validator: (val) => {
        return ["text", "textarea"].includes(val);
      }
    },
    size: {
      type: String,
      default: "",
      validator: (val) => {
        return ["", "small", "medium"].includes(val);
      }
    },
    // 是否能清空
    clearable: Boolean,
    disabled: Boolean,
    center: Boolean
  },
  emits: ["update:modelValue"],
  setup(__props, { emit: __emit }) {
    const attrs = useAttrs();
    const slots = useSlots();
    const props = __props;
    const emit = __emit;
    const prefixCls = getPrefixCls("input");
    const inputValue = computed({
      get() {
        return props.modelValue;
      },
      set(val) {
        emit("update:modelValue", val);
      }
    });
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const inputStyleClass = computed(() => {
      return {
        [`r-input--${props.size}`]: props.size,
        "is-disabled": props.disabled,
        "is-center": props.center
      };
    });
    const className = computed(() => {
      return {
        "has-prepend": slots.prepend,
        "has-append": slots.append
      };
    });
    const inputProps = computed(() => {
      return {
        ...attrs
      };
    });
    const shouClear = computed(() => props.clearable && inputValue.value);
    const clearHandle = () => {
      inputValue.value = "";
    };
    return (_ctx, _cache) => {
      const _component_HlxbIcon = resolveComponent("HlxbIcon");
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(["inline-container", styleClass.value])
      }, [
        __props.type === "textarea" ? withDirectives((openBlock(), createElementBlock("textarea", mergeProps({
          key: 0,
          class: "r-textarea",
          disabled: __props.disabled
        }, inputProps.value, {
          "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => inputValue.value = $event)
        }), null, 16, _hoisted_1)), [
          [vModelText, inputValue.value]
        ]) : (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass(["inline-container", [className.value, styleClass.value]])
        }, [
          unref(slots).prepend ? (openBlock(), createElementBlock("div", _hoisted_2, [
            renderSlot(_ctx.$slots, "prepend")
          ])) : createCommentVNode("", true),
          createElementVNode("div", _hoisted_3, [
            withDirectives(createElementVNode("input", mergeProps({
              type: "text",
              class: "r-input",
              disabled: __props.disabled
            }, inputProps.value, {
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => inputValue.value = $event),
              class: inputStyleClass.value
            }), null, 16, _hoisted_4), [
              [vModelText, inputValue.value]
            ]),
            shouClear.value ? (openBlock(), createElementBlock("span", {
              key: 0,
              class: "r-input-clear",
              onClick: clearHandle
            }, [
              createVNode(_component_HlxbIcon, { name: "close" })
            ])) : createCommentVNode("", true)
          ]),
          unref(slots).append ? (openBlock(), createElementBlock("div", _hoisted_5, [
            renderSlot(_ctx.$slots, "append")
          ])) : createCommentVNode("", true)
        ], 2))
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const prefixCls = require("../hooks/prefixCls.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "WeekdaysHeader"
  },
  __name: "WeekdaysHeader",
  setup(__props) {
    const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(vue.unref(prefixCls.weekdaysHeaderPrefixCls))
      }, [
        (vue.openBlock(), vue.createElementBlock(vue.Fragment, null, vue.renderList(weekdays, (day) => {
          return vue.createElementVNode("div", {
            class: "weekday",
            key: day
          }, vue.toDisplayString(day), 1);
        }), 64))
      ], 2);
    };
  }
});
exports.default = _sfc_main;

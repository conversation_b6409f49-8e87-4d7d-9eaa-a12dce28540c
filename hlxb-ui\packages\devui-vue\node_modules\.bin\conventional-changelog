#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/conventional-changelog-cli@2.2.2/node_modules/conventional-changelog-cli/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/conventional-changelog-cli@2.2.2/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/conventional-changelog-cli@2.2.2/node_modules/conventional-changelog-cli/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/conventional-changelog-cli@2.2.2/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../conventional-changelog-cli/cli.js" "$@"
else
  exec node  "$basedir/../conventional-changelog-cli/cli.js" "$@"
fi

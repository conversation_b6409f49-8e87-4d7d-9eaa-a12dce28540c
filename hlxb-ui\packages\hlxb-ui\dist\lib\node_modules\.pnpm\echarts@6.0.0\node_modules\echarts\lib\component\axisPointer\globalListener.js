"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const env = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/env.js");
const model = require("../../util/model.js");
var inner = model.makeInner();
var each = util.each;
function register(key, api, handler) {
  if (env.default.node) {
    return;
  }
  var zr = api.getZr();
  inner(zr).records || (inner(zr).records = {});
  initGlobalListeners(zr, api);
  var record = inner(zr).records[key] || (inner(zr).records[key] = {});
  record.handler = handler;
}
function initGlobalListeners(zr, api) {
  if (inner(zr).initialized) {
    return;
  }
  inner(zr).initialized = true;
  useHandler("click", util.curry(doEnter, "click"));
  useHandler("mousemove", util.curry(doEnter, "mousemove"));
  useHandler("globalout", onLeave);
  function useHandler(eventType, cb) {
    zr.on(eventType, function(e) {
      var dis = makeDispatchAction(api);
      each(inner(zr).records, function(record) {
        record && cb(record, e, dis.dispatchAction);
      });
      dispatchTooltipFinally(dis.pendings, api);
    });
  }
}
function dispatchTooltipFinally(pendings, api) {
  var showLen = pendings.showTip.length;
  var hideLen = pendings.hideTip.length;
  var actuallyPayload;
  if (showLen) {
    actuallyPayload = pendings.showTip[showLen - 1];
  } else if (hideLen) {
    actuallyPayload = pendings.hideTip[hideLen - 1];
  }
  if (actuallyPayload) {
    actuallyPayload.dispatchAction = null;
    api.dispatchAction(actuallyPayload);
  }
}
function onLeave(record, e, dispatchAction) {
  record.handler("leave", null, dispatchAction);
}
function doEnter(currTrigger, record, e, dispatchAction) {
  record.handler(currTrigger, e, dispatchAction);
}
function makeDispatchAction(api) {
  var pendings = {
    showTip: [],
    hideTip: []
  };
  var dispatchAction = function(payload) {
    var pendingList = pendings[payload.type];
    if (pendingList) {
      pendingList.push(payload);
    } else {
      payload.dispatchAction = dispatchAction;
      api.dispatchAction(payload);
    }
  };
  return {
    dispatchAction,
    pendings
  };
}
function unregister(key, api) {
  if (env.default.node) {
    return;
  }
  var zr = api.getZr();
  var record = (inner(zr).records || {})[key];
  if (record) {
    inner(zr).records[key] = null;
  }
}
exports.register = register;
exports.unregister = unregister;

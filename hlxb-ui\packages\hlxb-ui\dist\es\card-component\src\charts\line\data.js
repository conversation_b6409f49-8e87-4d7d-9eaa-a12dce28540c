import { colorRgba, colorRgbaStar, colorRgbaEnd } from "../../data.js";
import "lodash-es";
import "../../../../utils/lib/echarts.js";
const colorBox = [...colorRgba];
const colorBoxStar = [...colorRgbaStar];
const colorBoxEnd = [...colorRgbaEnd];
function optionFormate(props) {
  const unitName = props.dataList.length ? `单位（${props.dataList[0].unitName}）` : "";
  const unitNameBox = props.dataList.length ? Array.from(new Set(props.dataList.map((item) => item.unitName))) : [];
  let yAxisBox = unitNameBox.length ? unitNameBox.map((item, index) => {
    return {
      type: "value",
      name: `单位（${item}）`,
      // offset: -5,
      // splitNumber: 5,
      nameTextStyle: {
        color: "#333333",
        fontSize: 14,
        align: index === 0 ? "left" : "right"
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#E9E9E9",
          type: "solid"
        }
      },
      splitLine: {
        show: index === 0 ? true : false,
        lineStyle: {
          color: "#E9E9E9",
          type: "dashed"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#666666",
          fontSize: 14
        }
      }
    };
  }) : [
    {
      type: "value",
      name: unitName,
      // offset: -5,
      nameTextStyle: {
        color: "#333333",
        fontSize: 14,
        align: "left"
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#E9E9E9",
          type: "solid"
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#E9E9E9",
          type: "dashed"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#666666",
          fontSize: 14
        }
      }
    }
  ];
  if (yAxisBox.length > 2) {
    yAxisBox = [
      {
        type: "value",
        name: "",
        // offset: -5,
        nameTextStyle: {
          color: "#333333",
          fontSize: 14,
          align: "left"
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: "#E9E9E9",
            type: "solid"
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#E9E9E9",
            type: "dashed"
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: "#666666",
            fontSize: 14
          }
        }
      }
    ];
  }
  return {
    color: [...colorBox],
    tooltip: {
      trigger: "axis",
      appendTo: () => document.body,
      renderMode: "html",
      axisPointer: {
        type: "shadow"
      },
      backgroundColor: "#fff",
      textStyle: {
        color: "#333",
        fontSize: 14,
        lineHeight: 28,
        height: 28,
        fontWeight: 400
      },
      borderColor: "transparent",
      formatter: (params) => {
        const htmlStrBefor = `<div style="color: #999;">${params[0].name}</div>`;
        const htmlStrAfter = params.map((item, index) => {
          return `${item.marker} <span style="display: inline-block; width: 60px;">${item.seriesName}</span><span style="display: inline-block; width: 200px; font-weight: bold; text-align: right;">${item.value ? item.value : "-"}&nbsp;${props.dataList.length ? props.dataList[index].unitName : ""}</span>
                `;
        }).join("<br/>");
        return htmlStrBefor + htmlStrAfter;
      }
    },
    legend: {
      icon: "circle",
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 24,
      color: [...colorBox],
      top: 21,
      data: props.dataList.length ? props.dataList.map((i) => i.indexName) : [],
      textStyle: {
        fontSize: 14,
        color: "#333333"
      }
    },
    grid: {
      top: "25%",
      left: "0",
      right: "0",
      bottom: "1%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: true,
        data: props.dataList[0].XAxis,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#E9E9E9",
            type: "solid"
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          textStyle: {
            color: "#666666",
            fontSize: 14
          }
        },
        axisPointer: {
          type: "shadow"
        }
      }
    ],
    yAxis: yAxisBox,
    series: props.dataList.map((item, index) => {
      return {
        name: item.indexName,
        type: item.type ? item.type : "line",
        barMaxWidth: item.type === "bar" ? 20 : "",
        barGap: item.type === "bar" ? "-100%" : "",
        symbol: "none",
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${colorBoxStar[index]}`
                // 0% 处的颜色
              },
              {
                offset: 1,
                color: `${colorBoxEnd[index]}`
                // 100% 处的颜色
              }
            ],
            global: false
            // 缺省为 false
          }
        },
        color: [colorBox[index]],
        yAxisIndex: unitNameBox.length && unitNameBox.length < 3 ? unitNameBox.indexOf(item.unitName) : 0,
        data: item.data
      };
    }),
    animation: false
  };
}
export {
  optionFormate
};

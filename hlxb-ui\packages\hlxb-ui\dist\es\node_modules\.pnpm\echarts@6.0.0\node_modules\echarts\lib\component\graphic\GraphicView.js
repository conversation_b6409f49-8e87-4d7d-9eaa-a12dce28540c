import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { createHashMap, each, assert, indexOf, extend, hasOwn, retrieve2, isFunction, keys } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import Displayable from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Displayable.js";
import { convertOptionIdName, makeInner } from "../../util/model.js";
import { setTooltipConfig, getShapeClass } from "../../util/graphic.js";
import { positionElement, LOCATION_PARAMS } from "../../util/layout.js";
import { parsePercent } from "../../util/number.js";
import ComponentView from "../../view/Component.js";
import { getECData } from "../../util/innerStore.js";
import { isEC4CompatibleStyle, convertFromEC4CompatibleStyle } from "../../util/styleCompat.js";
import { applyUpdateTransition, updateLeaveTo, isTransitionAll, applyLeaveTransition } from "../../animation/customGraphicTransition.js";
import { updateProps } from "../../animation/basicTransition.js";
import { stopPreviousKeyframeAnimationAndRestore, applyKeyframeAnimation } from "../../animation/customGraphicKeyframeAnimation.js";
import ZRText from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
import ZRImage from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Image.js";
import Group from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
var nonShapeGraphicElements = {
  // Reserved but not supported in graphic component.
  path: null,
  compoundPath: null,
  // Supported in graphic component.
  group: Group,
  image: ZRImage,
  text: ZRText
};
var inner = makeInner();
var GraphicComponentView = (
  /** @class */
  function(_super) {
    __extends(GraphicComponentView2, _super);
    function GraphicComponentView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = GraphicComponentView2.type;
      return _this;
    }
    GraphicComponentView2.prototype.init = function() {
      this._elMap = createHashMap();
    };
    GraphicComponentView2.prototype.render = function(graphicModel, ecModel, api) {
      if (graphicModel !== this._lastGraphicModel) {
        this._clear();
      }
      this._lastGraphicModel = graphicModel;
      this._updateElements(graphicModel);
      this._relocate(graphicModel, api);
    };
    GraphicComponentView2.prototype._updateElements = function(graphicModel) {
      var elOptionsToUpdate = graphicModel.useElOptionsToUpdate();
      if (!elOptionsToUpdate) {
        return;
      }
      var elMap = this._elMap;
      var rootGroup = this.group;
      var globalZ = graphicModel.get("z");
      var globalZLevel = graphicModel.get("zlevel");
      each(elOptionsToUpdate, function(elOption) {
        var id = convertOptionIdName(elOption.id, null);
        var elExisting = id != null ? elMap.get(id) : null;
        var parentId = convertOptionIdName(elOption.parentId, null);
        var targetElParent = parentId != null ? elMap.get(parentId) : rootGroup;
        var elType = elOption.type;
        var elOptionStyle = elOption.style;
        if (elType === "text" && elOptionStyle) {
          if (elOption.hv && elOption.hv[1]) {
            elOptionStyle.textVerticalAlign = elOptionStyle.textBaseline = elOptionStyle.verticalAlign = elOptionStyle.align = null;
          }
        }
        var textContentOption = elOption.textContent;
        var textConfig = elOption.textConfig;
        if (elOptionStyle && isEC4CompatibleStyle(elOptionStyle, elType, !!textConfig, !!textContentOption)) {
          var convertResult = convertFromEC4CompatibleStyle(elOptionStyle, elType);
          if (!textConfig && convertResult.textConfig) {
            textConfig = elOption.textConfig = convertResult.textConfig;
          }
          if (!textContentOption && convertResult.textContent) {
            textContentOption = convertResult.textContent;
          }
        }
        var elOptionCleaned = getCleanedElOption(elOption);
        if (process.env.NODE_ENV !== "production") {
          elExisting && assert(targetElParent === elExisting.parent, "Changing parent is not supported.");
        }
        var $action = elOption.$action || "merge";
        var isMerge = $action === "merge";
        var isReplace = $action === "replace";
        if (isMerge) {
          var isInit = !elExisting;
          var el_1 = elExisting;
          if (isInit) {
            el_1 = createEl(id, targetElParent, elOption.type, elMap);
          } else {
            el_1 && (inner(el_1).isNew = false);
            stopPreviousKeyframeAnimationAndRestore(el_1);
          }
          if (el_1) {
            applyUpdateTransition(el_1, elOptionCleaned, graphicModel, {
              isInit
            });
            updateCommonAttrs(el_1, elOption, globalZ, globalZLevel);
          }
        } else if (isReplace) {
          removeEl(elExisting, elOption, elMap, graphicModel);
          var el_2 = createEl(id, targetElParent, elOption.type, elMap);
          if (el_2) {
            applyUpdateTransition(el_2, elOptionCleaned, graphicModel, {
              isInit: true
            });
            updateCommonAttrs(el_2, elOption, globalZ, globalZLevel);
          }
        } else if ($action === "remove") {
          updateLeaveTo(elExisting, elOption);
          removeEl(elExisting, elOption, elMap, graphicModel);
        }
        var el = elMap.get(id);
        if (el && textContentOption) {
          if (isMerge) {
            var textContentExisting = el.getTextContent();
            textContentExisting ? textContentExisting.attr(textContentOption) : el.setTextContent(new ZRText(textContentOption));
          } else if (isReplace) {
            el.setTextContent(new ZRText(textContentOption));
          }
        }
        if (el) {
          var clipPathOption = elOption.clipPath;
          if (clipPathOption) {
            var clipPathType = clipPathOption.type;
            var clipPath = void 0;
            var isInit = false;
            if (isMerge) {
              var oldClipPath = el.getClipPath();
              isInit = !oldClipPath || inner(oldClipPath).type !== clipPathType;
              clipPath = isInit ? newEl(clipPathType) : oldClipPath;
            } else if (isReplace) {
              isInit = true;
              clipPath = newEl(clipPathType);
            }
            el.setClipPath(clipPath);
            applyUpdateTransition(clipPath, clipPathOption, graphicModel, {
              isInit
            });
            applyKeyframeAnimation(clipPath, clipPathOption.keyframeAnimation, graphicModel);
          }
          var elInner = inner(el);
          el.setTextConfig(textConfig);
          elInner.option = elOption;
          setEventData(el, graphicModel, elOption);
          setTooltipConfig({
            el,
            componentModel: graphicModel,
            itemName: el.name,
            itemTooltipOption: elOption.tooltip
          });
          applyKeyframeAnimation(el, elOption.keyframeAnimation, graphicModel);
        }
      });
    };
    GraphicComponentView2.prototype._relocate = function(graphicModel, api) {
      var elOptions = graphicModel.option.elements;
      var rootGroup = this.group;
      var elMap = this._elMap;
      var apiWidth = api.getWidth();
      var apiHeight = api.getHeight();
      var xy = ["x", "y"];
      for (var i = 0; i < elOptions.length; i++) {
        var elOption = elOptions[i];
        var id = convertOptionIdName(elOption.id, null);
        var el = id != null ? elMap.get(id) : null;
        if (!el || !el.isGroup) {
          continue;
        }
        var parentEl = el.parent;
        var isParentRoot = parentEl === rootGroup;
        var elInner = inner(el);
        var parentElInner = inner(parentEl);
        elInner.width = parsePercent(elInner.option.width, isParentRoot ? apiWidth : parentElInner.width) || 0;
        elInner.height = parsePercent(elInner.option.height, isParentRoot ? apiHeight : parentElInner.height) || 0;
      }
      for (var i = elOptions.length - 1; i >= 0; i--) {
        var elOption = elOptions[i];
        var id = convertOptionIdName(elOption.id, null);
        var el = id != null ? elMap.get(id) : null;
        if (!el) {
          continue;
        }
        var parentEl = el.parent;
        var parentElInner = inner(parentEl);
        var containerInfo = parentEl === rootGroup ? {
          width: apiWidth,
          height: apiHeight
        } : {
          width: parentElInner.width,
          height: parentElInner.height
        };
        var layoutPos = {};
        var layouted = positionElement(el, elOption, containerInfo, null, {
          hv: elOption.hv,
          boundingMode: elOption.bounding
        }, layoutPos);
        if (!inner(el).isNew && layouted) {
          var transition = elOption.transition;
          var animatePos = {};
          for (var k = 0; k < xy.length; k++) {
            var key = xy[k];
            var val = layoutPos[key];
            if (transition && (isTransitionAll(transition) || indexOf(transition, key) >= 0)) {
              animatePos[key] = val;
            } else {
              el[key] = val;
            }
          }
          updateProps(el, animatePos, graphicModel, 0);
        } else {
          el.attr(layoutPos);
        }
      }
    };
    GraphicComponentView2.prototype._clear = function() {
      var _this = this;
      var elMap = this._elMap;
      elMap.each(function(el) {
        removeEl(el, inner(el).option, elMap, _this._lastGraphicModel);
      });
      this._elMap = createHashMap();
    };
    GraphicComponentView2.prototype.dispose = function() {
      this._clear();
    };
    GraphicComponentView2.type = "graphic";
    return GraphicComponentView2;
  }(ComponentView)
);
function newEl(graphicType) {
  if (process.env.NODE_ENV !== "production") {
    assert(graphicType, "graphic type MUST be set");
  }
  var Clz = hasOwn(nonShapeGraphicElements, graphicType) ? nonShapeGraphicElements[graphicType] : getShapeClass(graphicType);
  if (process.env.NODE_ENV !== "production") {
    assert(Clz, "graphic type " + graphicType + " can not be found");
  }
  var el = new Clz({});
  inner(el).type = graphicType;
  return el;
}
function createEl(id, targetElParent, graphicType, elMap) {
  var el = newEl(graphicType);
  targetElParent.add(el);
  elMap.set(id, el);
  inner(el).id = id;
  inner(el).isNew = true;
  return el;
}
function removeEl(elExisting, elOption, elMap, graphicModel) {
  var existElParent = elExisting && elExisting.parent;
  if (existElParent) {
    elExisting.type === "group" && elExisting.traverse(function(el) {
      removeEl(el, elOption, elMap, graphicModel);
    });
    applyLeaveTransition(elExisting, elOption, graphicModel);
    elMap.removeKey(inner(elExisting).id);
  }
}
function updateCommonAttrs(el, elOption, defaultZ, defaultZlevel) {
  if (!el.isGroup) {
    each([
      ["cursor", Displayable.prototype.cursor],
      // We should not support configure z and zlevel in the element level.
      // But seems we didn't limit it previously. So here still use it to avoid breaking.
      ["zlevel", defaultZlevel || 0],
      ["z", defaultZ || 0],
      // z2 must not be null/undefined, otherwise sort error may occur.
      ["z2", 0]
    ], function(item) {
      var prop = item[0];
      if (hasOwn(elOption, prop)) {
        el[prop] = retrieve2(elOption[prop], item[1]);
      } else if (el[prop] == null) {
        el[prop] = item[1];
      }
    });
  }
  each(keys(elOption), function(key) {
    if (key.indexOf("on") === 0) {
      var val = elOption[key];
      el[key] = isFunction(val) ? val : null;
    }
  });
  if (hasOwn(elOption, "draggable")) {
    el.draggable = elOption.draggable;
  }
  elOption.name != null && (el.name = elOption.name);
  elOption.id != null && (el.id = elOption.id);
}
function getCleanedElOption(elOption) {
  elOption = extend({}, elOption);
  each(["id", "parentId", "$action", "hv", "bounding", "textContent", "clipPath"].concat(LOCATION_PARAMS), function(name) {
    delete elOption[name];
  });
  return elOption;
}
function setEventData(el, graphicModel, elOption) {
  var eventData = getECData(el).eventData;
  if (!el.silent && !el.ignore && !eventData) {
    eventData = getECData(el).eventData = {
      componentType: "graphic",
      componentIndex: graphicModel.componentIndex,
      name: el.name
    };
  }
  if (eventData) {
    eventData.info = elOption.info;
  }
}
export {
  GraphicComponentView,
  inner
};

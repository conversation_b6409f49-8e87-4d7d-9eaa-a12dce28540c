"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../../../config/index.js");
const _hoisted_1 = { class: "item-index" };
const _hoisted_2 = {
  key: 0,
  class: "num-index"
};
const _hoisted_3 = { class: "item-content" };
const _hoisted_4 = { class: "value" };
const _hoisted_5 = { class: "unit" };
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbRankingSimple"
  },
  __name: "RankingSimple",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    // 是否显示序号
    indexType: {
      type: Boolean,
      default: false
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("ranking-simple");
    const props = __props;
    console.log("dataList", props);
    return (_ctx, _cache) => {
      return __props.dataList.length ? (vue.openBlock(), vue.createElementBlock("div", {
        key: 0,
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(__props.dataList, (item, index2) => {
          return vue.openBlock(), vue.createElementBlock("div", {
            key: index2,
            class: vue.normalizeClass(["item", __props.indexType ? "item-t" : ""])
          }, [
            vue.createElementVNode("div", _hoisted_1, [
              __props.indexType ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_2, vue.toDisplayString(index2 + 1), 1)) : vue.createCommentVNode("", true),
              vue.createElementVNode("div", {
                style: vue.normalizeStyle({ paddingLeft: __props.indexType ? "8px" : 0 }),
                class: "label"
              }, vue.toDisplayString(item.indexName), 5)
            ]),
            vue.createElementVNode("div", _hoisted_3, [
              vue.createElementVNode("div", _hoisted_4, vue.toDisplayString(item.value), 1),
              vue.createElementVNode("div", _hoisted_5, vue.toDisplayString(item.unitName), 1)
            ])
          ], 2);
        }), 128))
      ], 2)) : vue.createCommentVNode("", true);
    };
  }
});
exports.default = _sfc_main;

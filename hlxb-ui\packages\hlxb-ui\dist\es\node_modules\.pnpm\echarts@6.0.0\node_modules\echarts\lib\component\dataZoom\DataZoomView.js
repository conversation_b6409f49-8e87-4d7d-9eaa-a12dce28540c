import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentView from "../../view/Component.js";
var DataZoomView = (
  /** @class */
  function(_super) {
    __extends(DataZoomView2, _super);
    function DataZoomView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = DataZoomView2.type;
      return _this;
    }
    DataZoomView2.prototype.render = function(dataZoomModel, ecModel, api, payload) {
      this.dataZoomModel = dataZoomModel;
      this.ecModel = ecModel;
      this.api = api;
    };
    DataZoomView2.type = "dataZoom";
    return DataZoomView2;
  }(ComponentView)
);
export {
  DataZoomView as default
};

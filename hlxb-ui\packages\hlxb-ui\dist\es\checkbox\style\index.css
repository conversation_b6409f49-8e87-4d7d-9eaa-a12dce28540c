.hlxb-checkbox.r-checkbox {
  color: #606266;
  margin-right: 10px;
  cursor: pointer;
}
.hlxb-checkbox .r-checkbox--outer {
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  display: inline-block;
  line-height: 1;
  position: relative;
  vertical-align: middle;
}
.hlxb-checkbox .r-checkbox--inner {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}
.hlxb-checkbox .r-checkbox--inner::after {
  box-sizing: content-box;
  content: '';
  border: 1px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  width: 3px;
  transition: transform 0.15s ease-in 0.05s;
  transform-origin: center;
}
.hlxb-checkbox .r-checkbox--inner.is-checked {
  background-color: #409eff;
  border-color: #409eff;
}
.hlxb-checkbox .r-checkbox--inner.is-checked::after {
  transform: rotate(45deg) scaleY(1);
}
.hlxb-checkbox .r-checkbox-native {
  opacity: 0;
  display: none;
}
.hlxb-checkbox .r-checkbox__label {
  margin-left: 10px;
  display: inline-block;
  line-height: 19px;
  font-size: 14px;
}

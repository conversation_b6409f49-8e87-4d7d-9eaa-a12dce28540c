import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { use } from "../../extension.js";
import AxisView from "../axis/AxisView.js";
import PolarAxisPointer from "../axisPointer/PolarAxisPointer.js";
import { install as install$1 } from "../axisPointer/install.js";
import PolarModel from "../../coord/polar/PolarModel.js";
import axisModelCreator from "../../coord/axisModelCreator.js";
import { AngleAxisModel, RadiusAxisModel } from "../../coord/polar/AxisModel.js";
import polarCreator from "../../coord/polar/polarCreator.js";
import AngleAxisView from "../axis/AngleAxisView.js";
import RadiusAxisView from "../axis/RadiusAxisView.js";
import ComponentView from "../../view/Component.js";
import { curry } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import barLayoutPolar from "../../layout/barPolar.js";
var angleAxisExtraOption = {
  startAngle: 90,
  clockwise: true,
  splitNumber: 12,
  axisLabel: {
    rotate: 0
  }
};
var radiusAxisExtraOption = {
  splitNumber: 5
};
var PolarView = (
  /** @class */
  function(_super) {
    __extends(PolarView2, _super);
    function PolarView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = PolarView2.type;
      return _this;
    }
    PolarView2.type = "polar";
    return PolarView2;
  }(ComponentView)
);
function install(registers) {
  use(install$1);
  AxisView.registerAxisPointerClass("PolarAxisPointer", PolarAxisPointer);
  registers.registerCoordinateSystem("polar", polarCreator);
  registers.registerComponentModel(PolarModel);
  registers.registerComponentView(PolarView);
  axisModelCreator(registers, "angle", AngleAxisModel, angleAxisExtraOption);
  axisModelCreator(registers, "radius", RadiusAxisModel, radiusAxisExtraOption);
  registers.registerComponentView(AngleAxisView);
  registers.registerComponentView(RadiusAxisView);
  registers.registerLayout(curry(barLayoutPolar, "bar"));
}
export {
  install
};

import GeoModel from "../../coord/geo/GeoModel.js";
import geoCreator from "../../coord/geo/geoCreator.js";
import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { updateCenterAndZoomInAction } from "../helper/roamHelper.js";
import GeoView from "./GeoView.js";
import geoSourceManager from "../../coord/geo/geoSourceManager.js";
function registerMap(mapName, geoJson, specialAreas) {
  geoSourceManager.registerMap(mapName, geoJson, specialAreas);
}
function install(registers) {
  registers.registerCoordinateSystem("geo", geoCreator);
  registers.registerComponentModel(GeoModel);
  registers.registerComponentView(GeoView);
  registers.registerImpl("registerMap", registerMap);
  registers.registerImpl("getMap", function(mapName) {
    return geoSourceManager.getMapForUser(mapName);
  });
  function makeAction(method, actionInfo) {
    actionInfo.update = "geo:updateSelectStatus";
    registers.registerAction(actionInfo, function(payload, ecModel) {
      var selected = {};
      var allSelected = [];
      ecModel.eachComponent({
        mainType: "geo",
        query: payload
      }, function(geoModel) {
        geoModel[method](payload.name);
        var geo = geoModel.coordinateSystem;
        each(geo.regions, function(region) {
          selected[region.name] = geoModel.isSelected(region.name) || false;
        });
        var names = [];
        each(selected, function(v, name) {
          selected[name] && names.push(name);
        });
        allSelected.push({
          geoIndex: geoModel.componentIndex,
          // Use singular, the same naming convention as the event `selectchanged`.
          name: names
        });
      });
      return {
        selected,
        allSelected,
        name: payload.name
      };
    });
  }
  makeAction("toggleSelected", {
    type: "geoToggleSelect",
    event: "geoselectchanged"
  });
  makeAction("select", {
    type: "geoSelect",
    event: "geoselected"
  });
  makeAction("unSelect", {
    type: "geoUnSelect",
    event: "geounselected"
  });
  registers.registerAction({
    type: "geoRoam",
    event: "geoRoam",
    update: "updateTransform"
  }, function(payload, ecModel, api) {
    var componentType = payload.componentType;
    if (!componentType) {
      if (payload.geoId != null) {
        componentType = "geo";
      } else if (payload.seriesId != null) {
        componentType = "series";
      }
    }
    if (!componentType) {
      componentType = "series";
    }
    ecModel.eachComponent({
      mainType: componentType,
      query: payload
    }, function(componentModel) {
      var geo = componentModel.coordinateSystem;
      if (geo.type !== "geo") {
        return;
      }
      var res = updateCenterAndZoomInAction(geo, payload, componentModel.get("scaleLimit"));
      componentModel.setCenter && componentModel.setCenter(res.center);
      componentModel.setZoom && componentModel.setZoom(res.zoom);
      if (componentType === "series") {
        each(componentModel.seriesGroup, function(seriesModel) {
          seriesModel.setCenter(res.center);
          seriesModel.setZoom(res.zoom);
        });
      }
    });
  });
}
export {
  install
};

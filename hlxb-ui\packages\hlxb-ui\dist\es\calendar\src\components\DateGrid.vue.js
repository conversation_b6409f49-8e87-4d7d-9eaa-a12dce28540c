import { defineComponent, computed, createElementBlock, openBlock, normalizeClass, unref, createCommentVNode, Fragment, renderList, createBlock, withCtx, renderSlot, createTextVNode, toDisplayString } from "vue";
import _sfc_main$1 from "./DateCell.vue.js";
/* empty css              */
import { useCalendarContext } from "../hooks/useCalendarContext.js";
import { dateGridPrefixCls } from "../hooks/prefixCls.js";
const _hoisted_1 = {
  key: 0,
  class: "bg-month"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "DateGrid"
  },
  __name: "DateGrid",
  props: {
    displayedDates: {}
  },
  emits: ["date-click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const { calendarContext } = useCalendarContext();
    const emit = __emit;
    const datesByWeeks = computed(() => {
      const weeks = [];
      const dates = props.displayedDates;
      for (let i = 0; i < dates.length; i += 7) {
        weeks.push(dates.slice(i, i + 7));
      }
      return weeks;
    });
    const handleDateClick = (date, dateItem) => {
      emit("date-click", date, dateItem);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(dateGridPrefixCls))
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(datesByWeeks.value, (week, weekIndex) => {
          return openBlock(), createElementBlock("div", {
            key: weekIndex,
            class: "date-week"
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(week, (dateInfo) => {
              return openBlock(), createBlock(_sfc_main$1, {
                key: dateInfo.dateString,
                dateInfo,
                onClick: _cache[0] || (_cache[0] = (date, dateItem) => handleDateClick(date, dateItem))
              }, {
                "date-cell": withCtx(({ dateItem }) => [
                  renderSlot(_ctx.$slots, "date-cell", { dateItem }, () => [
                    createTextVNode(toDisplayString(dateItem.date.date()), 1)
                  ])
                ]),
                _: 2
              }, 1032, ["dateInfo"]);
            }), 128))
          ]);
        }), 128)),
        unref(calendarContext).isExpanded ? (openBlock(), createElementBlock("div", _hoisted_1, toDisplayString(unref(calendarContext).currentDate.month() + 1), 1)) : createCommentVNode("", true)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

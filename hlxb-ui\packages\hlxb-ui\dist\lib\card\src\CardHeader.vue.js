"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../../config/index.js");
const _hoisted_1 = { class: "header_left" };
const _hoisted_2 = {
  key: 0,
  class: "text"
};
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCardHeader",
    inheritAttrs: false
  },
  __name: "CardHeader",
  props: {
    // 卡片标题，类型为字符串
    title: {
      type: String
    },
    // 是否显示底部分割线，类型为布尔值，默认显示
    line: {
      type: Boolean,
      default: true
    },
    // 卡片头部的样式对象，类型为 CSSProperties，默认值为空对象
    headerStyleDate: {
      type: Object,
      default: () => {
      }
    },
    // 卡片头部的额外类名，类型为字符串，默认值为空字符串
    headerClassName: {
      type: String,
      default: ""
    },
    // 深色模式 浅色 大屏等其他主题色
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("card-header");
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        style: vue.normalizeStyle(__props.headerStyleDate ? __props.headerStyleDate : ""),
        class: vue.normalizeClass([
          vue.unref(prefixCls),
          { "base-header_line": __props.line },
          __props.headerClassName ? __props.headerClassName : "",
          __props.themeColor
        ])
      }, [
        vue.createElementVNode("div", _hoisted_1, [
          vue.renderSlot(_ctx.$slots, "headerLeftBefore"),
          __props.title ? (vue.openBlock(), vue.createElementBlock("span", _hoisted_2, vue.toDisplayString(__props.title), 1)) : vue.createCommentVNode("", true),
          vue.renderSlot(_ctx.$slots, "headerLeftAfter")
        ]),
        vue.renderSlot(_ctx.$slots, "headerRight")
      ], 6);
    };
  }
});
exports.default = _sfc_main;

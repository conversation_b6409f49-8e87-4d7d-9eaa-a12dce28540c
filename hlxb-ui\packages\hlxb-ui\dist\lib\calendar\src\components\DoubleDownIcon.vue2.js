"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  __name: "DoubleDownIcon",
  props: {
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  setup(__props) {
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("svg", vue.mergeProps({
        class: ["w-12px h-12px ml-4px double-down-icon", { isExpanded: __props.isExpanded }],
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewBox: "0 0 12.083984375 11.90478515625"
      }, _ctx.$attrs), [..._cache[0] || (_cache[0] = [
        vue.createStaticVNode('<defs><clipPath id="master_svg0_659_35523"><rect x="0" y="5" width="12.08349609375" height="6.904879570007324" rx="0"></rect></clipPath><clipPath id="master_svg1_659_35521"><rect x="0" y="0" width="12.08349609375" height="6.904879570007324" rx="0"></rect></clipPath></defs><g><g clip-path="url(#master_svg0_659_35523)"><g transform="matrix(0,-1,1,0,-11.90625,11.90625)"><path d="M6.70258,22.84385C6.9723,23.10595,6.9723,23.53095,6.70261,23.79315C6.43303,24.05525,5.99581,24.05525,5.72615,23.79315C5.72615,23.79315,0.202215,18.42261,0.202215,18.42261C-0.0674051,18.16046,-0.0674051,17.73553,0.202215,17.473390000000002C0.202215,17.473390000000002,5.72622,12.102932,5.72622,12.102932C5.99581,11.8406993,6.43299,11.840702,6.70261,12.102864C6.9723,12.365027,6.9723,12.790074,6.70261,13.05223C6.70261,13.05223,1.66674,17.948,1.66674,17.948C1.66674,17.948,6.70251,22.84385,6.70251,22.84385C6.70251,22.84385,6.70258,22.84385,6.70258,22.84385Z" fill="currentColor"></path></g></g><g style="opacity:0.5;" clip-path="url(#master_svg1_659_35521)"><g transform="matrix(0,-1,1,0,-5.90625,5.90625)"><path d="M6.70258,16.84385C6.9723,17.10595,6.9723,17.53095,6.70261,17.79315C6.43303,18.05525,5.99581,18.05525,5.72615,17.79315C5.72615,17.79315,0.202215,12.422609999999999,0.202215,12.422609999999999C-0.0674051,12.16046,-0.0674051,11.73553,0.202215,11.47339C0.202215,11.47339,5.72622,6.102932,5.72622,6.102932C5.99581,5.8406993,6.43299,5.840702,6.70261,6.102864C6.9723,6.3650269999999995,6.9723,6.790074,6.70261,7.05223C6.70261,7.05223,1.66674,11.948,1.66674,11.948C1.66674,11.948,6.70251,16.84385,6.70251,16.84385C6.70251,16.84385,6.70258,16.84385,6.70258,16.84385Z" fill="currentColor"></path></g></g></g>', 2)
      ])], 16);
    };
  }
});
exports.default = _sfc_main;

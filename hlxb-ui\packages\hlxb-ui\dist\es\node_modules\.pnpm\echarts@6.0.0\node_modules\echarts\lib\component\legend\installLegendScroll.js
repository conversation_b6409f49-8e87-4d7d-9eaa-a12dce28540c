import { use } from "../../extension.js";
import { install as install$1 } from "./installLegendPlain.js";
import ScrollableLegendModel from "./ScrollableLegendModel.js";
import ScrollableLegendView from "./ScrollableLegendView.js";
import installScrollableLegendAction from "./scrollableLegendAction.js";
function install(registers) {
  use(install$1);
  registers.registerComponentModel(ScrollableLegendModel);
  registers.registerComponentView(ScrollableLegendView);
  installScrollableLegendAction(registers);
}
export {
  install
};

import { isArray } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { GraphicComponentModel } from "./GraphicModel.js";
import { GraphicComponentView } from "./GraphicView.js";
function install(registers) {
  registers.registerComponentModel(GraphicComponentModel);
  registers.registerComponentView(GraphicComponentView);
  registers.registerPreprocessor(function(option) {
    var graphicOption = option.graphic;
    if (isArray(graphicOption)) {
      if (!graphicOption[0] || !graphicOption[0].elements) {
        option.graphic = [{
          elements: graphicOption
        }];
      } else {
        option.graphic = [option.graphic[0]];
      }
    } else if (graphicOption && !graphicOption.elements) {
      option.graphic = [{
        elements: [graphicOption]
      }];
    }
  });
}
export {
  install
};

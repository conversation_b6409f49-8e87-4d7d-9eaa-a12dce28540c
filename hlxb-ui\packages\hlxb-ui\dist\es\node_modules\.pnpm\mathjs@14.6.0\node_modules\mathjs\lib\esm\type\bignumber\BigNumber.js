import { Decimal } from "../../../../../../../decimal.js@10.6.0/node_modules/decimal.js/decimal.js";
import { factory } from "../../utils/factory.js";
var name = "BigNumber";
var dependencies = ["?on", "config"];
var createBigNumberClass = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    on,
    config
  } = _ref;
  var BigNumber = Decimal.clone({
    precision: config.precision,
    modulo: Decimal.EUCLID
  });
  BigNumber.prototype = Object.create(BigNumber.prototype);
  BigNumber.prototype.type = "BigNumber";
  BigNumber.prototype.isBigNumber = true;
  BigNumber.prototype.toJSON = function() {
    return {
      mathjs: "BigNumber",
      value: this.toString()
    };
  };
  BigNumber.fromJSON = function(json) {
    return new BigNumber(json.value);
  };
  if (on) {
    on("config", function(curr, prev) {
      if (curr.precision !== prev.precision) {
        BigNumber.config({
          precision: curr.precision
        });
      }
    });
  }
  return BigNumber;
}, {
  isClass: true
});
export {
  createBigNumberClass
};

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const antDesignVue = require("ant-design-vue");
const EmptyIcon_vue_vue_type_script_setup_true_lang = require("./EmptyIcon.vue.js");
const index = require("../config/index.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbEmpty"
  },
  __name: "index",
  setup(__props) {
    const prefixCls = index.getPrefixCls("empty");
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), "flex justify-center items-center flex-col"]),
        style: { "color": "#999999" }
      }, [
        vue.createVNode(vue.unref(antDesignVue.Empty), vue.normalizeProps(vue.guardReactiveProps(_ctx.$attrs)), vue.createSlots({
          description: vue.withCtx(() => [
            vue.renderSlot(_ctx.$slots, "description")
          ]),
          _: 2
        }, [
          !_ctx.$attrs.image ? {
            name: "image",
            fn: vue.withCtx(() => [
              vue.renderSlot(_ctx.$slots, "image", {}, () => [
                vue.createVNode(EmptyIcon_vue_vue_type_script_setup_true_lang.default)
              ])
            ]),
            key: "0"
          } : void 0
        ]), 1040),
        vue.renderSlot(_ctx.$slots, "bottom")
      ], 2);
    };
  }
});
exports.default = _sfc_main;

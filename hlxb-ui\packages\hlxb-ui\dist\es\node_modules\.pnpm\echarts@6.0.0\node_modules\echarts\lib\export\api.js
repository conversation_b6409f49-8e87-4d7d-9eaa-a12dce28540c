import ComponentModel from "../model/Component.js";
import ComponentView from "../view/Component.js";
import SeriesModel from "../model/Series.js";
import ChartView from "../view/Chart.js";
function extendComponentModel(proto) {
  var Model = ComponentModel.extend(proto);
  ComponentModel.registerClass(Model);
  return Model;
}
function extendComponentView(proto) {
  var View = ComponentView.extend(proto);
  ComponentView.registerClass(View);
  return View;
}
function extendSeriesModel(proto) {
  var Model = SeriesModel.extend(proto);
  SeriesModel.registerClass(Model);
  return Model;
}
function extendChartView(proto) {
  var View = ChartView.extend(proto);
  ChartView.registerClass(View);
  return View;
}
export {
  ChartView,
  ComponentModel,
  ComponentView,
  SeriesModel,
  extendChartView,
  extendComponentModel,
  extendComponentView,
  extendSeriesModel
};

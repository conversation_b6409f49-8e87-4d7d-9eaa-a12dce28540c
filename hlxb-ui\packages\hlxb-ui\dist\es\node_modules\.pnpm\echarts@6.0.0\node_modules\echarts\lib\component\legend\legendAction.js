import { curry, each, hasOwn } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function legendSelectActionHandler(methodName, payload, ecModel) {
  var isAllSelect = methodName === "allSelect" || methodName === "inverseSelect";
  var selectedMap = {};
  var actionLegendIndices = [];
  ecModel.eachComponent({
    mainType: "legend",
    query: payload
  }, function(legendModel) {
    if (isAllSelect) {
      legendModel[methodName]();
    } else {
      legendModel[methodName](payload.name);
    }
    makeSelectedMap(legendModel, selectedMap);
    actionLegendIndices.push(legendModel.componentIndex);
  });
  var allSelectedMap = {};
  ecModel.eachComponent("legend", function(legendModel) {
    each(selectedMap, function(isSelected, name) {
      legendModel[isSelected ? "select" : "unSelect"](name);
    });
    makeSelectedMap(legendModel, allSelectedMap);
  });
  return isAllSelect ? {
    selected: allSelectedMap,
    // return legendIndex array to tell the developers which legends are allSelect / inverseSelect
    legendIndex: actionLegendIndices
  } : {
    name: payload.name,
    selected: allSelectedMap
  };
}
function makeSelectedMap(legendModel, out) {
  var selectedMap = out || {};
  each(legendModel.getData(), function(model) {
    var name = model.get("name");
    if (name === "\n" || name === "") {
      return;
    }
    var isItemSelected = legendModel.isSelected(name);
    if (hasOwn(selectedMap, name)) {
      selectedMap[name] = selectedMap[name] && isItemSelected;
    } else {
      selectedMap[name] = isItemSelected;
    }
  });
  return selectedMap;
}
function installLegendAction(registers) {
  registers.registerAction("legendToggleSelect", "legendselectchanged", curry(legendSelectActionHandler, "toggleSelected"));
  registers.registerAction("legendAllSelect", "legendselectall", curry(legendSelectActionHandler, "allSelect"));
  registers.registerAction("legendInverseSelect", "legendinverseselect", curry(legendSelectActionHandler, "inverseSelect"));
  registers.registerAction("legendSelect", "legendselected", curry(legendSelectActionHandler, "select"));
  registers.registerAction("legendUnSelect", "legendunselected", curry(legendSelectActionHandler, "unSelect"));
}
export {
  installLegendAction
};

import { min, max, clone, sub, scale, distance, add } from "../../core/vector.js";
function smoothBezier(points, smooth, isLoop, constraint) {
  var cps = [];
  var v = [];
  var v1 = [];
  var v2 = [];
  var prevPoint;
  var nextPoint;
  var min$1;
  var max$1;
  if (constraint) {
    min$1 = [Infinity, Infinity];
    max$1 = [-Infinity, -Infinity];
    for (var i = 0, len = points.length; i < len; i++) {
      min(min$1, min$1, points[i]);
      max(max$1, max$1, points[i]);
    }
    min(min$1, min$1, constraint[0]);
    max(max$1, max$1, constraint[1]);
  }
  for (var i = 0, len = points.length; i < len; i++) {
    var point = points[i];
    if (isLoop) {
      prevPoint = points[i ? i - 1 : len - 1];
      nextPoint = points[(i + 1) % len];
    } else {
      if (i === 0 || i === len - 1) {
        cps.push(clone(points[i]));
        continue;
      } else {
        prevPoint = points[i - 1];
        nextPoint = points[i + 1];
      }
    }
    sub(v, nextPoint, prevPoint);
    scale(v, v, smooth);
    var d0 = distance(point, prevPoint);
    var d1 = distance(point, nextPoint);
    var sum = d0 + d1;
    if (sum !== 0) {
      d0 /= sum;
      d1 /= sum;
    }
    scale(v1, v, -d0);
    scale(v2, v, d1);
    var cp0 = add([], point, v1);
    var cp1 = add([], point, v2);
    if (constraint) {
      max(cp0, cp0, min$1);
      min(cp0, cp0, max$1);
      max(cp1, cp1, min$1);
      min(cp1, cp1, max$1);
    }
    cps.push(cp0);
    cps.push(cp1);
  }
  if (isLoop) {
    cps.push(cps.shift());
  }
  return cps;
}
export {
  smoothBezier as default
};

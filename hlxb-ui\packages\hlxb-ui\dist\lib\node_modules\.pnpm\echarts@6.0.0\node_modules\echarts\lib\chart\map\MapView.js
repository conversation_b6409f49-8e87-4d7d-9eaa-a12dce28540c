"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const MapDraw = require("../../component/helper/MapDraw.js");
const Chart = require("../../view/Chart.js");
const labelStyle = require("../../label/labelStyle.js");
const states = require("../../util/states.js");
const Circle = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Circle.js");
var MapView = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(MapView2, _super);
    function MapView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MapView2.type;
      return _this;
    }
    MapView2.prototype.render = function(mapModel, ecModel, api, payload) {
      if (payload && payload.type === "mapToggleSelect" && payload.from === this.uid) {
        return;
      }
      var group = this.group;
      group.removeAll();
      if (mapModel.getHostGeoModel()) {
        return;
      }
      if (this._mapDraw && payload && payload.type === "geoRoam") {
        this._mapDraw.resetForLabelLayout();
      }
      if (!(payload && payload.type === "geoRoam" && payload.componentType === "series" && payload.seriesId === mapModel.id)) {
        if (mapModel.needsDrawMap) {
          var mapDraw = this._mapDraw || new MapDraw.default(api);
          group.add(mapDraw.group);
          mapDraw.draw(mapModel, ecModel, api, this, payload);
          this._mapDraw = mapDraw;
        } else {
          this._mapDraw && this._mapDraw.remove();
          this._mapDraw = null;
        }
      } else {
        var mapDraw = this._mapDraw;
        mapDraw && group.add(mapDraw.group);
      }
      mapModel.get("showLegendSymbol") && ecModel.getComponent("legend") && this._renderSymbols(mapModel, ecModel, api);
    };
    MapView2.prototype.remove = function() {
      this._mapDraw && this._mapDraw.remove();
      this._mapDraw = null;
      this.group.removeAll();
    };
    MapView2.prototype.dispose = function() {
      this._mapDraw && this._mapDraw.remove();
      this._mapDraw = null;
    };
    MapView2.prototype._renderSymbols = function(mapModel, ecModel, api) {
      var originalData = mapModel.originalData;
      var group = this.group;
      originalData.each(originalData.mapDimension("value"), function(value, originalDataIndex) {
        if (isNaN(value)) {
          return;
        }
        var layout = originalData.getItemLayout(originalDataIndex);
        if (!layout || !layout.point) {
          return;
        }
        var point = layout.point;
        var offset = layout.offset;
        var circle = new Circle.default({
          style: {
            // Because the special of map draw.
            // Which needs statistic of multiple series and draw on one map.
            // And each series also need a symbol with legend color
            //
            // Layout and visual are put one the different data
            // TODO
            fill: mapModel.getData().getVisual("style").fill
          },
          shape: {
            cx: point[0] + offset * 9,
            cy: point[1],
            r: 3
          },
          silent: true,
          // Do not overlap the first series, on which labels are displayed.
          z2: 8 + (!offset ? states.Z2_EMPHASIS_LIFT + 1 : 0)
        });
        if (!offset) {
          var fullData = mapModel.mainSeries.getData();
          var name_1 = originalData.getName(originalDataIndex);
          var fullIndex_1 = fullData.indexOfName(name_1);
          var itemModel = originalData.getItemModel(originalDataIndex);
          var labelModel = itemModel.getModel("label");
          var regionGroup = fullData.getItemGraphicEl(fullIndex_1);
          labelStyle.setLabelStyle(circle, labelStyle.getLabelStatesModels(itemModel), {
            labelFetcher: {
              getFormattedLabel: function(idx, state) {
                return mapModel.getFormattedLabel(fullIndex_1, state);
              }
            },
            defaultText: name_1
          });
          circle.disableLabelAnimation = true;
          if (!labelModel.get("position")) {
            circle.setTextConfig({
              position: "bottom"
            });
          }
          regionGroup.onHoverStateChange = function(toState) {
            states.setStatesFlag(circle, toState);
          };
        }
        group.add(circle);
      });
    };
    MapView2.type = "map";
    return MapView2;
  }(Chart.default)
);
exports.default = MapView;

import { __extends } from "../../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { bind, each as each$1, clone } from "../../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import BrushController from "../../helper/BrushController.js";
import BrushTargetManager from "../../helper/BrushTargetManager.js";
import { push, pop, count } from "../../dataZoom/history.js";
import sliderMove from "../../helper/sliderMove.js";
import { ToolboxFeature } from "../featureManager.js";
import { parseFinder, makeInternalComponentId } from "../../../util/model.js";
import { registerInternalOptionCreator } from "../../../model/internalComponentCreator.js";
import tokens from "../../../visual/tokens.js";
var each = each$1;
var DATA_ZOOM_ID_BASE = makeInternalComponentId("toolbox-dataZoom_");
var DataZoomFeature = (
  /** @class */
  function(_super) {
    __extends(DataZoomFeature2, _super);
    function DataZoomFeature2() {
      return _super !== null && _super.apply(this, arguments) || this;
    }
    DataZoomFeature2.prototype.render = function(featureModel, ecModel, api, payload) {
      if (!this._brushController) {
        this._brushController = new BrushController(api.getZr());
        this._brushController.on("brush", bind(this._onBrush, this)).mount();
      }
      updateZoomBtnStatus(featureModel, ecModel, this, payload, api);
      updateBackBtnStatus(featureModel, ecModel);
    };
    DataZoomFeature2.prototype.onclick = function(ecModel, api, type) {
      handlers[type].call(this);
    };
    DataZoomFeature2.prototype.remove = function(ecModel, api) {
      this._brushController && this._brushController.unmount();
    };
    DataZoomFeature2.prototype.dispose = function(ecModel, api) {
      this._brushController && this._brushController.dispose();
    };
    DataZoomFeature2.prototype._onBrush = function(eventParam) {
      var areas = eventParam.areas;
      if (!eventParam.isEnd || !areas.length) {
        return;
      }
      var snapshot = {};
      var ecModel = this.ecModel;
      this._brushController.updateCovers([]);
      var brushTargetManager = new BrushTargetManager(makeAxisFinder(this.model), ecModel, {
        include: ["grid"]
      });
      brushTargetManager.matchOutputRanges(areas, ecModel, function(area, coordRange, coordSys) {
        if (coordSys.type !== "cartesian2d") {
          return;
        }
        var brushType = area.brushType;
        if (brushType === "rect") {
          setBatch("x", coordSys, coordRange[0]);
          setBatch("y", coordSys, coordRange[1]);
        } else {
          setBatch({
            lineX: "x",
            lineY: "y"
          }[brushType], coordSys, coordRange);
        }
      });
      push(ecModel, snapshot);
      this._dispatchZoomAction(snapshot);
      function setBatch(dimName, coordSys, minMax) {
        var axis = coordSys.getAxis(dimName);
        var axisModel = axis.model;
        var dataZoomModel = findDataZoom(dimName, axisModel, ecModel);
        var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy(axisModel).getMinMaxSpan();
        if (minMaxSpan.minValueSpan != null || minMaxSpan.maxValueSpan != null) {
          minMax = sliderMove(0, minMax.slice(), axis.scale.getExtent(), 0, minMaxSpan.minValueSpan, minMaxSpan.maxValueSpan);
        }
        dataZoomModel && (snapshot[dataZoomModel.id] = {
          dataZoomId: dataZoomModel.id,
          startValue: minMax[0],
          endValue: minMax[1]
        });
      }
      function findDataZoom(dimName, axisModel, ecModel2) {
        var found;
        ecModel2.eachComponent({
          mainType: "dataZoom",
          subType: "select"
        }, function(dzModel) {
          var has = dzModel.getAxisModel(dimName, axisModel.componentIndex);
          has && (found = dzModel);
        });
        return found;
      }
    };
    DataZoomFeature2.prototype._dispatchZoomAction = function(snapshot) {
      var batch = [];
      each(snapshot, function(batchItem, dataZoomId) {
        batch.push(clone(batchItem));
      });
      batch.length && this.api.dispatchAction({
        type: "dataZoom",
        from: this.uid,
        batch
      });
    };
    DataZoomFeature2.getDefaultOption = function(ecModel) {
      var defaultOption = {
        show: true,
        filterMode: "filter",
        // Icon group
        icon: {
          zoom: "M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",
          back: "M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"
        },
        // `zoom`, `back`
        title: ecModel.getLocaleModel().get(["toolbox", "dataZoom", "title"]),
        brushStyle: {
          borderWidth: 0,
          color: tokens.color.backgroundTint
        }
      };
      return defaultOption;
    };
    return DataZoomFeature2;
  }(ToolboxFeature)
);
var handlers = {
  zoom: function() {
    var nextActive = !this._isZoomActive;
    this.api.dispatchAction({
      type: "takeGlobalCursor",
      key: "dataZoomSelect",
      dataZoomSelectActive: nextActive
    });
  },
  back: function() {
    this._dispatchZoomAction(pop(this.ecModel));
  }
};
function makeAxisFinder(dzFeatureModel) {
  var setting = {
    xAxisIndex: dzFeatureModel.get("xAxisIndex", true),
    yAxisIndex: dzFeatureModel.get("yAxisIndex", true),
    xAxisId: dzFeatureModel.get("xAxisId", true),
    yAxisId: dzFeatureModel.get("yAxisId", true)
  };
  if (setting.xAxisIndex == null && setting.xAxisId == null) {
    setting.xAxisIndex = "all";
  }
  if (setting.yAxisIndex == null && setting.yAxisId == null) {
    setting.yAxisIndex = "all";
  }
  return setting;
}
function updateBackBtnStatus(featureModel, ecModel) {
  featureModel.setIconStatus("back", count(ecModel) > 1 ? "emphasis" : "normal");
}
function updateZoomBtnStatus(featureModel, ecModel, view, payload, api) {
  var zoomActive = view._isZoomActive;
  if (payload && payload.type === "takeGlobalCursor") {
    zoomActive = payload.key === "dataZoomSelect" ? payload.dataZoomSelectActive : false;
  }
  view._isZoomActive = zoomActive;
  featureModel.setIconStatus("zoom", zoomActive ? "emphasis" : "normal");
  var brushTargetManager = new BrushTargetManager(makeAxisFinder(featureModel), ecModel, {
    include: ["grid"]
  });
  var panels = brushTargetManager.makePanelOpts(api, function(targetInfo) {
    return targetInfo.xAxisDeclared && !targetInfo.yAxisDeclared ? "lineX" : !targetInfo.xAxisDeclared && targetInfo.yAxisDeclared ? "lineY" : "rect";
  });
  view._brushController.setPanels(panels).enableBrush(zoomActive && panels.length ? {
    brushType: "auto",
    brushStyle: featureModel.getModel("brushStyle").getItemStyle()
  } : false);
}
registerInternalOptionCreator("dataZoom", function(ecModel) {
  var toolboxModel = ecModel.getComponent("toolbox", 0);
  var featureDataZoomPath = ["feature", "dataZoom"];
  if (!toolboxModel || toolboxModel.get(featureDataZoomPath) == null) {
    return;
  }
  var dzFeatureModel = toolboxModel.getModel(featureDataZoomPath);
  var dzOptions = [];
  var finder = makeAxisFinder(dzFeatureModel);
  var finderResult = parseFinder(ecModel, finder);
  each(finderResult.xAxisModels, function(axisModel) {
    return buildInternalOptions(axisModel, "xAxis", "xAxisIndex");
  });
  each(finderResult.yAxisModels, function(axisModel) {
    return buildInternalOptions(axisModel, "yAxis", "yAxisIndex");
  });
  function buildInternalOptions(axisModel, axisMainType, axisIndexPropName) {
    var axisIndex = axisModel.componentIndex;
    var newOpt = {
      type: "select",
      $fromToolbox: true,
      // Default to be filter
      filterMode: dzFeatureModel.get("filterMode", true) || "filter",
      // Id for merge mapping.
      id: DATA_ZOOM_ID_BASE + axisMainType + axisIndex
    };
    newOpt[axisIndexPropName] = axisIndex;
    dzOptions.push(newOpt);
  }
  return dzOptions;
});
export {
  DataZoomFeature as default
};

function HlxbUIStyleImport(options = {}) {
  const { importStyle = true, resolveStyle } = options;
  if (!importStyle) {
    return {
      name: "hlxb-ui-style-import",
      transform() {
        return null;
      }
    };
  }
  const componentMap = {
    HlxbBarPlusCard: "card-component/combinationCards",
    HlxbBarSimpleCard: "card-component/combinationCards",
    HlxbButton: "button",
    HlxbCalendar: "calendar",
    HlxbCard: "card",
    HlxbCardBody: "card",
    HlxbCardEmpty: "card-component/basicComponents",
    HlxbCardHeader: "card",
    HlxbCardLoading: "card-component/basicComponents",
    HlxbCheckbox: "checkbox",
    HlxbDrugSummary: "card-component/basicComponents",
    HlxbEnergySummary: "card-component/basicComponents",
    HlxbGrid: "grid",
    HlxbHorizontaSmallSquare: "card-component/basicComponents",
    HlxbIcon: "icon",
    HlxbInput: "input",
    HlxbInputNumber: "input-number",
    HlxbLinePlusCard: "card-component/combinationCards",
    HlxbLineSimpleCard: "card-component/combinationCards",
    HlxbPiePlusCard: "card-component/combinationCards",
    HlxbPieSimpleCard: "card-component/combinationCards",
    HlxbRadio: "radio",
    HlxbRanking: "card-component/basicComponents",
    HlxbRankingPlusCard: "card-component/combinationCards",
    HlxbRankingSimple: "card-component/basicComponents",
    HlxbRankingSimpleCard: "card-component/combinationCards",
    HlxbSummarySimpleCard: "card-component/combinationCards",
    HlxbSwiper: "swiper",
    HlxbWeather: "weather"
  };
  const componentDependencies = {
    DateCell: ["calendar"],
    DateGrid: ["calendar"],
    ExpandButton: ["calendar"],
    HlxbBarEcharts: ["card"],
    HlxbBarPlusCard: ["card", "card-component/basicComponents"],
    HlxbBarSimpleCard: ["card", "card-component/basicComponents"],
    HlxbCardEmpty: ["card"],
    HlxbDrugSummary: ["card"],
    HlxbEnergySummary: ["card"],
    HlxbHorizontaSmallSquare: ["card"],
    HlxbLineEcharts: ["card"],
    HlxbLinePlusCard: ["card", "card-component/basicComponents"],
    HlxbLineSimpleCard: ["card", "card-component/basicComponents"],
    HlxbPiePlusCard: ["card", "card-component/basicComponents"],
    HlxbPieSimpleCard: ["card", "card-component/basicComponents"],
    HlxbRanking: ["card"],
    HlxbRankingPlusCard: ["card", "card-component/basicComponents"],
    HlxbRankingSimpleCard: ["card", "card-component/basicComponents"],
    HlxbSummarySimpleCard: ["card", "card-component/basicComponents"],
    WeekdaysHeader: ["calendar"]
  };
  return {
    name: "hlxb-ui-style-import",
    transform(code, id) {
      if (!/\.(vue|[jt]sx?)$/.test(id)) {
        return null;
      }
      const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+\w+)\s+from\s+['"]hlxb-ui['"];?/g;
      let match;
      const styleImports = [];
      while ((match = importRegex.exec(code)) !== null) {
        const namedImports = match[1];
        if (namedImports) {
          const componentNames = namedImports.split(",").map((name) => name.trim().replace(/\s+as\s+\w+/, "")).filter((name) => componentMap[name]);
          for (const componentName of componentNames) {
            const stylePath = componentMap[componentName];
            if (stylePath) {
              const stylesToImport = /* @__PURE__ */ new Set();
              if (resolveStyle) {
                const customStyle = resolveStyle(componentName);
                if (typeof customStyle === "string") {
                  stylesToImport.add(customStyle);
                } else if (Array.isArray(customStyle)) {
                  customStyle.forEach((path) => stylesToImport.add(path));
                } else {
                  continue;
                }
              } else {
                const actualStylePath = stylePath.replace(/\//g, "/");
                stylesToImport.add(`hlxb-ui/${actualStylePath}/style/index.css`);
                const dependencies = componentDependencies[componentName];
                if (dependencies && dependencies.length > 0) {
                  dependencies.forEach((depPath) => {
                    stylesToImport.add(`hlxb-ui/${depPath}/style/index.css`);
                  });
                }
              }
              stylesToImport.forEach((stylePath2) => {
                styleImports.push(`import '${stylePath2}';`);
              });
            }
          }
        }
      }
      if (styleImports.length > 0) {
        const uniqueStyleImports = [...new Set(styleImports)];
        return uniqueStyleImports.join("\n") + "\n" + code;
      }
      return null;
    }
  };
}
export {
  HlxbUIStyleImport,
  HlxbUIStyleImport as default
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { bind, extend, each as each$1, isArray } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import LinearGradient from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/LinearGradient.js";
import { stop } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/event.js";
import VisualMapView from "./VisualMapView.js";
import { applyTransform, getTransform, transformDirection } from "../../util/graphic.js";
import { linearMap as linearMap$1 } from "../../util/number.js";
import sliderMove from "../helper/sliderMove.js";
import { getItemAlign, makeHighDownBatch } from "./helper.js";
import { compressBatches } from "../../util/model.js";
import { parsePercent } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/contain/text.js";
import { setAsHighDownDispatcher } from "../../util/states.js";
import { createSymbol } from "../../util/symbol.js";
import ZRImage from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Image.js";
import { getECData } from "../../util/innerStore.js";
import { createTextStyle } from "../../label/labelStyle.js";
import { findEventDispatcher } from "../../util/event.js";
import ZRText from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
import Group from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
import Rect from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js";
import Polygon from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Polygon.js";
var linearMap = linearMap$1;
var each = each$1;
var mathMin = Math.min;
var mathMax = Math.max;
var HOVER_LINK_SIZE = 12;
var HOVER_LINK_OUT = 6;
var ContinuousView = (
  /** @class */
  function(_super) {
    __extends(ContinuousView2, _super);
    function ContinuousView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ContinuousView2.type;
      _this._shapes = {};
      _this._dataInterval = [];
      _this._handleEnds = [];
      _this._hoverLinkDataIndices = [];
      return _this;
    }
    ContinuousView2.prototype.init = function(ecModel, api) {
      _super.prototype.init.call(this, ecModel, api);
      this._hoverLinkFromSeriesMouseOver = bind(this._hoverLinkFromSeriesMouseOver, this);
      this._hideIndicator = bind(this._hideIndicator, this);
    };
    ContinuousView2.prototype.doRender = function(visualMapModel, ecModel, api, payload) {
      if (!payload || payload.type !== "selectDataRange" || payload.from !== this.uid) {
        this._buildView();
      }
    };
    ContinuousView2.prototype._buildView = function() {
      this.group.removeAll();
      var visualMapModel = this.visualMapModel;
      var thisGroup = this.group;
      this._orient = visualMapModel.get("orient");
      this._useHandle = visualMapModel.get("calculable");
      this._resetInterval();
      this._renderBar(thisGroup);
      var dataRangeText = visualMapModel.get("text");
      this._renderEndsText(thisGroup, dataRangeText, 0);
      this._renderEndsText(thisGroup, dataRangeText, 1);
      this._updateView(true);
      this.renderBackground(thisGroup);
      this._updateView();
      this._enableHoverLinkToSeries();
      this._enableHoverLinkFromSeries();
      this.positionGroup(thisGroup);
    };
    ContinuousView2.prototype._renderEndsText = function(group, dataRangeText, endsIndex) {
      if (!dataRangeText) {
        return;
      }
      var text = dataRangeText[1 - endsIndex];
      text = text != null ? text + "" : "";
      var visualMapModel = this.visualMapModel;
      var textGap = visualMapModel.get("textGap");
      var itemSize = visualMapModel.itemSize;
      var barGroup = this._shapes.mainGroup;
      var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);
      var align = this._applyTransform(endsIndex === 0 ? "bottom" : "top", barGroup);
      var orient = this._orient;
      var textStyleModel = this.visualMapModel.textStyleModel;
      this.group.add(new ZRText({
        style: createTextStyle(textStyleModel, {
          x: position[0],
          y: position[1],
          verticalAlign: textStyleModel.get("verticalAlign") || (orient === "horizontal" ? "middle" : align),
          align: textStyleModel.get("align") || (orient === "horizontal" ? align : "center"),
          text
        })
      }));
    };
    ContinuousView2.prototype._renderBar = function(targetGroup) {
      var visualMapModel = this.visualMapModel;
      var shapes = this._shapes;
      var itemSize = visualMapModel.itemSize;
      var orient = this._orient;
      var useHandle = this._useHandle;
      var itemAlign = getItemAlign(visualMapModel, this.api, itemSize);
      var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);
      var gradientBarGroup = new Group();
      mainGroup.add(gradientBarGroup);
      gradientBarGroup.add(shapes.outOfRange = createPolygon());
      gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, bind(this._dragHandle, this, "all", false), bind(this._dragHandle, this, "all", true)));
      gradientBarGroup.setClipPath(new Rect({
        shape: {
          x: 0,
          y: 0,
          width: itemSize[0],
          height: itemSize[1],
          r: 3
        }
      }));
      var textRect = visualMapModel.textStyleModel.getTextRect("国");
      var textSize = mathMax(textRect.width, textRect.height);
      if (useHandle) {
        shapes.handleThumbs = [];
        shapes.handleLabels = [];
        shapes.handleLabelPoints = [];
        this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);
        this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);
      }
      this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);
      targetGroup.add(mainGroup);
    };
    ContinuousView2.prototype._createHandle = function(visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {
      var onDrift = bind(this._dragHandle, this, handleIndex, false);
      var onDragEnd = bind(this._dragHandle, this, handleIndex, true);
      var handleSize = parsePercent(visualMapModel.get("handleSize"), itemSize[0]);
      var handleThumb = createSymbol(visualMapModel.get("handleIcon"), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);
      var cursor = getCursor(this._orient);
      handleThumb.attr({
        cursor,
        draggable: true,
        drift: onDrift,
        ondragend: onDragEnd,
        onmousemove: function(e) {
          stop(e.event);
        }
      });
      handleThumb.x = itemSize[0] / 2;
      handleThumb.useStyle(visualMapModel.getModel("handleStyle").getItemStyle());
      handleThumb.setStyle({
        strokeNoScale: true,
        strokeFirst: true
      });
      handleThumb.style.lineWidth *= 2;
      handleThumb.ensureState("emphasis").style = visualMapModel.getModel(["emphasis", "handleStyle"]).getItemStyle();
      setAsHighDownDispatcher(handleThumb, true);
      mainGroup.add(handleThumb);
      var textStyleModel = this.visualMapModel.textStyleModel;
      var handleLabel = new ZRText({
        cursor,
        draggable: true,
        drift: onDrift,
        onmousemove: function(e) {
          stop(e.event);
        },
        ondragend: onDragEnd,
        style: createTextStyle(textStyleModel, {
          x: 0,
          y: 0,
          text: ""
        })
      });
      handleLabel.ensureState("blur").style = {
        opacity: 0.1
      };
      handleLabel.stateTransition = {
        duration: 200
      };
      this.group.add(handleLabel);
      var handleLabelPoint = [handleSize, 0];
      var shapes = this._shapes;
      shapes.handleThumbs[handleIndex] = handleThumb;
      shapes.handleLabelPoints[handleIndex] = handleLabelPoint;
      shapes.handleLabels[handleIndex] = handleLabel;
    };
    ContinuousView2.prototype._createIndicator = function(visualMapModel, mainGroup, itemSize, textSize, orient) {
      var scale = parsePercent(visualMapModel.get("indicatorSize"), itemSize[0]);
      var indicator = createSymbol(visualMapModel.get("indicatorIcon"), -scale / 2, -scale / 2, scale, scale, null, true);
      indicator.attr({
        cursor: "move",
        invisible: true,
        silent: true,
        x: itemSize[0] / 2
      });
      var indicatorStyle = visualMapModel.getModel("indicatorStyle").getItemStyle();
      if (indicator instanceof ZRImage) {
        var pathStyle = indicator.style;
        indicator.useStyle(extend({
          // TODO other properties like x, y ?
          image: pathStyle.image,
          x: pathStyle.x,
          y: pathStyle.y,
          width: pathStyle.width,
          height: pathStyle.height
        }, indicatorStyle));
      } else {
        indicator.useStyle(indicatorStyle);
      }
      mainGroup.add(indicator);
      var textStyleModel = this.visualMapModel.textStyleModel;
      var indicatorLabel = new ZRText({
        silent: true,
        invisible: true,
        style: createTextStyle(textStyleModel, {
          x: 0,
          y: 0,
          text: ""
        })
      });
      this.group.add(indicatorLabel);
      var indicatorLabelPoint = [(orient === "horizontal" ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];
      var shapes = this._shapes;
      shapes.indicator = indicator;
      shapes.indicatorLabel = indicatorLabel;
      shapes.indicatorLabelPoint = indicatorLabelPoint;
      this._firstShowIndicator = true;
    };
    ContinuousView2.prototype._dragHandle = function(handleIndex, isEnd, dx, dy) {
      if (!this._useHandle) {
        return;
      }
      this._dragging = !isEnd;
      if (!isEnd) {
        var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);
        this._updateInterval(handleIndex, vertex[1]);
        this._hideIndicator();
        this._updateView();
      }
      if (isEnd === !this.visualMapModel.get("realtime")) {
        this.api.dispatchAction({
          type: "selectDataRange",
          from: this.uid,
          visualMapId: this.visualMapModel.id,
          selected: this._dataInterval.slice()
        });
      }
      if (isEnd) {
        !this._hovering && this._clearHoverLinkToSeries();
      } else if (useHoverLinkOnHandle(this.visualMapModel)) {
        this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);
      }
    };
    ContinuousView2.prototype._resetInterval = function() {
      var visualMapModel = this.visualMapModel;
      var dataInterval = this._dataInterval = visualMapModel.getSelected();
      var dataExtent = visualMapModel.getExtent();
      var sizeExtent = [0, visualMapModel.itemSize[1]];
      this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];
    };
    ContinuousView2.prototype._updateInterval = function(handleIndex, delta) {
      delta = delta || 0;
      var visualMapModel = this.visualMapModel;
      var handleEnds = this._handleEnds;
      var sizeExtent = [0, visualMapModel.itemSize[1]];
      sliderMove(
        delta,
        handleEnds,
        sizeExtent,
        handleIndex,
        // cross is forbidden
        0
      );
      var dataExtent = visualMapModel.getExtent();
      this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];
    };
    ContinuousView2.prototype._updateView = function(forSketch) {
      var visualMapModel = this.visualMapModel;
      var dataExtent = visualMapModel.getExtent();
      var shapes = this._shapes;
      var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];
      var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;
      var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, "inRange");
      var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, "outOfRange");
      shapes.inRange.setStyle({
        fill: visualInRange.barColor
        // opacity: visualInRange.opacity
      }).setShape("points", visualInRange.barPoints);
      shapes.outOfRange.setStyle({
        fill: visualOutOfRange.barColor
        // opacity: visualOutOfRange.opacity
      }).setShape("points", visualOutOfRange.barPoints);
      this._updateHandle(inRangeHandleEnds, visualInRange);
    };
    ContinuousView2.prototype._createBarVisual = function(dataInterval, dataExtent, handleEnds, forceState) {
      var opts = {
        forceState,
        convertOpacityToAlpha: true
      };
      var colorStops = this._makeColorGradient(dataInterval, opts);
      var symbolSizes = [this.getControllerVisual(dataInterval[0], "symbolSize", opts), this.getControllerVisual(dataInterval[1], "symbolSize", opts)];
      var barPoints = this._createBarPoints(handleEnds, symbolSizes);
      return {
        barColor: new LinearGradient(0, 0, 0, 1, colorStops),
        barPoints,
        handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]
      };
    };
    ContinuousView2.prototype._makeColorGradient = function(dataInterval, opts) {
      var sampleNumber = 100;
      var colorStops = [];
      var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;
      colorStops.push({
        color: this.getControllerVisual(dataInterval[0], "color", opts),
        offset: 0
      });
      for (var i = 1; i < sampleNumber; i++) {
        var currValue = dataInterval[0] + step * i;
        if (currValue > dataInterval[1]) {
          break;
        }
        colorStops.push({
          color: this.getControllerVisual(currValue, "color", opts),
          offset: i / sampleNumber
        });
      }
      colorStops.push({
        color: this.getControllerVisual(dataInterval[1], "color", opts),
        offset: 1
      });
      return colorStops;
    };
    ContinuousView2.prototype._createBarPoints = function(handleEnds, symbolSizes) {
      var itemSize = this.visualMapModel.itemSize;
      return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];
    };
    ContinuousView2.prototype._createBarGroup = function(itemAlign) {
      var orient = this._orient;
      var inverse = this.visualMapModel.get("inverse");
      return new Group(orient === "horizontal" && !inverse ? {
        scaleX: itemAlign === "bottom" ? 1 : -1,
        rotation: Math.PI / 2
      } : orient === "horizontal" && inverse ? {
        scaleX: itemAlign === "bottom" ? -1 : 1,
        rotation: -Math.PI / 2
      } : orient === "vertical" && !inverse ? {
        scaleX: itemAlign === "left" ? 1 : -1,
        scaleY: -1
      } : {
        scaleX: itemAlign === "left" ? 1 : -1
      });
    };
    ContinuousView2.prototype._updateHandle = function(handleEnds, visualInRange) {
      if (!this._useHandle) {
        return;
      }
      var shapes = this._shapes;
      var visualMapModel = this.visualMapModel;
      var handleThumbs = shapes.handleThumbs;
      var handleLabels = shapes.handleLabels;
      var itemSize = visualMapModel.itemSize;
      var dataExtent = visualMapModel.getExtent();
      var align = this._applyTransform("left", shapes.mainGroup);
      each([0, 1], function(handleIndex) {
        var handleThumb = handleThumbs[handleIndex];
        handleThumb.setStyle("fill", visualInRange.handlesColor[handleIndex]);
        handleThumb.y = handleEnds[handleIndex];
        var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);
        var symbolSize = this.getControllerVisual(val, "symbolSize");
        handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];
        handleThumb.x = itemSize[0] - symbolSize / 2;
        var textPoint = applyTransform(shapes.handleLabelPoints[handleIndex], getTransform(handleThumb, this.group));
        if (this._orient === "horizontal") {
          var minimumOffset = align === "left" || align === "top" ? (itemSize[0] - symbolSize) / 2 : (itemSize[0] - symbolSize) / -2;
          textPoint[1] += minimumOffset;
        }
        handleLabels[handleIndex].setStyle({
          x: textPoint[0],
          y: textPoint[1],
          text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),
          verticalAlign: "middle",
          align: this._orient === "vertical" ? this._applyTransform("left", shapes.mainGroup) : "center"
        });
      }, this);
    };
    ContinuousView2.prototype._showIndicator = function(cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {
      var visualMapModel = this.visualMapModel;
      var dataExtent = visualMapModel.getExtent();
      var itemSize = visualMapModel.itemSize;
      var sizeExtent = [0, itemSize[1]];
      var shapes = this._shapes;
      var indicator = shapes.indicator;
      if (!indicator) {
        return;
      }
      indicator.attr("invisible", false);
      var opts = {
        convertOpacityToAlpha: true
      };
      var color = this.getControllerVisual(cursorValue, "color", opts);
      var symbolSize = this.getControllerVisual(cursorValue, "symbolSize");
      var y = linearMap(cursorValue, dataExtent, sizeExtent, true);
      var x = itemSize[0] - symbolSize / 2;
      var oldIndicatorPos = {
        x: indicator.x,
        y: indicator.y
      };
      indicator.y = y;
      indicator.x = x;
      var textPoint = applyTransform(shapes.indicatorLabelPoint, getTransform(indicator, this.group));
      var indicatorLabel = shapes.indicatorLabel;
      indicatorLabel.attr("invisible", false);
      var align = this._applyTransform("left", shapes.mainGroup);
      var orient = this._orient;
      var isHorizontal = orient === "horizontal";
      indicatorLabel.setStyle({
        text: (rangeSymbol ? rangeSymbol : "") + visualMapModel.formatValueText(textValue),
        verticalAlign: isHorizontal ? align : "middle",
        align: isHorizontal ? "center" : align
      });
      var indicatorNewProps = {
        x,
        y,
        style: {
          fill: color
        }
      };
      var labelNewProps = {
        style: {
          x: textPoint[0],
          y: textPoint[1]
        }
      };
      if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {
        var animationCfg = {
          duration: 100,
          easing: "cubicInOut",
          additive: true
        };
        indicator.x = oldIndicatorPos.x;
        indicator.y = oldIndicatorPos.y;
        indicator.animateTo(indicatorNewProps, animationCfg);
        indicatorLabel.animateTo(labelNewProps, animationCfg);
      } else {
        indicator.attr(indicatorNewProps);
        indicatorLabel.attr(labelNewProps);
      }
      this._firstShowIndicator = false;
      var handleLabels = this._shapes.handleLabels;
      if (handleLabels) {
        for (var i = 0; i < handleLabels.length; i++) {
          this.api.enterBlur(handleLabels[i]);
        }
      }
    };
    ContinuousView2.prototype._enableHoverLinkToSeries = function() {
      var self = this;
      this._shapes.mainGroup.on("mousemove", function(e) {
        self._hovering = true;
        if (!self._dragging) {
          var itemSize = self.visualMapModel.itemSize;
          var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true);
          pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);
          self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);
        }
      }).on("mouseout", function() {
        self._hovering = false;
        !self._dragging && self._clearHoverLinkToSeries();
      });
    };
    ContinuousView2.prototype._enableHoverLinkFromSeries = function() {
      var zr = this.api.getZr();
      if (this.visualMapModel.option.hoverLink) {
        zr.on("mouseover", this._hoverLinkFromSeriesMouseOver, this);
        zr.on("mouseout", this._hideIndicator, this);
      } else {
        this._clearHoverLinkFromSeries();
      }
    };
    ContinuousView2.prototype._doHoverLinkToSeries = function(cursorPos, hoverOnBar) {
      var visualMapModel = this.visualMapModel;
      var itemSize = visualMapModel.itemSize;
      if (!visualMapModel.option.hoverLink) {
        return;
      }
      var sizeExtent = [0, itemSize[1]];
      var dataExtent = visualMapModel.getExtent();
      cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);
      var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);
      var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];
      var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);
      var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)];
      hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);
      hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity);
      if (hoverOnBar) {
        if (valueRange[0] === -Infinity) {
          this._showIndicator(cursorValue, valueRange[1], "< ", halfHoverLinkSize);
        } else if (valueRange[1] === Infinity) {
          this._showIndicator(cursorValue, valueRange[0], "> ", halfHoverLinkSize);
        } else {
          this._showIndicator(cursorValue, cursorValue, "≈ ", halfHoverLinkSize);
        }
      }
      var oldBatch = this._hoverLinkDataIndices;
      var newBatch = [];
      if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {
        newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);
      }
      var resultBatches = compressBatches(oldBatch, newBatch);
      this._dispatchHighDown("downplay", makeHighDownBatch(resultBatches[0], visualMapModel));
      this._dispatchHighDown("highlight", makeHighDownBatch(resultBatches[1], visualMapModel));
    };
    ContinuousView2.prototype._hoverLinkFromSeriesMouseOver = function(e) {
      var ecData;
      findEventDispatcher(e.target, function(target) {
        var currECData = getECData(target);
        if (currECData.dataIndex != null) {
          ecData = currECData;
          return true;
        }
      }, true);
      if (!ecData) {
        return;
      }
      var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);
      var visualMapModel = this.visualMapModel;
      if (!visualMapModel.isTargetSeries(dataModel)) {
        return;
      }
      var data = dataModel.getData(ecData.dataType);
      var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);
      if (!isNaN(value)) {
        this._showIndicator(value, value);
      }
    };
    ContinuousView2.prototype._hideIndicator = function() {
      var shapes = this._shapes;
      shapes.indicator && shapes.indicator.attr("invisible", true);
      shapes.indicatorLabel && shapes.indicatorLabel.attr("invisible", true);
      var handleLabels = this._shapes.handleLabels;
      if (handleLabels) {
        for (var i = 0; i < handleLabels.length; i++) {
          this.api.leaveBlur(handleLabels[i]);
        }
      }
    };
    ContinuousView2.prototype._clearHoverLinkToSeries = function() {
      this._hideIndicator();
      var indices = this._hoverLinkDataIndices;
      this._dispatchHighDown("downplay", makeHighDownBatch(indices, this.visualMapModel));
      indices.length = 0;
    };
    ContinuousView2.prototype._clearHoverLinkFromSeries = function() {
      this._hideIndicator();
      var zr = this.api.getZr();
      zr.off("mouseover", this._hoverLinkFromSeriesMouseOver);
      zr.off("mouseout", this._hideIndicator);
    };
    ContinuousView2.prototype._applyTransform = function(vertex, element, inverse, global) {
      var transform = getTransform(element, global ? null : this.group);
      return isArray(vertex) ? applyTransform(vertex, transform, inverse) : transformDirection(vertex, transform, inverse);
    };
    ContinuousView2.prototype._dispatchHighDown = function(type, batch) {
      batch && batch.length && this.api.dispatchAction({
        type,
        batch
      });
    };
    ContinuousView2.prototype.dispose = function() {
      this._clearHoverLinkFromSeries();
      this._clearHoverLinkToSeries();
    };
    ContinuousView2.type = "visualMap.continuous";
    return ContinuousView2;
  }(VisualMapView)
);
function createPolygon(points, cursor, onDrift, onDragEnd) {
  return new Polygon({
    shape: {
      points
    },
    draggable: !!onDrift,
    cursor,
    drift: onDrift,
    onmousemove: function(e) {
      stop(e.event);
    },
    ondragend: onDragEnd
  });
}
function getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {
  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;
  var hoverLinkDataSize = visualMapModel.get("hoverLinkDataSize");
  if (hoverLinkDataSize) {
    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;
  }
  return halfHoverLinkSize;
}
function useHoverLinkOnHandle(visualMapModel) {
  var hoverLinkOnHandle = visualMapModel.get("hoverLinkOnHandle");
  return !!(hoverLinkOnHandle == null ? visualMapModel.get("realtime") : hoverLinkOnHandle);
}
function getCursor(orient) {
  return orient === "vertical" ? "ns-resize" : "ew-resize";
}
export {
  ContinuousView as default
};

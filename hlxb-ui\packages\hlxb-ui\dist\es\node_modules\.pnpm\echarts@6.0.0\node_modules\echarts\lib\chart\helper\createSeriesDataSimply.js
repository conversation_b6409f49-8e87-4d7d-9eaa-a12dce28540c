import prepareSeriesDataSchema from "../../data/helper/createDimensions.js";
import SeriesData from "../../data/SeriesData.js";
import { isArray, extend } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function createSeriesDataSimply(seriesModel, opt, nameList) {
  opt = isArray(opt) && {
    coordDimensions: opt
  } || extend({
    encodeDefine: seriesModel.getEncode()
  }, opt);
  var source = seriesModel.getSource();
  var dimensions = prepareSeriesDataSchema(source, opt).dimensions;
  var list = new SeriesData(dimensions, seriesModel);
  list.initData(source, nameList);
  return list;
}
export {
  createSeriesDataSimply as default
};

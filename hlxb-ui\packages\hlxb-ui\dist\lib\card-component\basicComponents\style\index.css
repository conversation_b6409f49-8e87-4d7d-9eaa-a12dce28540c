.hlxb-ranking {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-ranking.Dark {
  color: #fff;
}
.hlxb-ranking.light {
  color: #333;
}
.hlxb-ranking.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-ranking .item-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}
.hlxb-ranking .item-list .activeItem {
  position: relative;
  border-radius: 4px;
  border: 1px solid var(--theme-color-88p) !important;
}
.hlxb-ranking .item-list .activeItem .img_box {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 0;
  bottom: 0;
}
.hlxb-ranking .item-list .hoverItem {
  cursor: pointer;
}
.hlxb-ranking .item-list .hoverItem:hover {
  border: 1px solid var(--theme-color-88p);
}
.hlxb-ranking .item-list .item {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #fff;
}
.hlxb-ranking .item-list .item .top-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.hlxb-ranking .item-list .item .bar-box {
  width: 100%;
  margin-top: 12px;
  height: 12px;
  background: #eeeff1;
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .bar-box .bar-conter {
  height: 12px;
  background: #999;
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .bar-box .abar-conter {
  height: 12px;
  background: var(--theme-color-88p);
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .abar-box {
  background: rgba(11, 98, 203, 0.16);
}
.hlxb-ranking .item-list .item .item-index {
  flex: 1;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #333;
}
.hlxb-ranking .item-list .item .item-index .num-index {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
}
.hlxb-ranking .item-list .item .item-index .label {
  padding-left: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  width: 120px;
  white-space: nowrap;
  /* 让文本不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号代替超出部分 */
}
.hlxb-ranking .item-list .item .item-content {
  flex: 1;
  display: flex;
  align-items: baseline;
  justify-content: end;
}
.hlxb-ranking .item-list .item .item-content .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
}
.hlxb-ranking .item-list .item .item-content .unit {
  font-weight: 400;
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}
.hlxb-ranking .item-list .item .num-index {
  color: #fff;
  background: url('../../assets/images/item_fo.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(1) .num-index {
  background: url('../../assets/images/item_o.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(2) .num-index {
  color: #fff;
  background: url('../../assets/images/item_tw.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(3) .num-index {
  color: #fff;
  background: url('../../assets/images/item_th.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking-simple {
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}
.hlxb-ranking-simple.Dark {
  color: #fff;
}
.hlxb-ranking-simple.light {
  color: #333;
}
.hlxb-ranking-simple.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-ranking-simple .item-t {
  background: rgba(11, 98, 203, 0.08);
}
.hlxb-ranking-simple .item-t .num-index {
  color: #fff;
  background: var(--theme-color);
}
.hlxb-ranking-simple .empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999;
}
.hlxb-ranking-simple .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 16px;
  border-radius: 4px;
  background: #f2f3f5;
  color: #fff;
}
.hlxb-ranking-simple .item .item-index {
  flex: 1;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #333;
}
.hlxb-ranking-simple .item .item-index .num-index {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: #999;
  color: #fff;
  border-radius: 10px;
}
.hlxb-ranking-simple .item .item-index .label {
  font-weight: 400;
  font-size: 14px;
  width: 120px;
  white-space: nowrap;
  /* 让文本不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号代替超出部分 */
}
.hlxb-ranking-simple .item .item-content {
  flex: 1;
  display: flex;
  align-items: baseline;
  justify-content: end;
}
.hlxb-ranking-simple .item .item-content .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
}
.hlxb-ranking-simple .item .item-content .unit {
  font-weight: 400;
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}
.hlxb-ranking-simple .item:nth-child(1),
.hlxb-ranking-simple .item:nth-child(2),
.hlxb-ranking-simple .item:nth-child(3) {
  background: rgba(11, 98, 203, 0.08);
}
.hlxb-ranking-simple .item:nth-child(1) .num-index,
.hlxb-ranking-simple .item:nth-child(2) .num-index,
.hlxb-ranking-simple .item:nth-child(3) .num-index {
  color: #fff;
  background: var(--theme-color);
}
.hlxb-energy-summary {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-energy-summary.Dark {
  color: #fff;
}
.hlxb-energy-summary.light {
  color: #333;
}
.hlxb-energy-summary.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-energy-summary .empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hlxb-energy-summary .container-list {
  display: flex;
  flex-direction: column;
  gap: 16px 0;
  height: 100%;
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item {
  padding: 0 12px 0 0;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  height: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hlxb-energy-summary .container-list .list-item .item {
  width: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
}
.hlxb-energy-summary .container-list .list-item .item .conten-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: self-start;
  overflow: hidden;
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item .item .name {
  width: 100%;
  display: flex;
  padding-left: 24px;
  justify-content: flex-start;
  align-items: center;
}
.hlxb-energy-summary .container-list .list-item .item .name .icon {
  width: 24px;
  height: 24px;
}
.hlxb-energy-summary .container-list .list-item .item .name .icon img {
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item .item .name .text {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-energy-summary .container-list .list-item .item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  width: 100%;
  justify-content: start;
  padding-left: 24px;
  overflow: hidden;
  align-items: end;
}
.hlxb-energy-summary .container-list .list-item .item .value .number-value {
  font-family: 'D-DIN-PRO, D-DIN-PRO';
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-energy-summary .container-list .list-item .item .value .unitLevel {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-bottom: 3px;
}
.hlxb-energy-summary .container-list .list-item .item .value .unit {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-energy-summary .container-list .list-item .item .value .ups-icon {
  display: inline-block;
  margin-left: 6px;
}
.hlxb-energy-summary .container-list .list-item .item .value-before {
  margin: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .conten-box {
  padding-left: 24px;
  padding-right: 24px;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .name {
  padding-left: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .value {
  padding-left: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(2) .value,
.hlxb-energy-summary .container-list .list-item .item:nth-child(3) .value {
  padding-left: 24px;
}
.hlxb-energy-summary .container-list .list-item .item-after::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 34px;
  background-color: #bebebe;
}
.hlxb-drug-summary {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-drug-summary.Dark {
  color: #fff;
}
.hlxb-drug-summary.light {
  color: #333;
}
.hlxb-drug-summary.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-drug-summary .empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hlxb-drug-summary .container-list {
  display: flex;
  flex-direction: column;
  gap: 16px 0;
  height: 100%;
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item {
  padding: 0 12px 0 0;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  height: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hlxb-drug-summary .container-list .list-item .item {
  width: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
}
.hlxb-drug-summary .container-list .list-item .item .conten-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: self-start;
  overflow: hidden;
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item .item .name {
  width: 100%;
  display: flex;
  padding-left: 24px;
  justify-content: flex-start;
  align-items: center;
}
.hlxb-drug-summary .container-list .list-item .item .name .icon {
  width: 24px;
  height: 24px;
}
.hlxb-drug-summary .container-list .list-item .item .name .icon img {
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item .item .name .text {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-drug-summary .container-list .list-item .item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  width: 100%;
  justify-content: start;
  padding-left: 24px;
  overflow: hidden;
  align-items: end;
}
.hlxb-drug-summary .container-list .list-item .item .value .number-value {
  font-family: 'D-DIN-PRO, D-DIN-PRO';
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-drug-summary .container-list .list-item .item .value .unitLevel {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-bottom: 3px;
}
.hlxb-drug-summary .container-list .list-item .item .value .unit {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-drug-summary .container-list .list-item .item .value .ups-icon {
  display: inline-block;
  margin-left: 6px;
}
.hlxb-drug-summary .container-list .list-item .item .value-before {
  margin: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .conten-box {
  padding-left: 24px;
  padding-right: 24px;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .name {
  padding-left: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .value {
  padding-left: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(2) .value,
.hlxb-drug-summary .container-list .list-item .item:nth-child(3) .value {
  padding-left: 24px;
}
.hlxb-drug-summary .container-list .list-item .item-after::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 34px;
  background-color: #bebebe;
}
.hlxb-horizonta-small-square {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-horizonta-small-square.Dark {
  color: #fff;
}
.hlxb-horizonta-small-square.light {
  color: #333;
}
.hlxb-horizonta-small-square.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-horizonta-small-square .extra-wrap :deep(.ant-select) {
  margin-right: 16px;
}
.hlxb-horizonta-small-square .total-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 17px;
  overflow-x: auto;
}
.hlxb-horizonta-small-square .total-list .item {
  height: 100%;
  flex: 1;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}
.hlxb-horizonta-small-square .total-list .item .icon {
  width: 40px;
  height: 40px;
}
.hlxb-horizonta-small-square .total-list .item .content-column {
  flex-direction: column;
}
.hlxb-horizonta-small-square .total-list .item .content {
  display: flex;
  flex: 1;
  padding: 16px;
  justify-content: space-between;
  align-items: start;
  gap: 8px 0;
}
.hlxb-horizonta-small-square .total-list .item .content .name {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  line-height: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_0::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(46, 196, 255, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_1::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(67, 92, 255, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_2::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(34, 205, 128, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_3::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(255, 140, 46, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_4::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(237, 210, 38, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_5::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(255, 82, 43, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .value_pleft {
  padding-left: 14px;
}
.hlxb-horizonta-small-square .total-list .item .content .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: flex-end;
  line-height: 1;
}
.hlxb-horizonta-small-square .total-list .item .content .value .number-value {
  font-family: D-DIN-PRO;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-horizonta-small-square .total-list .item .content .value .unit {
  margin-left: 8px;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-horizonta-small-square .empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999;
}
.hlxb-card-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hlxb-card-loading.Dark {
  color: #fff;
}
.hlxb-card-loading.light {
  color: #333;
}
.hlxb-card-loading.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-card-empty {
  color: #999;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hlxb-card-empty .text-center {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}
.hlxb-card-empty.Dark {
  color: #fff;
}
.hlxb-card-empty.light {
  color: #333;
}
.hlxb-card-empty.screenColor {
  color: #fff;
  background: transparent;
}

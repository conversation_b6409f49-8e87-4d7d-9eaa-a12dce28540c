"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const extension = require("../../extension.js");
const MapView = require("./MapView.js");
const MapSeries = require("./MapSeries.js");
const mapDataStatistic = require("./mapDataStatistic.js");
const mapSymbolLayout = require("./mapSymbolLayout.js");
const dataSelectAction = require("../../legacy/dataSelectAction.js");
const install$1 = require("../../component/geo/install.js");
function install(registers) {
  extension.use(install$1.install);
  registers.registerChartView(MapView.default);
  registers.registerSeriesModel(MapSeries.default);
  registers.registerLayout(mapSymbolLayout.default);
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, mapDataStatistic.default);
  dataSelectAction.createLegacyDataSelectAction("map", registers.registerAction);
}
exports.install = install;

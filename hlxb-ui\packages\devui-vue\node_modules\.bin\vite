#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules/vite/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules/vite/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules/vite/bin/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules/vite/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/vite@2.9.18_less@4.4.1_sass@1.91.0/node_modules:/mnt/f/work/code/test/zujian/hlxb-ui-twelve/hlxb-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi

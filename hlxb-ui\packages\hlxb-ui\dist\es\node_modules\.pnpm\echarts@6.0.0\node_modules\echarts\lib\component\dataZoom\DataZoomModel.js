import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { merge, each, createHashMap, assert } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import ComponentModel from "../../model/Component.js";
import { getAxisMainType, DATA_ZOOM_AXIS_DIMENSIONS } from "./helper.js";
import { MULTIPLE_REFERRING, SINGLE_REFERRING } from "../../util/model.js";
var DataZoomAxisInfo = (
  /** @class */
  function() {
    function DataZoomAxisInfo2() {
      this.indexList = [];
      this.indexMap = [];
    }
    DataZoomAxisInfo2.prototype.add = function(axisCmptIdx) {
      if (!this.indexMap[axisCmptIdx]) {
        this.indexList.push(axisCmptIdx);
        this.indexMap[axisCmptIdx] = true;
      }
    };
    return DataZoomAxisInfo2;
  }()
);
var DataZoomModel = (
  /** @class */
  function(_super) {
    __extends(DataZoomModel2, _super);
    function DataZoomModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = DataZoomModel2.type;
      _this._autoThrottle = true;
      _this._noTarget = true;
      _this._rangePropMode = ["percent", "percent"];
      return _this;
    }
    DataZoomModel2.prototype.init = function(option, parentModel, ecModel) {
      var inputRawOption = retrieveRawOption(option);
      this.settledOption = inputRawOption;
      this.mergeDefaultAndTheme(option, ecModel);
      this._doInit(inputRawOption);
    };
    DataZoomModel2.prototype.mergeOption = function(newOption) {
      var inputRawOption = retrieveRawOption(newOption);
      merge(this.option, newOption, true);
      merge(this.settledOption, inputRawOption, true);
      this._doInit(inputRawOption);
    };
    DataZoomModel2.prototype._doInit = function(inputRawOption) {
      var thisOption = this.option;
      this._setDefaultThrottle(inputRawOption);
      this._updateRangeUse(inputRawOption);
      var settledOption = this.settledOption;
      each([["start", "startValue"], ["end", "endValue"]], function(names, index) {
        if (this._rangePropMode[index] === "value") {
          thisOption[names[0]] = settledOption[names[0]] = null;
        }
      }, this);
      this._resetTarget();
    };
    DataZoomModel2.prototype._resetTarget = function() {
      var optionOrient = this.get("orient", true);
      var targetAxisIndexMap = this._targetAxisInfoMap = createHashMap();
      var hasAxisSpecified = this._fillSpecifiedTargetAxis(targetAxisIndexMap);
      if (hasAxisSpecified) {
        this._orient = optionOrient || this._makeAutoOrientByTargetAxis();
      } else {
        this._orient = optionOrient || "horizontal";
        this._fillAutoTargetAxisByOrient(targetAxisIndexMap, this._orient);
      }
      this._noTarget = true;
      targetAxisIndexMap.each(function(axisInfo) {
        if (axisInfo.indexList.length) {
          this._noTarget = false;
        }
      }, this);
    };
    DataZoomModel2.prototype._fillSpecifiedTargetAxis = function(targetAxisIndexMap) {
      var hasAxisSpecified = false;
      each(DATA_ZOOM_AXIS_DIMENSIONS, function(axisDim) {
        var refering = this.getReferringComponents(getAxisMainType(axisDim), MULTIPLE_REFERRING);
        if (!refering.specified) {
          return;
        }
        hasAxisSpecified = true;
        var axisInfo = new DataZoomAxisInfo();
        each(refering.models, function(axisModel) {
          axisInfo.add(axisModel.componentIndex);
        });
        targetAxisIndexMap.set(axisDim, axisInfo);
      }, this);
      return hasAxisSpecified;
    };
    DataZoomModel2.prototype._fillAutoTargetAxisByOrient = function(targetAxisIndexMap, orient) {
      var ecModel = this.ecModel;
      var needAuto = true;
      if (needAuto) {
        var axisDim = orient === "vertical" ? "y" : "x";
        var axisModels = ecModel.findComponents({
          mainType: axisDim + "Axis"
        });
        setParallelAxis(axisModels, axisDim);
      }
      if (needAuto) {
        var axisModels = ecModel.findComponents({
          mainType: "singleAxis",
          filter: function(axisModel) {
            return axisModel.get("orient", true) === orient;
          }
        });
        setParallelAxis(axisModels, "single");
      }
      function setParallelAxis(axisModels2, axisDim2) {
        var axisModel = axisModels2[0];
        if (!axisModel) {
          return;
        }
        var axisInfo = new DataZoomAxisInfo();
        axisInfo.add(axisModel.componentIndex);
        targetAxisIndexMap.set(axisDim2, axisInfo);
        needAuto = false;
        if (axisDim2 === "x" || axisDim2 === "y") {
          var gridModel_1 = axisModel.getReferringComponents("grid", SINGLE_REFERRING).models[0];
          gridModel_1 && each(axisModels2, function(axModel) {
            if (axisModel.componentIndex !== axModel.componentIndex && gridModel_1 === axModel.getReferringComponents("grid", SINGLE_REFERRING).models[0]) {
              axisInfo.add(axModel.componentIndex);
            }
          });
        }
      }
      if (needAuto) {
        each(DATA_ZOOM_AXIS_DIMENSIONS, function(axisDim2) {
          if (!needAuto) {
            return;
          }
          var axisModels2 = ecModel.findComponents({
            mainType: getAxisMainType(axisDim2),
            filter: function(axisModel) {
              return axisModel.get("type", true) === "category";
            }
          });
          if (axisModels2[0]) {
            var axisInfo = new DataZoomAxisInfo();
            axisInfo.add(axisModels2[0].componentIndex);
            targetAxisIndexMap.set(axisDim2, axisInfo);
            needAuto = false;
          }
        }, this);
      }
    };
    DataZoomModel2.prototype._makeAutoOrientByTargetAxis = function() {
      var dim;
      this.eachTargetAxis(function(axisDim) {
        !dim && (dim = axisDim);
      }, this);
      return dim === "y" ? "vertical" : "horizontal";
    };
    DataZoomModel2.prototype._setDefaultThrottle = function(inputRawOption) {
      if (inputRawOption.hasOwnProperty("throttle")) {
        this._autoThrottle = false;
      }
      if (this._autoThrottle) {
        var globalOption = this.ecModel.option;
        this.option.throttle = globalOption.animation && globalOption.animationDurationUpdate > 0 ? 100 : 20;
      }
    };
    DataZoomModel2.prototype._updateRangeUse = function(inputRawOption) {
      var rangePropMode = this._rangePropMode;
      var rangeModeInOption = this.get("rangeMode");
      each([["start", "startValue"], ["end", "endValue"]], function(names, index) {
        var percentSpecified = inputRawOption[names[0]] != null;
        var valueSpecified = inputRawOption[names[1]] != null;
        if (percentSpecified && !valueSpecified) {
          rangePropMode[index] = "percent";
        } else if (!percentSpecified && valueSpecified) {
          rangePropMode[index] = "value";
        } else if (rangeModeInOption) {
          rangePropMode[index] = rangeModeInOption[index];
        } else if (percentSpecified) {
          rangePropMode[index] = "percent";
        }
      });
    };
    DataZoomModel2.prototype.noTarget = function() {
      return this._noTarget;
    };
    DataZoomModel2.prototype.getFirstTargetAxisModel = function() {
      var firstAxisModel;
      this.eachTargetAxis(function(axisDim, axisIndex) {
        if (firstAxisModel == null) {
          firstAxisModel = this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);
        }
      }, this);
      return firstAxisModel;
    };
    DataZoomModel2.prototype.eachTargetAxis = function(callback, context) {
      this._targetAxisInfoMap.each(function(axisInfo, axisDim) {
        each(axisInfo.indexList, function(axisIndex) {
          callback.call(context, axisDim, axisIndex);
        });
      });
    };
    DataZoomModel2.prototype.getAxisProxy = function(axisDim, axisIndex) {
      var axisModel = this.getAxisModel(axisDim, axisIndex);
      if (axisModel) {
        return axisModel.__dzAxisProxy;
      }
    };
    DataZoomModel2.prototype.getAxisModel = function(axisDim, axisIndex) {
      if (process.env.NODE_ENV !== "production") {
        assert(axisDim && axisIndex != null);
      }
      var axisInfo = this._targetAxisInfoMap.get(axisDim);
      if (axisInfo && axisInfo.indexMap[axisIndex]) {
        return this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);
      }
    };
    DataZoomModel2.prototype.setRawRange = function(opt) {
      var thisOption = this.option;
      var settledOption = this.settledOption;
      each([["start", "startValue"], ["end", "endValue"]], function(names) {
        if (opt[names[0]] != null || opt[names[1]] != null) {
          thisOption[names[0]] = settledOption[names[0]] = opt[names[0]];
          thisOption[names[1]] = settledOption[names[1]] = opt[names[1]];
        }
      }, this);
      this._updateRangeUse(opt);
    };
    DataZoomModel2.prototype.setCalculatedRange = function(opt) {
      var option = this.option;
      each(["start", "startValue", "end", "endValue"], function(name) {
        option[name] = opt[name];
      });
    };
    DataZoomModel2.prototype.getPercentRange = function() {
      var axisProxy = this.findRepresentativeAxisProxy();
      if (axisProxy) {
        return axisProxy.getDataPercentWindow();
      }
    };
    DataZoomModel2.prototype.getValueRange = function(axisDim, axisIndex) {
      if (axisDim == null && axisIndex == null) {
        var axisProxy = this.findRepresentativeAxisProxy();
        if (axisProxy) {
          return axisProxy.getDataValueWindow();
        }
      } else {
        return this.getAxisProxy(axisDim, axisIndex).getDataValueWindow();
      }
    };
    DataZoomModel2.prototype.findRepresentativeAxisProxy = function(axisModel) {
      if (axisModel) {
        return axisModel.__dzAxisProxy;
      }
      var firstProxy;
      var axisDimList = this._targetAxisInfoMap.keys();
      for (var i = 0; i < axisDimList.length; i++) {
        var axisDim = axisDimList[i];
        var axisInfo = this._targetAxisInfoMap.get(axisDim);
        for (var j = 0; j < axisInfo.indexList.length; j++) {
          var proxy = this.getAxisProxy(axisDim, axisInfo.indexList[j]);
          if (proxy.hostedBy(this)) {
            return proxy;
          }
          if (!firstProxy) {
            firstProxy = proxy;
          }
        }
      }
      return firstProxy;
    };
    DataZoomModel2.prototype.getRangePropMode = function() {
      return this._rangePropMode.slice();
    };
    DataZoomModel2.prototype.getOrient = function() {
      if (process.env.NODE_ENV !== "production") {
        assert(this._orient);
      }
      return this._orient;
    };
    DataZoomModel2.type = "dataZoom";
    DataZoomModel2.dependencies = ["xAxis", "yAxis", "radiusAxis", "angleAxis", "singleAxis", "series", "toolbox"];
    DataZoomModel2.defaultOption = {
      // zlevel: 0,
      z: 4,
      filterMode: "filter",
      start: 0,
      end: 100
    };
    return DataZoomModel2;
  }(ComponentModel)
);
function retrieveRawOption(option) {
  var ret = {};
  each(["start", "end", "startValue", "endValue", "throttle"], function(name) {
    option.hasOwnProperty(name) && (ret[name] = option[name]);
  });
  return ret;
}
export {
  DataZoomModel as default
};

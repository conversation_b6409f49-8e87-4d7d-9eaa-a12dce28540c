import { defineComponent, ref, watch, createElementBlock, openBlock, normalizeClass, unref, createCommentVNode, Fragment, renderList, createElementVNode, toDisplayString, createBlock, withCtx, createTextVNode, renderSlot } from "vue";
import { Tooltip } from "ant-design-vue";
import up from "../assets/images/up.svg.js";
import down from "../assets/images/down.svg.js";
import { getPrefixCls } from "../../../config/index.js";
const _hoisted_1 = {
  key: 0,
  class: "container-list"
};
const _hoisted_2 = { class: "conten-box" };
const _hoisted_3 = { class: "name" };
const _hoisted_4 = { class: "text" };
const _hoisted_5 = {
  key: 0,
  class: "value value-before"
};
const _hoisted_6 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_7 = { class: "number-value" };
const _hoisted_8 = {
  key: 2,
  class: "unit"
};
const _hoisted_9 = { class: "item" };
const _hoisted_10 = { class: "conten-box" };
const _hoisted_11 = { class: "name" };
const _hoisted_12 = { class: "text" };
const _hoisted_13 = { class: "value" };
const _hoisted_14 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_15 = { class: "number-value" };
const _hoisted_16 = {
  key: 2,
  class: "unit"
};
const _hoisted_17 = { class: "item" };
const _hoisted_18 = { class: "conten-box" };
const _hoisted_19 = { class: "name" };
const _hoisted_20 = { class: "text" };
const _hoisted_21 = { class: "value" };
const _hoisted_22 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_23 = { class: "number-value" };
const _hoisted_24 = {
  key: 2,
  class: "unit"
};
const _hoisted_25 = {
  key: 3,
  class: "ups-icon"
};
const _hoisted_26 = ["src"];
const _hoisted_27 = ["src"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbDrugSummary"
  },
  __name: "DrugSummary",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    // 小图标是否展示
    elect: {
      type: Boolean,
      default: false
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("drug-summary");
    const props = __props;
    const loading = ref(false);
    function showTooltip(e) {
      if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none";
      }
    }
    watch(
      () => props.dataList,
      (newVal) => {
        if (!newVal) {
          loading.value = true;
        } else {
          loading.value = false;
        }
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.themeColor])
      }, [
        __props.dataList.length ? (openBlock(), createElementBlock("div", _hoisted_1, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(__props.dataList, (item, index) => {
            return openBlock(), createElementBlock("div", {
              class: "list-item",
              key: index
            }, [
              createElementVNode("div", {
                class: normalizeClass(["item", item.value || item.unitName ? "item-after" : ""])
              }, [
                createElementVNode("div", _hoisted_2, [
                  createElementVNode("div", _hoisted_3, [
                    createElementVNode("div", _hoisted_4, toDisplayString(item.indexName), 1)
                  ]),
                  item.value || item.unitName ? (openBlock(), createElementBlock("div", _hoisted_5, [
                    item.value === null ? (openBlock(), createElementBlock("div", _hoisted_6, "-")) : (openBlock(), createBlock(unref(Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: withCtx(() => [
                        createTextVNode(toDisplayString(Number(item.value) ? Number(item.value) : "-") + toDisplayString(item.unitName), 1)
                      ]),
                      default: withCtx(() => [
                        createElementVNode("div", _hoisted_7, toDisplayString(Number(item.value) ? Number(item.value) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.value !== null ? (openBlock(), createElementBlock("span", _hoisted_8, toDisplayString(item.unitName), 1)) : createCommentVNode("", true)
                  ])) : createCommentVNode("", true)
                ])
              ], 2),
              createElementVNode("div", _hoisted_9, [
                createElementVNode("div", _hoisted_10, [
                  createElementVNode("div", _hoisted_11, [
                    renderSlot(_ctx.$slots, "summaryIcon"),
                    createElementVNode("div", _hoisted_12, toDisplayString(item.indexPreName), 1)
                  ]),
                  createElementVNode("div", _hoisted_13, [
                    item.preVal === null ? (openBlock(), createElementBlock("div", _hoisted_14, "-")) : (openBlock(), createBlock(unref(Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: withCtx(() => [
                        createTextVNode(toDisplayString(Number(item.preVal) ? Number(item.preVal) : "-") + toDisplayString(item.unitName), 1)
                      ]),
                      default: withCtx(() => [
                        createElementVNode("div", _hoisted_15, toDisplayString(Number(item.preVal) ? Number(item.preVal) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.preVal !== null ? (openBlock(), createElementBlock("span", _hoisted_16, toDisplayString(item.unitName), 1)) : createCommentVNode("", true)
                  ])
                ])
              ]),
              createElementVNode("div", _hoisted_17, [
                createElementVNode("div", _hoisted_18, [
                  createElementVNode("div", _hoisted_19, [
                    renderSlot(_ctx.$slots, "summaryIconTwo"),
                    createElementVNode("div", _hoisted_20, toDisplayString(item.ratioName), 1)
                  ]),
                  createElementVNode("div", _hoisted_21, [
                    item.ratioVal === null ? (openBlock(), createElementBlock("div", _hoisted_22, "-")) : (openBlock(), createBlock(unref(Tooltip), {
                      key: 1,
                      onMouseenter: showTooltip
                    }, {
                      title: withCtx(() => [
                        createTextVNode(toDisplayString(Number(item.ratioVal) ? Number(item.ratioVal) : "-") + "%", 1)
                      ]),
                      default: withCtx(() => [
                        createElementVNode("div", _hoisted_23, toDisplayString(Number(item.ratioVal) ? Number(item.ratioVal) : "-"), 1)
                      ]),
                      _: 2
                    }, 1024)),
                    item.ratioVal !== null ? (openBlock(), createElementBlock("span", _hoisted_24, "%")) : createCommentVNode("", true),
                    item.ratioVal ? (openBlock(), createElementBlock("div", _hoisted_25, [
                      item.ratioVal > 0 ? (openBlock(), createElementBlock("img", {
                        key: 0,
                        src: unref(up)
                      }, null, 8, _hoisted_26)) : item.ratioVal < 0 ? (openBlock(), createElementBlock("img", {
                        key: 1,
                        src: unref(down)
                      }, null, 8, _hoisted_27)) : createCommentVNode("", true)
                    ])) : createCommentVNode("", true)
                  ])
                ])
              ])
            ]);
          }), 128))
        ])) : createCommentVNode("", true)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

import { bind } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { incrementalApplyVisual } from "../../visual/visualSolution.js";
import VisualMapping from "../../visual/VisualMapping.js";
import { getVisualFromData } from "../../visual/helper.js";
var visualMapEncodingHandlers = [
  {
    createOnAllSeries: true,
    reset: function(seriesModel, ecModel) {
      var resetDefines = [];
      ecModel.eachComponent("visualMap", function(visualMapModel) {
        var pipelineContext = seriesModel.pipelineContext;
        if (!visualMapModel.isTargetSeries(seriesModel) || pipelineContext && pipelineContext.large) {
          return;
        }
        resetDefines.push(incrementalApplyVisual(visualMapModel.stateList, visualMapModel.targetVisuals, bind(visualMapModel.getValueState, visualMapModel), visualMapModel.getDataDimensionIndex(seriesModel.getData())));
      });
      return resetDefines;
    }
  },
  // Only support color.
  {
    createOnAllSeries: true,
    reset: function(seriesModel, ecModel) {
      var data = seriesModel.getData();
      var visualMetaList = [];
      ecModel.eachComponent("visualMap", function(visualMapModel) {
        if (visualMapModel.isTargetSeries(seriesModel)) {
          var visualMeta = visualMapModel.getVisualMeta(bind(getColorVisual, null, seriesModel, visualMapModel)) || {
            stops: [],
            outerColors: []
          };
          var dimIdx = visualMapModel.getDataDimensionIndex(data);
          if (dimIdx >= 0) {
            visualMeta.dimension = dimIdx;
            visualMetaList.push(visualMeta);
          }
        }
      });
      seriesModel.getData().setVisual("visualMeta", visualMetaList);
    }
  }
];
function getColorVisual(seriesModel, visualMapModel, value, valueState) {
  var mappings = visualMapModel.targetVisuals[valueState];
  var visualTypes = VisualMapping.prepareVisualTypes(mappings);
  var resultVisual = {
    color: getVisualFromData(seriesModel.getData(), "color")
    // default color.
  };
  for (var i = 0, len = visualTypes.length; i < len; i++) {
    var type = visualTypes[i];
    var mapping = mappings[type === "opacity" ? "__alphaForOpacity" : type];
    mapping && mapping.applyVisual(value, getVisual, setVisual);
  }
  return resultVisual.color;
  function getVisual(key) {
    return resultVisual[key];
  }
  function setVisual(key, value2) {
    resultVisual[key] = value2;
  }
}
export {
  visualMapEncodingHandlers
};

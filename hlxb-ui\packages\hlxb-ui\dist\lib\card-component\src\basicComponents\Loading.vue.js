"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../../../config/index.js");
const Loading3QuartersOutlined = require("../../../node_modules/.pnpm/@ant-design_icons-vue@6.1.0_vue@3.5.20_typescript@4.9.5_/node_modules/@ant-design/icons-vue/es/icons/Loading3QuartersOutlined.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCardLoading"
  },
  __name: "Loading",
  props: {
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("card-loading");
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        vue.createVNode(vue.unref(Loading3QuartersOutlined.default), {
          style: { "font-size": "24px", "color": "var(--theme-color)", "margin": "0 auto", "display": "block" },
          spin: ""
        })
      ], 2);
    };
  }
});
exports.default = _sfc_main;

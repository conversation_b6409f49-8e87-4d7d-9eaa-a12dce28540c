import AxisView from "../axis/AxisView.js";
import CartesianAxisPointer from "./CartesianAxisPointer.js";
import AxisPointerModel from "./AxisPointerModel.js";
import AxisPointerView from "./AxisPointerView.js";
import { isArray } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { collect } from "./modelHelper.js";
import axisTrigger from "./axisTrigger.js";
function install(registers) {
  AxisView.registerAxisPointerClass("CartesianAxisPointer", CartesianAxisPointer);
  registers.registerComponentModel(AxisPointerModel);
  registers.registerComponentView(AxisPointerView);
  registers.registerPreprocessor(function(option) {
    if (option) {
      (!option.axisPointer || option.axisPointer.length === 0) && (option.axisPointer = {});
      var link = option.axisPointer.link;
      if (link && !isArray(link)) {
        option.axisPointer.link = [link];
      }
    }
  });
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, function(ecModel, api) {
    ecModel.getComponent("axisPointer").coordSysAxesInfo = collect(ecModel, api);
  });
  registers.registerAction({
    type: "updateAxisPointer",
    event: "updateAxisPointer",
    update: ":updateAxisPointer"
  }, axisTrigger);
}
export {
  install
};

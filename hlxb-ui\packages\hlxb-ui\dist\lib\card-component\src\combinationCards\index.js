"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const index = require("../../../utils/index.js");
const PieSimpleCard_vue_vue_type_script_setup_true_lang = require("./chartsType/PieSimpleCard.vue.js");
;/* empty css                               */
const PiePlusCard_vue_vue_type_script_setup_true_lang = require("./chartsType/PiePlusCard.vue.js");
;/* empty css                             */
const LineSimpleCard_vue_vue_type_script_setup_true_lang = require("./chartsType/LineSimpleCard.vue.js");
;/* empty css                                */
const LinePlusCard_vue_vue_type_script_setup_true_lang = require("./chartsType/LinePlusCard.vue.js");
;/* empty css                              */
const BarSimpleCard_vue_vue_type_script_setup_true_lang = require("./chartsType/BarSimpleCard.vue.js");
;/* empty css                               */
const BarPlusCard_vue_vue_type_script_setup_true_lang = require("./chartsType/BarPlusCard.vue.js");
;/* empty css                             */
const RankingSimpleCard_vue_vue_type_script_setup_true_lang = require("./rankingType/RankingSimpleCard.vue.js");
;/* empty css                                    */
const RankingPlusCard_vue_vue_type_script_setup_true_lang = require("./rankingType/RankingPlusCard.vue.js");
;/* empty css                                  */
const SummarySimpleCard_vue_vue_type_script_setup_true_lang = require("./summaryType/SummarySimpleCard.vue.js");
;/* empty css                                    */
const HlxbPieSimpleCard = index.withInstall(PieSimpleCard_vue_vue_type_script_setup_true_lang.default);
const HlxbPiePlusCard = index.withInstall(PiePlusCard_vue_vue_type_script_setup_true_lang.default);
const HlxbLineSimpleCard = index.withInstall(LineSimpleCard_vue_vue_type_script_setup_true_lang.default);
const HlxbLinePlusCard = index.withInstall(LinePlusCard_vue_vue_type_script_setup_true_lang.default);
const HlxbBarSimpleCard = index.withInstall(BarSimpleCard_vue_vue_type_script_setup_true_lang.default);
const HlxbBarPlusCard = index.withInstall(BarPlusCard_vue_vue_type_script_setup_true_lang.default);
const HlxbRankingSimpleCard = index.withInstall(RankingSimpleCard_vue_vue_type_script_setup_true_lang.default);
const HlxbRankingPlusCard = index.withInstall(RankingPlusCard_vue_vue_type_script_setup_true_lang.default);
const HlxbSummarySimpleCard = index.withInstall(SummarySimpleCard_vue_vue_type_script_setup_true_lang.default);
exports.HlxbBarPlusCard = HlxbBarPlusCard;
exports.HlxbBarSimpleCard = HlxbBarSimpleCard;
exports.HlxbLinePlusCard = HlxbLinePlusCard;
exports.HlxbLineSimpleCard = HlxbLineSimpleCard;
exports.HlxbPiePlusCard = HlxbPiePlusCard;
exports.HlxbPieSimpleCard = HlxbPieSimpleCard;
exports.HlxbRankingPlusCard = HlxbRankingPlusCard;
exports.HlxbRankingSimpleCard = HlxbRankingSimpleCard;
exports.HlxbSummarySimpleCard = HlxbSummarySimpleCard;

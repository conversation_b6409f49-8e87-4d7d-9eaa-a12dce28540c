import Fraction from "../../../../../../../fraction.js@5.3.4/node_modules/fraction.js/dist/fraction.js";
import { factory } from "../../utils/factory.js";
var name = "Fraction";
var dependencies = [];
var createFractionClass = /* @__PURE__ */ factory(name, dependencies, () => {
  Object.defineProperty(Fraction, "name", {
    value: "Fraction"
  });
  Fraction.prototype.constructor = Fraction;
  Fraction.prototype.type = "Fraction";
  Fraction.prototype.isFraction = true;
  Fraction.prototype.toJSON = function() {
    return {
      mathjs: "Fraction",
      n: String(this.s * this.n),
      d: String(this.d)
    };
  };
  Fraction.fromJSON = function(json) {
    return new Fraction(json);
  };
  return Fraction;
}, {
  isClass: true
});
export {
  createFractionClass
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Eventful from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Eventful.js";
import { isMiddleOrRightButtonOnMouseUpDown, stop } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/event.js";
import { isTaken } from "./interactionMutex.js";
import { bind, retrieve2, extend, defaults, isString } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { makeInner } from "../../util/model.js";
import { retrieveZInfo } from "../../util/graphic.js";
import { onIrrelevantElement } from "./cursorHelper.js";
var RoamController = (
  /** @class */
  function(_super) {
    __extends(RoamController2, _super);
    function RoamController2(zr) {
      var _this = _super.call(this) || this;
      _this._zr = zr;
      var mousedownHandler = bind(_this._mousedownHandler, _this);
      var mousemoveHandler = bind(_this._mousemoveHandler, _this);
      var mouseupHandler = bind(_this._mouseupHandler, _this);
      var mousewheelHandler = bind(_this._mousewheelHandler, _this);
      var pinchHandler = bind(_this._pinchHandler, _this);
      _this.enable = function(controlType, rawOpt) {
        var zInfo = rawOpt.zInfo;
        var _a = retrieveZInfo(zInfo.component), z = _a.z, zlevel = _a.zlevel;
        var zInfoParsed = {
          component: zInfo.component,
          z,
          zlevel,
          // By default roam controller is the lowest z2 comparing to other elememts in a component.
          z2: retrieve2(zInfo.z2, -Infinity)
        };
        var triggerInfo = extend({}, rawOpt.triggerInfo);
        this._opt = defaults(extend({}, rawOpt), {
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          // By default, wheel do not trigger move.
          moveOnMouseWheel: false,
          preventDefaultMouseMove: true,
          zInfoParsed,
          triggerInfo
        });
        if (controlType == null) {
          controlType = true;
        }
        if (!this._enabled || this._controlType !== controlType) {
          this._enabled = true;
          this.disable();
          if (controlType === true || controlType === "move" || controlType === "pan") {
            addRoamZrListener(zr, "mousedown", mousedownHandler, zInfoParsed);
            addRoamZrListener(zr, "mousemove", mousemoveHandler, zInfoParsed);
            addRoamZrListener(zr, "mouseup", mouseupHandler, zInfoParsed);
          }
          if (controlType === true || controlType === "scale" || controlType === "zoom") {
            addRoamZrListener(zr, "mousewheel", mousewheelHandler, zInfoParsed);
            addRoamZrListener(zr, "pinch", pinchHandler, zInfoParsed);
          }
        }
      };
      _this.disable = function() {
        this._enabled = false;
        removeRoamZrListener(zr, "mousedown", mousedownHandler);
        removeRoamZrListener(zr, "mousemove", mousemoveHandler);
        removeRoamZrListener(zr, "mouseup", mouseupHandler);
        removeRoamZrListener(zr, "mousewheel", mousewheelHandler);
        removeRoamZrListener(zr, "pinch", pinchHandler);
      };
      return _this;
    }
    RoamController2.prototype.isDragging = function() {
      return this._dragging;
    };
    RoamController2.prototype.isPinching = function() {
      return this._pinching;
    };
    RoamController2.prototype._checkPointer = function(e, x, y) {
      var opt = this._opt;
      var zInfoParsed = opt.zInfoParsed;
      if (onIrrelevantElement(e, opt.api, zInfoParsed.component)) {
        return false;
      }
      var triggerInfo = opt.triggerInfo;
      var roamTrigger = triggerInfo.roamTrigger;
      var inArea = false;
      if (roamTrigger === "global") {
        inArea = true;
      }
      if (!inArea) {
        inArea = triggerInfo.isInSelf(e, x, y);
      }
      if (inArea && triggerInfo.isInClip && !triggerInfo.isInClip(e, x, y)) {
        inArea = false;
      }
      return inArea;
    };
    RoamController2.prototype._decideCursorStyle = function(e, x, y, forReverse) {
      var target = e.target;
      if (!target && this._checkPointer(e, x, y)) {
        return "grab";
      }
      if (forReverse) {
        return target && target.cursor || "default";
      }
    };
    RoamController2.prototype.dispose = function() {
      this.disable();
    };
    RoamController2.prototype._mousedownHandler = function(e) {
      if (isMiddleOrRightButtonOnMouseUpDown(e) || eventConsumed(e)) {
        return;
      }
      var el = e.target;
      while (el) {
        if (el.draggable) {
          return;
        }
        el = el.__hostTarget || el.parent;
      }
      var x = e.offsetX;
      var y = e.offsetY;
      if (this._checkPointer(e, x, y)) {
        this._x = x;
        this._y = y;
        this._dragging = true;
      }
    };
    RoamController2.prototype._mousemoveHandler = function(e) {
      var zr = this._zr;
      if (e.gestureEvent === "pinch" || isTaken(zr, "globalPan") || eventConsumed(e)) {
        return;
      }
      var x = e.offsetX;
      var y = e.offsetY;
      if (!this._dragging || !isAvailableBehavior("moveOnMouseMove", e, this._opt)) {
        var cursorStyle = this._decideCursorStyle(e, x, y, false);
        if (cursorStyle) {
          zr.setCursorStyle(cursorStyle);
        }
        return;
      }
      zr.setCursorStyle("grabbing");
      var oldX = this._x;
      var oldY = this._y;
      var dx = x - oldX;
      var dy = y - oldY;
      this._x = x;
      this._y = y;
      if (this._opt.preventDefaultMouseMove) {
        stop(e.event);
      }
      e.__ecRoamConsumed = true;
      trigger(this, "pan", "moveOnMouseMove", e, {
        dx,
        dy,
        oldX,
        oldY,
        newX: x,
        newY: y,
        isAvailableBehavior: null
      });
    };
    RoamController2.prototype._mouseupHandler = function(e) {
      if (eventConsumed(e)) {
        return;
      }
      var zr = this._zr;
      if (!isMiddleOrRightButtonOnMouseUpDown(e)) {
        this._dragging = false;
        var cursorStyle = this._decideCursorStyle(e, e.offsetX, e.offsetY, true);
        if (cursorStyle) {
          zr.setCursorStyle(cursorStyle);
        }
      }
    };
    RoamController2.prototype._mousewheelHandler = function(e) {
      if (eventConsumed(e)) {
        return;
      }
      var shouldZoom = isAvailableBehavior("zoomOnMouseWheel", e, this._opt);
      var shouldMove = isAvailableBehavior("moveOnMouseWheel", e, this._opt);
      var wheelDelta = e.wheelDelta;
      var absWheelDeltaDelta = Math.abs(wheelDelta);
      var originX = e.offsetX;
      var originY = e.offsetY;
      if (wheelDelta === 0 || !shouldZoom && !shouldMove) {
        return;
      }
      if (shouldZoom) {
        var factor = absWheelDeltaDelta > 3 ? 1.4 : absWheelDeltaDelta > 1 ? 1.2 : 1.1;
        var scale = wheelDelta > 0 ? factor : 1 / factor;
        this._checkTriggerMoveZoom(this, "zoom", "zoomOnMouseWheel", e, {
          scale,
          originX,
          originY,
          isAvailableBehavior: null
        });
      }
      if (shouldMove) {
        var absDelta = Math.abs(wheelDelta);
        var scrollDelta = (wheelDelta > 0 ? 1 : -1) * (absDelta > 3 ? 0.4 : absDelta > 1 ? 0.15 : 0.05);
        this._checkTriggerMoveZoom(this, "scrollMove", "moveOnMouseWheel", e, {
          scrollDelta,
          originX,
          originY,
          isAvailableBehavior: null
        });
      }
    };
    RoamController2.prototype._pinchHandler = function(e) {
      if (isTaken(this._zr, "globalPan") || eventConsumed(e)) {
        return;
      }
      var scale = e.pinchScale > 1 ? 1.1 : 1 / 1.1;
      this._checkTriggerMoveZoom(this, "zoom", null, e, {
        scale,
        originX: e.pinchX,
        originY: e.pinchY,
        isAvailableBehavior: null
      });
    };
    RoamController2.prototype._checkTriggerMoveZoom = function(controller, eventName, behaviorToCheck, e, contollerEvent) {
      if (controller._checkPointer(e, contollerEvent.originX, contollerEvent.originY)) {
        stop(e.event);
        e.__ecRoamConsumed = true;
        trigger(controller, eventName, behaviorToCheck, e, contollerEvent);
      }
    };
    return RoamController2;
  }(Eventful)
);
function eventConsumed(e) {
  return e.__ecRoamConsumed;
}
var innerZrStore = makeInner();
function ensureZrStore(zr) {
  var store = innerZrStore(zr);
  store.roam = store.roam || {};
  store.uniform = store.uniform || {};
  return store;
}
function addRoamZrListener(zr, eventType, listener, zInfoParsed) {
  var store = ensureZrStore(zr);
  var roam = store.roam;
  var listenerList = roam[eventType] = roam[eventType] || [];
  var idx = 0;
  for (; idx < listenerList.length; idx++) {
    var currZInfo = listenerList[idx].zInfoParsed;
    if ((currZInfo.zlevel - zInfoParsed.zlevel || currZInfo.z - zInfoParsed.z || currZInfo.z2 - zInfoParsed.z2) <= 0) {
      break;
    }
  }
  listenerList.splice(idx, 0, {
    listener,
    zInfoParsed
  });
  ensureUniformListener(zr, eventType);
}
function removeRoamZrListener(zr, eventType, listener) {
  var store = ensureZrStore(zr);
  var listenerList = store.roam[eventType] || [];
  for (var idx = 0; idx < listenerList.length; idx++) {
    if (listenerList[idx].listener === listener) {
      listenerList.splice(idx, 1);
      if (!listenerList.length) {
        removeUniformListener(zr, eventType);
      }
      return;
    }
  }
}
function ensureUniformListener(zr, eventType) {
  var store = ensureZrStore(zr);
  if (!store.uniform[eventType]) {
    zr.on(eventType, store.uniform[eventType] = function(event) {
      var listenerList = store.roam[eventType];
      if (listenerList) {
        for (var i = 0; i < listenerList.length; i++) {
          listenerList[i].listener(event);
        }
      }
    });
  }
}
function removeUniformListener(zr, eventType) {
  var store = ensureZrStore(zr);
  var uniform = store.uniform;
  if (uniform[eventType]) {
    zr.off(eventType, uniform[eventType]);
    uniform[eventType] = null;
  }
}
function trigger(controller, eventName, behaviorToCheck, e, contollerEvent) {
  contollerEvent.isAvailableBehavior = bind(isAvailableBehavior, null, behaviorToCheck, e);
  controller.trigger(eventName, contollerEvent);
}
function isAvailableBehavior(behaviorToCheck, e, settings) {
  var setting = settings[behaviorToCheck];
  return !behaviorToCheck || setting && (!isString(setting) || e.event[setting + "Key"]);
}
export {
  RoamController as default
};

import { extend, each, map, mergeAll, retrieve, createHashMap, merge } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import Geo, { geo2DDimensions } from "./Geo.js";
import { createBoxLayoutReference, getLayoutRect, applyPreserveAspect } from "../../util/layout.js";
import { parsePercent } from "../../util/number.js";
import geoSourceManager from "./geoSourceManager.js";
import { min, max } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/vector.js";
import { injectCoordSysByOption } from "../../core/CoordinateSystem.js";
import { SINGLE_REFERRING } from "../../util/model.js";
function resizeGeo(geoModel, api) {
  var boundingCoords = geoModel.get("boundingCoords");
  if (boundingCoords != null) {
    var leftTop_1 = boundingCoords[0];
    var rightBottom_1 = boundingCoords[1];
    if (!(isFinite(leftTop_1[0]) && isFinite(leftTop_1[1]) && isFinite(rightBottom_1[0]) && isFinite(rightBottom_1[1]))) {
      if (process.env.NODE_ENV !== "production") {
        console.error("Invalid boundingCoords");
      }
    } else {
      var projection_1 = this.projection;
      if (projection_1) {
        var xMin = leftTop_1[0];
        var yMin = leftTop_1[1];
        var xMax = rightBottom_1[0];
        var yMax = rightBottom_1[1];
        leftTop_1 = [Infinity, Infinity];
        rightBottom_1 = [-Infinity, -Infinity];
        var sampleLine = function(x0, y0, x1, y1) {
          var dx = x1 - x0;
          var dy = y1 - y0;
          for (var i = 0; i <= 100; i++) {
            var p = i / 100;
            var pt = projection_1.project([x0 + dx * p, y0 + dy * p]);
            min(leftTop_1, leftTop_1, pt);
            max(rightBottom_1, rightBottom_1, pt);
          }
        };
        sampleLine(xMin, yMin, xMax, yMin);
        sampleLine(xMax, yMin, xMax, yMax);
        sampleLine(xMax, yMax, xMin, yMax);
        sampleLine(xMin, yMax, xMax, yMin);
      }
      this.setBoundingRect(leftTop_1[0], leftTop_1[1], rightBottom_1[0] - leftTop_1[0], rightBottom_1[1] - leftTop_1[1]);
    }
  }
  var rect = this.getBoundingRect();
  var centerOption = geoModel.get("layoutCenter");
  var sizeOption = geoModel.get("layoutSize");
  var refContainer = createBoxLayoutReference(geoModel, api).refContainer;
  var aspect = rect.width / rect.height * this.aspectScale;
  var useCenterAndSize = false;
  var center;
  var size;
  if (centerOption && sizeOption) {
    center = [parsePercent(centerOption[0], refContainer.width) + refContainer.x, parsePercent(centerOption[1], refContainer.height) + refContainer.y];
    size = parsePercent(sizeOption, Math.min(refContainer.width, refContainer.height));
    if (!isNaN(center[0]) && !isNaN(center[1]) && !isNaN(size)) {
      useCenterAndSize = true;
    } else {
      if (process.env.NODE_ENV !== "production") {
        console.warn("Given layoutCenter or layoutSize data are invalid. Use left/top/width/height instead.");
      }
    }
  }
  var viewRect;
  if (useCenterAndSize) {
    viewRect = {};
    if (aspect > 1) {
      viewRect.width = size;
      viewRect.height = size / aspect;
    } else {
      viewRect.height = size;
      viewRect.width = size * aspect;
    }
    viewRect.y = center[1] - viewRect.height / 2;
    viewRect.x = center[0] - viewRect.width / 2;
  } else {
    var boxLayoutOption = geoModel.getBoxLayoutParams();
    boxLayoutOption.aspect = aspect;
    viewRect = getLayoutRect(boxLayoutOption, refContainer);
    viewRect = applyPreserveAspect(geoModel, viewRect, aspect);
  }
  this.setViewRect(viewRect.x, viewRect.y, viewRect.width, viewRect.height);
  this.setCenter(geoModel.get("center"));
  this.setZoom(geoModel.get("zoom"));
}
function setGeoCoords(geo, model) {
  each(model.get("geoCoord"), function(geoCoord, name) {
    geo.addGeoCoord(name, geoCoord);
  });
}
var GeoCreator = (
  /** @class */
  function() {
    function GeoCreator2() {
      this.dimensions = geo2DDimensions;
    }
    GeoCreator2.prototype.create = function(ecModel, api) {
      var geoList = [];
      function getCommonGeoProperties(model) {
        return {
          nameProperty: model.get("nameProperty"),
          aspectScale: model.get("aspectScale"),
          projection: model.get("projection")
        };
      }
      ecModel.eachComponent("geo", function(geoModel, idx) {
        var mapName = geoModel.get("map");
        var geo = new Geo(mapName + idx, mapName, extend({
          nameMap: geoModel.get("nameMap"),
          api,
          ecModel
        }, getCommonGeoProperties(geoModel)));
        geo.zoomLimit = geoModel.get("scaleLimit");
        geoList.push(geo);
        geoModel.coordinateSystem = geo;
        geo.model = geoModel;
        geo.resize = resizeGeo;
        geo.resize(geoModel, api);
      });
      ecModel.eachSeries(function(seriesModel) {
        injectCoordSysByOption({
          targetModel: seriesModel,
          coordSysType: "geo",
          coordSysProvider: function() {
            var geoModel = seriesModel.subType === "map" ? seriesModel.getHostGeoModel() : seriesModel.getReferringComponents("geo", SINGLE_REFERRING).models[0];
            return geoModel && geoModel.coordinateSystem;
          },
          allowNotFound: true
        });
      });
      var mapModelGroupBySeries = {};
      ecModel.eachSeriesByType("map", function(seriesModel) {
        if (!seriesModel.getHostGeoModel()) {
          var mapType = seriesModel.getMapType();
          mapModelGroupBySeries[mapType] = mapModelGroupBySeries[mapType] || [];
          mapModelGroupBySeries[mapType].push(seriesModel);
        }
      });
      each(mapModelGroupBySeries, function(mapSeries, mapType) {
        var nameMapList = map(mapSeries, function(singleMapSeries) {
          return singleMapSeries.get("nameMap");
        });
        var geo = new Geo(mapType, mapType, extend({
          nameMap: mergeAll(nameMapList),
          api,
          ecModel
        }, getCommonGeoProperties(mapSeries[0])));
        geo.zoomLimit = retrieve.apply(null, map(mapSeries, function(singleMapSeries) {
          return singleMapSeries.get("scaleLimit");
        }));
        geoList.push(geo);
        geo.resize = resizeGeo;
        geo.resize(mapSeries[0], api);
        each(mapSeries, function(singleMapSeries) {
          singleMapSeries.coordinateSystem = geo;
          setGeoCoords(geo, singleMapSeries);
        });
      });
      return geoList;
    };
    GeoCreator2.prototype.getFilledRegions = function(originRegionArr, mapName, nameMap, nameProperty) {
      var regionsArr = (originRegionArr || []).slice();
      var dataNameMap = createHashMap();
      for (var i = 0; i < regionsArr.length; i++) {
        dataNameMap.set(regionsArr[i].name, regionsArr[i]);
      }
      var source = geoSourceManager.load(mapName, nameMap, nameProperty);
      each(source.regions, function(region) {
        var name = region.name;
        var regionOption = dataNameMap.get(name);
        var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;
        if (!regionOption) {
          regionOption = {
            name
          };
          regionsArr.push(regionOption);
        }
        specifiedGeoJSONRegionStyle && merge(regionOption, specifiedGeoJSONRegionStyle);
      });
      return regionsArr;
    };
    return GeoCreator2;
  }()
);
var geoCreator = new GeoCreator();
export {
  geoCreator as default
};

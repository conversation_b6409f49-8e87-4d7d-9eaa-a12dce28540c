import { createHashMap, assert, indexOf } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
var DATA_ZOOM_AXIS_DIMENSIONS = ["x", "y", "radius", "angle", "single"];
var SERIES_COORDS = ["cartesian2d", "polar", "singleAxis"];
function isCoordSupported(seriesModel) {
  var coordType = seriesModel.get("coordinateSystem");
  return indexOf(SERIES_COORDS, coordType) >= 0;
}
function getAxisMainType(axisDim) {
  if (process.env.NODE_ENV !== "production") {
    assert(axisDim);
  }
  return axisDim + "Axis";
}
function findEffectedDataZooms(ecModel, payload) {
  var axisRecords = createHashMap();
  var effectedModels = [];
  var effectedModelMap = createHashMap();
  ecModel.eachComponent({
    mainType: "dataZoom",
    query: payload
  }, function(dataZoomModel) {
    if (!effectedModelMap.get(dataZoomModel.uid)) {
      addToEffected(dataZoomModel);
    }
  });
  var foundNewLink;
  do {
    foundNewLink = false;
    ecModel.eachComponent("dataZoom", processSingle);
  } while (foundNewLink);
  function processSingle(dataZoomModel) {
    if (!effectedModelMap.get(dataZoomModel.uid) && isLinked(dataZoomModel)) {
      addToEffected(dataZoomModel);
      foundNewLink = true;
    }
  }
  function addToEffected(dataZoom) {
    effectedModelMap.set(dataZoom.uid, true);
    effectedModels.push(dataZoom);
    markAxisControlled(dataZoom);
  }
  function isLinked(dataZoomModel) {
    var isLink = false;
    dataZoomModel.eachTargetAxis(function(axisDim, axisIndex) {
      var axisIdxArr = axisRecords.get(axisDim);
      if (axisIdxArr && axisIdxArr[axisIndex]) {
        isLink = true;
      }
    });
    return isLink;
  }
  function markAxisControlled(dataZoomModel) {
    dataZoomModel.eachTargetAxis(function(axisDim, axisIndex) {
      (axisRecords.get(axisDim) || axisRecords.set(axisDim, []))[axisIndex] = true;
    });
  }
  return effectedModels;
}
function collectReferCoordSysModelInfo(dataZoomModel) {
  var ecModel = dataZoomModel.ecModel;
  var coordSysInfoWrap = {
    infoList: [],
    infoMap: createHashMap()
  };
  dataZoomModel.eachTargetAxis(function(axisDim, axisIndex) {
    var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);
    if (!axisModel) {
      return;
    }
    var coordSysModel = axisModel.getCoordSysModel();
    if (!coordSysModel) {
      return;
    }
    var coordSysUid = coordSysModel.uid;
    var coordSysInfo = coordSysInfoWrap.infoMap.get(coordSysUid);
    if (!coordSysInfo) {
      coordSysInfo = {
        model: coordSysModel,
        axisModels: []
      };
      coordSysInfoWrap.infoList.push(coordSysInfo);
      coordSysInfoWrap.infoMap.set(coordSysUid, coordSysInfo);
    }
    coordSysInfo.axisModels.push(axisModel);
  });
  return coordSysInfoWrap;
}
export {
  DATA_ZOOM_AXIS_DIMENSIONS,
  collectReferCoordSysModelInfo,
  findEffectedDataZooms,
  getAxisMainType,
  isCoordSupported
};

import { factory } from "../../../utils/factory.js";
import { deepMap } from "../../../utils/collection.js";
var name = "bignumber";
var dependencies = ["typed", "BigNumber"];
var createBignumber = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed,
    BigNumber
  } = _ref;
  return typed("bignumber", {
    "": function _() {
      return new BigNumber(0);
    },
    number: function number(x) {
      return new BigNumber(x + "");
    },
    string: function string(x) {
      var wordSizeSuffixMatch = x.match(/(0[box][0-9a-fA-F]*)i([0-9]*)/);
      if (wordSizeSuffixMatch) {
        var size = wordSizeSuffixMatch[2];
        var n = BigNumber(wordSizeSuffixMatch[1]);
        var twoPowSize = new BigNumber(2).pow(Number(size));
        if (n.gt(twoPowSize.sub(1))) {
          throw new SyntaxError('String "'.concat(x, '" is out of range'));
        }
        var twoPowSizeSubOne = new BigNumber(2).pow(Number(size) - 1);
        if (n.gte(twoPowSizeSubOne)) {
          return n.sub(twoPowSize);
        } else {
          return n;
        }
      }
      return new BigNumber(x);
    },
    BigNumber: function BigNumber2(x) {
      return x;
    },
    bigint: function bigint(x) {
      return new BigNumber(x.toString());
    },
    Unit: typed.referToSelf((self) => (x) => {
      var clone = x.clone();
      clone.value = self(x.value);
      return clone;
    }),
    Fraction: function Fraction(x) {
      return new BigNumber(String(x.n)).div(String(x.d)).times(String(x.s));
    },
    null: function _null(_x) {
      return new BigNumber(0);
    },
    "Array | Matrix": typed.referToSelf((self) => (x) => deepMap(x, self))
  });
});
export {
  createBignumber
};

import env from "./core/env.js";
var dpr = 1;
if (env.hasGlobalWindow) {
  dpr = Math.max(window.devicePixelRatio || window.screen && window.screen.deviceXDPI / window.screen.logicalXDPI || 1, 1);
}
var devicePixelRatio = dpr;
var DARK_MODE_THRESHOLD = 0.4;
var DARK_LABEL_COLOR = "#333";
var LIGHT_LABEL_COLOR = "#ccc";
var LIGHTER_LABEL_COLOR = "#eee";
export {
  DARK_LABEL_COLOR,
  DARK_MODE_THRESHOLD,
  LIGHTER_LABEL_COLOR,
  LIGHT_LABEL_COLOR,
  devicePixelRatio
};

import { __extends } from "../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { round, getPrecision } from "../util/number.js";
import { addCommas } from "../util/format.js";
import Scale from "./Scale.js";
import { contain, getIntervalPrecision, intervalScaleNiceTicks } from "./helper.js";
import { getScaleBreakHelper } from "./break.js";
var roundNumber = round;
var IntervalScale = (
  /** @class */
  function(_super) {
    __extends(IntervalScale2, _super);
    function IntervalScale2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = "interval";
      _this._interval = 0;
      _this._intervalPrecision = 2;
      return _this;
    }
    IntervalScale2.prototype.parse = function(val) {
      return val == null || val === "" ? NaN : Number(val);
    };
    IntervalScale2.prototype.contain = function(val) {
      return contain(val, this._extent);
    };
    IntervalScale2.prototype.normalize = function(val) {
      return this._calculator.normalize(val, this._extent);
    };
    IntervalScale2.prototype.scale = function(val) {
      return this._calculator.scale(val, this._extent);
    };
    IntervalScale2.prototype.getInterval = function() {
      return this._interval;
    };
    IntervalScale2.prototype.setInterval = function(interval) {
      this._interval = interval;
      this._niceExtent = this._extent.slice();
      this._intervalPrecision = getIntervalPrecision(interval);
    };
    IntervalScale2.prototype.getTicks = function(opt) {
      opt = opt || {};
      var interval = this._interval;
      var extent = this._extent;
      var niceTickExtent = this._niceExtent;
      var intervalPrecision = this._intervalPrecision;
      var scaleBreakHelper = getScaleBreakHelper();
      var ticks = [];
      if (!interval) {
        return ticks;
      }
      if (opt.breakTicks === "only_break" && scaleBreakHelper) ;
      var safeLimit = 1e4;
      if (extent[0] < niceTickExtent[0]) {
        if (opt.expandToNicedExtent) {
          ticks.push({
            value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)
          });
        } else {
          ticks.push({
            value: extent[0]
          });
        }
      }
      var estimateNiceMultiple = function(tickVal, targetTick) {
        return Math.round((targetTick - tickVal) / interval);
      };
      var tick = niceTickExtent[0];
      while (tick <= niceTickExtent[1]) {
        ticks.push({
          value: tick
        });
        tick = roundNumber(tick + interval, intervalPrecision);
        if (this._brkCtx) {
          var moreMultiple = this._brkCtx.calcNiceTickMultiple(tick, estimateNiceMultiple);
          if (moreMultiple >= 0) {
            tick = roundNumber(tick + moreMultiple * interval, intervalPrecision);
          }
        }
        if (ticks.length > 0 && tick === ticks[ticks.length - 1].value) {
          break;
        }
        if (ticks.length > safeLimit) {
          return [];
        }
      }
      var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];
      if (extent[1] > lastNiceTick) {
        if (opt.expandToNicedExtent) {
          ticks.push({
            value: roundNumber(lastNiceTick + interval, intervalPrecision)
          });
        } else {
          ticks.push({
            value: extent[1]
          });
        }
      }
      if (opt.breakTicks !== "none" && scaleBreakHelper) ;
      return ticks;
    };
    IntervalScale2.prototype.getMinorTicks = function(splitNumber) {
      var ticks = this.getTicks({
        expandToNicedExtent: true
      });
      var minorTicks = [];
      var extent = this.getExtent();
      for (var i = 1; i < ticks.length; i++) {
        var nextTick = ticks[i];
        var prevTick = ticks[i - 1];
        if (prevTick["break"] || nextTick["break"]) {
          continue;
        }
        var count = 0;
        var minorTicksGroup = [];
        var interval = nextTick.value - prevTick.value;
        var minorInterval = interval / splitNumber;
        var minorIntervalPrecision = getIntervalPrecision(minorInterval);
        while (count < splitNumber - 1) {
          var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval, minorIntervalPrecision);
          if (minorTick > extent[0] && minorTick < extent[1]) {
            minorTicksGroup.push(minorTick);
          }
          count++;
        }
        var scaleBreakHelper = getScaleBreakHelper();
        scaleBreakHelper && scaleBreakHelper.pruneTicksByBreak("auto", minorTicksGroup, this._getNonTransBreaks(), function(value) {
          return value;
        }, this._interval, extent);
        minorTicks.push(minorTicksGroup);
      }
      return minorTicks;
    };
    IntervalScale2.prototype._getNonTransBreaks = function() {
      return this._brkCtx ? this._brkCtx.breaks : [];
    };
    IntervalScale2.prototype.getLabel = function(data, opt) {
      if (data == null) {
        return "";
      }
      var precision = opt && opt.precision;
      if (precision == null) {
        precision = getPrecision(data.value) || 0;
      } else if (precision === "auto") {
        precision = this._intervalPrecision;
      }
      var dataNum = roundNumber(data.value, precision, true);
      return addCommas(dataNum);
    };
    IntervalScale2.prototype.calcNiceTicks = function(splitNumber, minInterval, maxInterval) {
      splitNumber = splitNumber || 5;
      var extent = this._extent.slice();
      var span = this._getExtentSpanWithBreaks();
      if (!isFinite(span)) {
        return;
      }
      if (span < 0) {
        span = -span;
        extent.reverse();
        this._innerSetExtent(extent[0], extent[1]);
        extent = this._extent.slice();
      }
      var result = intervalScaleNiceTicks(extent, span, splitNumber, minInterval, maxInterval);
      this._intervalPrecision = result.intervalPrecision;
      this._interval = result.interval;
      this._niceExtent = result.niceTickExtent;
    };
    IntervalScale2.prototype.calcNiceExtent = function(opt) {
      var extent = this._extent.slice();
      if (extent[0] === extent[1]) {
        if (extent[0] !== 0) {
          var expandSize = Math.abs(extent[0]);
          if (!opt.fixMax) {
            extent[1] += expandSize / 2;
            extent[0] -= expandSize / 2;
          } else {
            extent[0] -= expandSize / 2;
          }
        } else {
          extent[1] = 1;
        }
      }
      var span = extent[1] - extent[0];
      if (!isFinite(span)) {
        extent[0] = 0;
        extent[1] = 1;
      }
      this._innerSetExtent(extent[0], extent[1]);
      extent = this._extent.slice();
      this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);
      var interval = this._interval;
      var intervalPrecition = this._intervalPrecision;
      if (!opt.fixMin) {
        extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval, intervalPrecition);
      }
      if (!opt.fixMax) {
        extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval, intervalPrecition);
      }
      this._innerSetExtent(extent[0], extent[1]);
    };
    IntervalScale2.prototype.setNiceExtent = function(min, max) {
      this._niceExtent = [min, max];
    };
    IntervalScale2.type = "interval";
    return IntervalScale2;
  }(Scale)
);
Scale.registerClass(IntervalScale);
export {
  IntervalScale as default
};

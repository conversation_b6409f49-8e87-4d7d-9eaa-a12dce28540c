import { isObject, isArray, each as each$1, hasOwn, curry, defaults } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import BoundingRect from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js";
import { parsePercent } from "./number.js";
import { normalizeCssArray } from "./format.js";
import { error } from "./log.js";
import { getCoordForBoxCoordSys, BoxCoordinateSystemCoordFrom } from "../core/CoordinateSystem.js";
var each = each$1;
var LOCATION_PARAMS = ["left", "right", "top", "bottom", "width", "height"];
var HV_NAMES = [["width", "left", "right"], ["height", "top", "bottom"]];
function boxLayout(orient, group, gap, maxWidth, maxHeight) {
  var x = 0;
  var y = 0;
  if (maxWidth == null) {
    maxWidth = Infinity;
  }
  if (maxHeight == null) {
    maxHeight = Infinity;
  }
  var currentLineMaxSize = 0;
  group.eachChild(function(child, idx) {
    var rect = child.getBoundingRect();
    var nextChild = group.childAt(idx + 1);
    var nextChildRect = nextChild && nextChild.getBoundingRect();
    var nextX;
    var nextY;
    if (orient === "horizontal") {
      var moveX = rect.width + (nextChildRect ? -nextChildRect.x + rect.x : 0);
      nextX = x + moveX;
      if (nextX > maxWidth || child.newline) {
        x = 0;
        nextX = moveX;
        y += currentLineMaxSize + gap;
        currentLineMaxSize = rect.height;
      } else {
        currentLineMaxSize = Math.max(currentLineMaxSize, rect.height);
      }
    } else {
      var moveY = rect.height + (nextChildRect ? -nextChildRect.y + rect.y : 0);
      nextY = y + moveY;
      if (nextY > maxHeight || child.newline) {
        x += currentLineMaxSize + gap;
        y = 0;
        nextY = moveY;
        currentLineMaxSize = rect.width;
      } else {
        currentLineMaxSize = Math.max(currentLineMaxSize, rect.width);
      }
    }
    if (child.newline) {
      return;
    }
    child.x = x;
    child.y = y;
    child.markRedraw();
    orient === "horizontal" ? x = nextX + gap : y = nextY + gap;
  });
}
var box = boxLayout;
curry(boxLayout, "vertical");
curry(boxLayout, "horizontal");
function getBoxLayoutParams(boxLayoutModel, ignoreParent) {
  return {
    left: boxLayoutModel.getShallow("left", ignoreParent),
    top: boxLayoutModel.getShallow("top", ignoreParent),
    right: boxLayoutModel.getShallow("right", ignoreParent),
    bottom: boxLayoutModel.getShallow("bottom", ignoreParent),
    width: boxLayoutModel.getShallow("width", ignoreParent),
    height: boxLayoutModel.getShallow("height", ignoreParent)
  };
}
function getViewRectAndCenterForCircleLayout(seriesModel, api) {
  var layoutRef = createBoxLayoutReference(seriesModel, api, {
    enableLayoutOnlyByCenter: true
  });
  var boxLayoutParams = seriesModel.getBoxLayoutParams();
  var viewRect;
  var center;
  if (layoutRef.type === BoxLayoutReferenceType.point) {
    center = layoutRef.refPoint;
    viewRect = getLayoutRect(boxLayoutParams, {
      width: api.getWidth(),
      height: api.getHeight()
    });
  } else {
    var centerOption = seriesModel.get("center");
    var centerOptionArr = isArray(centerOption) ? centerOption : [centerOption, centerOption];
    viewRect = getLayoutRect(boxLayoutParams, layoutRef.refContainer);
    center = layoutRef.boxCoordFrom === BoxCoordinateSystemCoordFrom.coord2 ? layoutRef.refPoint : [parsePercent(centerOptionArr[0], viewRect.width) + viewRect.x, parsePercent(centerOptionArr[1], viewRect.height) + viewRect.y];
  }
  return {
    viewRect,
    center
  };
}
function getCircleLayout(seriesModel, api) {
  var _a = getViewRectAndCenterForCircleLayout(seriesModel, api), viewRect = _a.viewRect, center = _a.center;
  var radius = seriesModel.get("radius");
  if (!isArray(radius)) {
    radius = [0, radius];
  }
  var width = parsePercent(viewRect.width, api.getWidth());
  var height = parsePercent(viewRect.height, api.getHeight());
  var size = Math.min(width, height);
  var r0 = parsePercent(radius[0], size / 2);
  var r = parsePercent(radius[1], size / 2);
  return {
    cx: center[0],
    cy: center[1],
    r0,
    r,
    viewRect
  };
}
function getLayoutRect(positionInfo, containerRect, margin) {
  margin = normalizeCssArray(margin || 0);
  var containerWidth = containerRect.width;
  var containerHeight = containerRect.height;
  var left = parsePercent(positionInfo.left, containerWidth);
  var top = parsePercent(positionInfo.top, containerHeight);
  var right = parsePercent(positionInfo.right, containerWidth);
  var bottom = parsePercent(positionInfo.bottom, containerHeight);
  var width = parsePercent(positionInfo.width, containerWidth);
  var height = parsePercent(positionInfo.height, containerHeight);
  var verticalMargin = margin[2] + margin[0];
  var horizontalMargin = margin[1] + margin[3];
  var aspect = positionInfo.aspect;
  if (isNaN(width)) {
    width = containerWidth - right - horizontalMargin - left;
  }
  if (isNaN(height)) {
    height = containerHeight - bottom - verticalMargin - top;
  }
  if (aspect != null) {
    if (isNaN(width) && isNaN(height)) {
      if (aspect > containerWidth / containerHeight) {
        width = containerWidth * 0.8;
      } else {
        height = containerHeight * 0.8;
      }
    }
    if (isNaN(width)) {
      width = aspect * height;
    }
    if (isNaN(height)) {
      height = width / aspect;
    }
  }
  if (isNaN(left)) {
    left = containerWidth - right - width - horizontalMargin;
  }
  if (isNaN(top)) {
    top = containerHeight - bottom - height - verticalMargin;
  }
  switch (positionInfo.left || positionInfo.right) {
    case "center":
      left = containerWidth / 2 - width / 2 - margin[3];
      break;
    case "right":
      left = containerWidth - width - horizontalMargin;
      break;
  }
  switch (positionInfo.top || positionInfo.bottom) {
    case "middle":
    case "center":
      top = containerHeight / 2 - height / 2 - margin[0];
      break;
    case "bottom":
      top = containerHeight - height - verticalMargin;
      break;
  }
  left = left || 0;
  top = top || 0;
  if (isNaN(width)) {
    width = containerWidth - horizontalMargin - left - (right || 0);
  }
  if (isNaN(height)) {
    height = containerHeight - verticalMargin - top - (bottom || 0);
  }
  var rect = new BoundingRect((containerRect.x || 0) + left + margin[3], (containerRect.y || 0) + top + margin[0], width, height);
  rect.margin = margin;
  return rect;
}
function applyPreserveAspect(component, layoutRect, aspect) {
  var preserveAspect = component.getShallow("preserveAspect", true);
  if (!preserveAspect) {
    return layoutRect;
  }
  var actualAspect = layoutRect.width / layoutRect.height;
  if (Math.abs(Math.atan(aspect) - Math.atan(actualAspect)) < 1e-9) {
    return layoutRect;
  }
  var preserveAspectAlign = component.getShallow("preserveAspectAlign", true);
  var preserveAspectVerticalAlign = component.getShallow("preserveAspectVerticalAlign", true);
  var layoutOptInner = {
    width: layoutRect.width,
    height: layoutRect.height
  };
  var isCover = preserveAspect === "cover";
  if (actualAspect > aspect && !isCover || actualAspect < aspect && isCover) {
    layoutOptInner.width = layoutRect.height * aspect;
    preserveAspectAlign === "left" ? layoutOptInner.left = 0 : preserveAspectAlign === "right" ? layoutOptInner.right = 0 : layoutOptInner.left = "center";
  } else {
    layoutOptInner.height = layoutRect.width / aspect;
    preserveAspectVerticalAlign === "top" ? layoutOptInner.top = 0 : preserveAspectVerticalAlign === "bottom" ? layoutOptInner.bottom = 0 : layoutOptInner.top = "middle";
  }
  return getLayoutRect(layoutOptInner, layoutRect);
}
var BoxLayoutReferenceType = {
  rect: 1,
  point: 2
};
function createBoxLayoutReference(model, api, opt) {
  var refContainer;
  var refPoint;
  var layoutRefType;
  var boxCoordSys = model.boxCoordinateSystem;
  var boxCoordFrom;
  if (boxCoordSys) {
    var _a = getCoordForBoxCoordSys(model), coord = _a.coord, from = _a.from;
    if (boxCoordSys.dataToLayout) {
      layoutRefType = BoxLayoutReferenceType.rect;
      boxCoordFrom = from;
      var result = boxCoordSys.dataToLayout(coord);
      refContainer = result.contentRect || result.rect;
    } else if (opt && opt.enableLayoutOnlyByCenter && boxCoordSys.dataToPoint) {
      layoutRefType = BoxLayoutReferenceType.point;
      boxCoordFrom = from;
      refPoint = boxCoordSys.dataToPoint(coord);
    } else {
      if (process.env.NODE_ENV !== "production") {
        error(model.type + "[" + model.componentIndex + "]" + (" layout based on " + boxCoordSys.type + " is not supported."));
      }
    }
  }
  if (layoutRefType == null) {
    layoutRefType = BoxLayoutReferenceType.rect;
  }
  if (layoutRefType === BoxLayoutReferenceType.rect) {
    if (!refContainer) {
      refContainer = {
        x: 0,
        y: 0,
        width: api.getWidth(),
        height: api.getHeight()
      };
    }
    refPoint = [refContainer.x + refContainer.width / 2, refContainer.y + refContainer.height / 2];
  }
  return {
    type: layoutRefType,
    refContainer,
    refPoint,
    boxCoordFrom
  };
}
function positionElement(el, positionInfo, containerRect, margin, opt, out) {
  var h = !opt || !opt.hv || opt.hv[0];
  var v = !opt || !opt.hv || opt.hv[1];
  var boundingMode = opt && opt.boundingMode || "all";
  out = out || el;
  out.x = el.x;
  out.y = el.y;
  if (!h && !v) {
    return false;
  }
  var rect;
  if (boundingMode === "raw") {
    rect = el.type === "group" ? new BoundingRect(0, 0, +positionInfo.width || 0, +positionInfo.height || 0) : el.getBoundingRect();
  } else {
    rect = el.getBoundingRect();
    if (el.needLocalTransform()) {
      var transform = el.getLocalTransform();
      rect = rect.clone();
      rect.applyTransform(transform);
    }
  }
  var layoutRect = getLayoutRect(defaults({
    width: rect.width,
    height: rect.height
  }, positionInfo), containerRect, margin);
  var dx = h ? layoutRect.x - rect.x : 0;
  var dy = v ? layoutRect.y - rect.y : 0;
  if (boundingMode === "raw") {
    out.x = dx;
    out.y = dy;
  } else {
    out.x += dx;
    out.y += dy;
  }
  if (out === el) {
    el.markRedraw();
  }
  return true;
}
function sizeCalculable(option, hvIdx) {
  return option[HV_NAMES[hvIdx][0]] != null || option[HV_NAMES[hvIdx][1]] != null && option[HV_NAMES[hvIdx][2]] != null;
}
function fetchLayoutMode(ins) {
  var layoutMode = ins.layoutMode || ins.constructor.layoutMode;
  return isObject(layoutMode) ? layoutMode : layoutMode ? {
    type: layoutMode
  } : null;
}
function mergeLayoutParam(targetOption, newOption, opt) {
  var ignoreSize = opt && opt.ignoreSize;
  !isArray(ignoreSize) && (ignoreSize = [ignoreSize, ignoreSize]);
  var hResult = merge(HV_NAMES[0], 0);
  var vResult = merge(HV_NAMES[1], 1);
  copy(HV_NAMES[0], targetOption, hResult);
  copy(HV_NAMES[1], targetOption, vResult);
  function merge(names, hvIdx) {
    var newParams = {};
    var newValueCount = 0;
    var merged = {};
    var mergedValueCount = 0;
    var enoughParamNumber = 2;
    each(names, function(name) {
      merged[name] = targetOption[name];
    });
    each(names, function(name) {
      hasOwn(newOption, name) && (newParams[name] = merged[name] = newOption[name]);
      hasValue(newParams, name) && newValueCount++;
      hasValue(merged, name) && mergedValueCount++;
    });
    if (ignoreSize[hvIdx]) {
      if (hasValue(newOption, names[1])) {
        merged[names[2]] = null;
      } else if (hasValue(newOption, names[2])) {
        merged[names[1]] = null;
      }
      return merged;
    }
    if (mergedValueCount === enoughParamNumber || !newValueCount) {
      return merged;
    } else if (newValueCount >= enoughParamNumber) {
      return newParams;
    } else {
      for (var i = 0; i < names.length; i++) {
        var name_1 = names[i];
        if (!hasOwn(newParams, name_1) && hasOwn(targetOption, name_1)) {
          newParams[name_1] = targetOption[name_1];
          break;
        }
      }
      return newParams;
    }
  }
  function hasValue(obj, name) {
    return obj[name] != null && obj[name] !== "auto";
  }
  function copy(names, target, source) {
    each(names, function(name) {
      target[name] = source[name];
    });
  }
}
function getLayoutParams(source) {
  return copyLayoutParams({}, source);
}
function copyLayoutParams(target, source) {
  source && target && each(LOCATION_PARAMS, function(name) {
    hasOwn(source, name) && (target[name] = source[name]);
  });
  return target;
}
export {
  BoxLayoutReferenceType,
  HV_NAMES,
  LOCATION_PARAMS,
  applyPreserveAspect,
  box,
  copyLayoutParams,
  createBoxLayoutReference,
  fetchLayoutMode,
  getBoxLayoutParams,
  getCircleLayout,
  getLayoutParams,
  getLayoutRect,
  mergeLayoutParam,
  positionElement,
  sizeCalculable
};

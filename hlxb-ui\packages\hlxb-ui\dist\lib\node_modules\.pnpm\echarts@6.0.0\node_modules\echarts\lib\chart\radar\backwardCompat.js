"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
function radarBackwardCompat(option) {
  var polarOptArr = option.polar;
  if (polarOptArr) {
    if (!util.isArray(polarOptArr)) {
      polarOptArr = [polarOptArr];
    }
    var polarNotRadar_1 = [];
    util.each(polarOptArr, function(polarOpt, idx) {
      if (polarOpt.indicator) {
        if (polarOpt.type && !polarOpt.shape) {
          polarOpt.shape = polarOpt.type;
        }
        option.radar = option.radar || [];
        if (!util.isArray(option.radar)) {
          option.radar = [option.radar];
        }
        option.radar.push(polarOpt);
      } else {
        polarNotRadar_1.push(polarOpt);
      }
    });
    option.polar = polarNotRadar_1;
  }
  util.each(option.series, function(seriesOpt) {
    if (seriesOpt && seriesOpt.type === "radar" && seriesOpt.polarIndex) {
      seriesOpt.radarIndex = seriesOpt.polarIndex;
    }
  });
}
exports.default = radarBackwardCompat;

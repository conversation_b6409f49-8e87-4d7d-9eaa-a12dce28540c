"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const Path = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Path.js");
const Group = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const graphic = require("../../util/graphic.js");
const innerStore = require("../../util/innerStore.js");
const states = require("../../util/states.js");
const labelStyle = require("../../label/labelStyle.js");
const throttle = require("../../util/throttle.js");
const createClipPathFromCoordSys = require("../helper/createClipPathFromCoordSys.js");
const sausage = require("../../util/shape/sausage.js");
const Chart = require("../../view/Chart.js");
const CoordinateSystem = require("../../coord/CoordinateSystem.js");
const labelHelper = require("../helper/labelHelper.js");
const log = require("../../util/log.js");
const sectorLabel = require("../../label/sectorLabel.js");
const basicTransition = require("../../animation/basicTransition.js");
const sectorHelper = require("../helper/sectorHelper.js");
const Sector = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Sector.js");
const Rect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js");
var mathMax = Math.max;
var mathMin = Math.min;
function getClipArea(coord, data) {
  var coordSysClipArea = coord.getArea && coord.getArea();
  if (CoordinateSystem.isCoordinateSystemType(coord, "cartesian2d")) {
    var baseAxis = coord.getBaseAxis();
    if (baseAxis.type !== "category" || !baseAxis.onBand) {
      var expandWidth = data.getLayout("bandWidth");
      if (baseAxis.isHorizontal()) {
        coordSysClipArea.x -= expandWidth;
        coordSysClipArea.width += expandWidth * 2;
      } else {
        coordSysClipArea.y -= expandWidth;
        coordSysClipArea.height += expandWidth * 2;
      }
    }
  }
  return coordSysClipArea;
}
var BarView = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(BarView2, _super);
    function BarView2() {
      var _this = _super.call(this) || this;
      _this.type = BarView2.type;
      _this._isFirstFrame = true;
      return _this;
    }
    BarView2.prototype.render = function(seriesModel, ecModel, api, payload) {
      this._model = seriesModel;
      this._removeOnRenderedListener(api);
      this._updateDrawMode(seriesModel);
      var coordinateSystemType = seriesModel.get("coordinateSystem");
      if (coordinateSystemType === "cartesian2d" || coordinateSystemType === "polar") {
        this._progressiveEls = null;
        this._isLargeDraw ? this._renderLarge(seriesModel, ecModel, api) : this._renderNormal(seriesModel, ecModel, api, payload);
      } else if (process.env.NODE_ENV !== "production") {
        log.warn("Only cartesian2d and polar supported for bar.");
      }
    };
    BarView2.prototype.incrementalPrepareRender = function(seriesModel) {
      this._clear();
      this._updateDrawMode(seriesModel);
      this._updateLargeClip(seriesModel);
    };
    BarView2.prototype.incrementalRender = function(params, seriesModel) {
      this._progressiveEls = [];
      this._incrementalRenderLarge(params, seriesModel);
    };
    BarView2.prototype.eachRendered = function(cb) {
      graphic.traverseElements(this._progressiveEls || this.group, cb);
    };
    BarView2.prototype._updateDrawMode = function(seriesModel) {
      var isLargeDraw = seriesModel.pipelineContext.large;
      if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {
        this._isLargeDraw = isLargeDraw;
        this._clear();
      }
    };
    BarView2.prototype._renderNormal = function(seriesModel, ecModel, api, payload) {
      var group = this.group;
      var data = seriesModel.getData();
      var oldData = this._data;
      var coord = seriesModel.coordinateSystem;
      var baseAxis = coord.getBaseAxis();
      var isHorizontalOrRadial;
      if (coord.type === "cartesian2d") {
        isHorizontalOrRadial = baseAxis.isHorizontal();
      } else if (coord.type === "polar") {
        isHorizontalOrRadial = baseAxis.dim === "angle";
      }
      var animationModel = seriesModel.isAnimationEnabled() ? seriesModel : null;
      var realtimeSortCfg = shouldRealtimeSort(seriesModel, coord);
      if (realtimeSortCfg) {
        this._enableRealtimeSort(realtimeSortCfg, data, api);
      }
      var needsClip = seriesModel.get("clip", true) || realtimeSortCfg;
      var coordSysClipArea = getClipArea(coord, data);
      group.removeClipPath();
      var roundCap = seriesModel.get("roundCap", true);
      var drawBackground = seriesModel.get("showBackground", true);
      var backgroundModel = seriesModel.getModel("backgroundStyle");
      var barBorderRadius = backgroundModel.get("borderRadius") || 0;
      var bgEls = [];
      var oldBgEls = this._backgroundEls;
      var isInitSort = payload && payload.isInitSort;
      var isChangeOrder = payload && payload.type === "changeAxisOrder";
      function createBackground(dataIndex) {
        var bgLayout = getLayout[coord.type](data, dataIndex);
        if (!bgLayout) {
          return null;
        }
        var bgEl = createBackgroundEl(coord, isHorizontalOrRadial, bgLayout);
        bgEl.useStyle(backgroundModel.getItemStyle());
        if (coord.type === "cartesian2d") {
          bgEl.setShape("r", barBorderRadius);
        } else {
          bgEl.setShape("cornerRadius", barBorderRadius);
        }
        bgEls[dataIndex] = bgEl;
        return bgEl;
      }
      data.diff(oldData).add(function(dataIndex) {
        var itemModel = data.getItemModel(dataIndex);
        var layout = getLayout[coord.type](data, dataIndex, itemModel);
        if (!layout) {
          return;
        }
        if (drawBackground) {
          createBackground(dataIndex);
        }
        if (!data.hasValue(dataIndex) || !isValidLayout[coord.type](layout)) {
          return;
        }
        var isClipped = false;
        if (needsClip) {
          isClipped = clip[coord.type](coordSysClipArea, layout);
        }
        var el = elementCreator[coord.type](seriesModel, data, dataIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, false, roundCap);
        if (realtimeSortCfg) {
          el.forceLabelAnimation = true;
        }
        updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === "polar");
        if (isInitSort) {
          el.attr({
            shape: layout
          });
        } else if (realtimeSortCfg) {
          updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, dataIndex, isHorizontalOrRadial, false, false);
        } else {
          basicTransition.initProps(el, {
            shape: layout
          }, seriesModel, dataIndex);
        }
        data.setItemGraphicEl(dataIndex, el);
        group.add(el);
        el.ignore = isClipped;
      }).update(function(newIndex, oldIndex) {
        var itemModel = data.getItemModel(newIndex);
        var layout = getLayout[coord.type](data, newIndex, itemModel);
        if (!layout) {
          return;
        }
        if (drawBackground) {
          var bgEl = void 0;
          if (oldBgEls.length === 0) {
            bgEl = createBackground(oldIndex);
          } else {
            bgEl = oldBgEls[oldIndex];
            bgEl.useStyle(backgroundModel.getItemStyle());
            if (coord.type === "cartesian2d") {
              bgEl.setShape("r", barBorderRadius);
            } else {
              bgEl.setShape("cornerRadius", barBorderRadius);
            }
            bgEls[newIndex] = bgEl;
          }
          var bgLayout = getLayout[coord.type](data, newIndex);
          var shape = createBackgroundShape(isHorizontalOrRadial, bgLayout, coord);
          basicTransition.updateProps(bgEl, {
            shape
          }, animationModel, newIndex);
        }
        var el = oldData.getItemGraphicEl(oldIndex);
        if (!data.hasValue(newIndex) || !isValidLayout[coord.type](layout)) {
          group.remove(el);
          return;
        }
        var isClipped = false;
        if (needsClip) {
          isClipped = clip[coord.type](coordSysClipArea, layout);
          if (isClipped) {
            group.remove(el);
          }
        }
        var roundCapChanged = el && (el.type === "sector" && roundCap || el.type === "sausage" && !roundCap);
        if (roundCapChanged) {
          el && basicTransition.removeElementWithFadeOut(el, seriesModel, oldIndex);
          el = null;
        }
        if (!el) {
          el = elementCreator[coord.type](seriesModel, data, newIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, true, roundCap);
        } else {
          basicTransition.saveOldStyle(el);
        }
        if (realtimeSortCfg) {
          el.forceLabelAnimation = true;
        }
        if (isChangeOrder) {
          var textEl = el.getTextContent();
          if (textEl) {
            var labelInnerStore = labelStyle.labelInner(textEl);
            if (labelInnerStore.prevValue != null) {
              labelInnerStore.prevValue = labelInnerStore.value;
            }
          }
        } else {
          updateStyle(el, data, newIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === "polar");
        }
        if (isInitSort) {
          el.attr({
            shape: layout
          });
        } else if (realtimeSortCfg) {
          updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, newIndex, isHorizontalOrRadial, true, isChangeOrder);
        } else {
          basicTransition.updateProps(el, {
            shape: layout
          }, seriesModel, newIndex, null);
        }
        data.setItemGraphicEl(newIndex, el);
        el.ignore = isClipped;
        group.add(el);
      }).remove(function(dataIndex) {
        var el = oldData.getItemGraphicEl(dataIndex);
        el && basicTransition.removeElementWithFadeOut(el, seriesModel, dataIndex);
      }).execute();
      var bgGroup = this._backgroundGroup || (this._backgroundGroup = new Group.default());
      bgGroup.removeAll();
      for (var i = 0; i < bgEls.length; ++i) {
        bgGroup.add(bgEls[i]);
      }
      group.add(bgGroup);
      this._backgroundEls = bgEls;
      this._data = data;
    };
    BarView2.prototype._renderLarge = function(seriesModel, ecModel, api) {
      this._clear();
      createLarge(seriesModel, this.group);
      this._updateLargeClip(seriesModel);
    };
    BarView2.prototype._incrementalRenderLarge = function(params, seriesModel) {
      this._removeBackground();
      createLarge(seriesModel, this.group, this._progressiveEls, true);
    };
    BarView2.prototype._updateLargeClip = function(seriesModel) {
      var clipPath = seriesModel.get("clip", true) && createClipPathFromCoordSys.createClipPath(seriesModel.coordinateSystem, false, seriesModel);
      var group = this.group;
      if (clipPath) {
        group.setClipPath(clipPath);
      } else {
        group.removeClipPath();
      }
    };
    BarView2.prototype._enableRealtimeSort = function(realtimeSortCfg, data, api) {
      var _this = this;
      if (!data.count()) {
        return;
      }
      var baseAxis = realtimeSortCfg.baseAxis;
      if (this._isFirstFrame) {
        this._dispatchInitSort(data, realtimeSortCfg, api);
        this._isFirstFrame = false;
      } else {
        var orderMapping_1 = function(idx) {
          var el = data.getItemGraphicEl(idx);
          var shape = el && el.shape;
          return shape && // The result should be consistent with the initial sort by data value.
          // Do not support the case that both positive and negative exist.
          Math.abs(baseAxis.isHorizontal() ? shape.height : shape.width) || 0;
        };
        this._onRendered = function() {
          _this._updateSortWithinSameData(data, orderMapping_1, baseAxis, api);
        };
        api.getZr().on("rendered", this._onRendered);
      }
    };
    BarView2.prototype._dataSort = function(data, baseAxis, orderMapping) {
      var info = [];
      data.each(data.mapDimension(baseAxis.dim), function(ordinalNumber, dataIdx) {
        var mappedValue = orderMapping(dataIdx);
        mappedValue = mappedValue == null ? NaN : mappedValue;
        info.push({
          dataIndex: dataIdx,
          mappedValue,
          ordinalNumber
        });
      });
      info.sort(function(a, b) {
        return b.mappedValue - a.mappedValue;
      });
      return {
        ordinalNumbers: util.map(info, function(item) {
          return item.ordinalNumber;
        })
      };
    };
    BarView2.prototype._isOrderChangedWithinSameData = function(data, orderMapping, baseAxis) {
      var scale = baseAxis.scale;
      var ordinalDataDim = data.mapDimension(baseAxis.dim);
      var lastValue = Number.MAX_VALUE;
      for (var tickNum = 0, len = scale.getOrdinalMeta().categories.length; tickNum < len; ++tickNum) {
        var rawIdx = data.rawIndexOf(ordinalDataDim, scale.getRawOrdinalNumber(tickNum));
        var value = rawIdx < 0 ? Number.MIN_VALUE : orderMapping(data.indexOfRawIndex(rawIdx));
        if (value > lastValue) {
          return true;
        }
        lastValue = value;
      }
      return false;
    };
    BarView2.prototype._isOrderDifferentInView = function(orderInfo, baseAxis) {
      var scale = baseAxis.scale;
      var extent = scale.getExtent();
      var tickNum = Math.max(0, extent[0]);
      var tickMax = Math.min(extent[1], scale.getOrdinalMeta().categories.length - 1);
      for (; tickNum <= tickMax; ++tickNum) {
        if (orderInfo.ordinalNumbers[tickNum] !== scale.getRawOrdinalNumber(tickNum)) {
          return true;
        }
      }
    };
    BarView2.prototype._updateSortWithinSameData = function(data, orderMapping, baseAxis, api) {
      if (!this._isOrderChangedWithinSameData(data, orderMapping, baseAxis)) {
        return;
      }
      var sortInfo = this._dataSort(data, baseAxis, orderMapping);
      if (this._isOrderDifferentInView(sortInfo, baseAxis)) {
        this._removeOnRenderedListener(api);
        api.dispatchAction({
          type: "changeAxisOrder",
          componentType: baseAxis.dim + "Axis",
          axisId: baseAxis.index,
          sortInfo
        });
      }
    };
    BarView2.prototype._dispatchInitSort = function(data, realtimeSortCfg, api) {
      var baseAxis = realtimeSortCfg.baseAxis;
      var sortResult = this._dataSort(data, baseAxis, function(dataIdx) {
        return data.get(data.mapDimension(realtimeSortCfg.otherAxis.dim), dataIdx);
      });
      api.dispatchAction({
        type: "changeAxisOrder",
        componentType: baseAxis.dim + "Axis",
        isInitSort: true,
        axisId: baseAxis.index,
        sortInfo: sortResult
      });
    };
    BarView2.prototype.remove = function(ecModel, api) {
      this._clear(this._model);
      this._removeOnRenderedListener(api);
    };
    BarView2.prototype.dispose = function(ecModel, api) {
      this._removeOnRenderedListener(api);
    };
    BarView2.prototype._removeOnRenderedListener = function(api) {
      if (this._onRendered) {
        api.getZr().off("rendered", this._onRendered);
        this._onRendered = null;
      }
    };
    BarView2.prototype._clear = function(model) {
      var group = this.group;
      var data = this._data;
      if (model && model.isAnimationEnabled() && data && !this._isLargeDraw) {
        this._removeBackground();
        this._backgroundEls = [];
        data.eachItemGraphicEl(function(el) {
          basicTransition.removeElementWithFadeOut(el, model, innerStore.getECData(el).dataIndex);
        });
      } else {
        group.removeAll();
      }
      this._data = null;
      this._isFirstFrame = true;
    };
    BarView2.prototype._removeBackground = function() {
      this.group.remove(this._backgroundGroup);
      this._backgroundGroup = null;
    };
    BarView2.type = "bar";
    return BarView2;
  }(Chart.default)
);
var clip = {
  cartesian2d: function(coordSysBoundingRect, layout) {
    var signWidth = layout.width < 0 ? -1 : 1;
    var signHeight = layout.height < 0 ? -1 : 1;
    if (signWidth < 0) {
      layout.x += layout.width;
      layout.width = -layout.width;
    }
    if (signHeight < 0) {
      layout.y += layout.height;
      layout.height = -layout.height;
    }
    var coordSysX2 = coordSysBoundingRect.x + coordSysBoundingRect.width;
    var coordSysY2 = coordSysBoundingRect.y + coordSysBoundingRect.height;
    var x = mathMax(layout.x, coordSysBoundingRect.x);
    var x2 = mathMin(layout.x + layout.width, coordSysX2);
    var y = mathMax(layout.y, coordSysBoundingRect.y);
    var y2 = mathMin(layout.y + layout.height, coordSysY2);
    var xClipped = x2 < x;
    var yClipped = y2 < y;
    layout.x = xClipped && x > coordSysX2 ? x2 : x;
    layout.y = yClipped && y > coordSysY2 ? y2 : y;
    layout.width = xClipped ? 0 : x2 - x;
    layout.height = yClipped ? 0 : y2 - y;
    if (signWidth < 0) {
      layout.x += layout.width;
      layout.width = -layout.width;
    }
    if (signHeight < 0) {
      layout.y += layout.height;
      layout.height = -layout.height;
    }
    return xClipped || yClipped;
  },
  polar: function(coordSysClipArea, layout) {
    var signR = layout.r0 <= layout.r ? 1 : -1;
    if (signR < 0) {
      var tmp = layout.r;
      layout.r = layout.r0;
      layout.r0 = tmp;
    }
    var r = mathMin(layout.r, coordSysClipArea.r);
    var r0 = mathMax(layout.r0, coordSysClipArea.r0);
    layout.r = r;
    layout.r0 = r0;
    var clipped = r - r0 < 0;
    if (signR < 0) {
      var tmp = layout.r;
      layout.r = layout.r0;
      layout.r0 = tmp;
    }
    return clipped;
  }
};
var elementCreator = {
  cartesian2d: function(seriesModel, data, newIndex, layout, isHorizontal, animationModel, axisModel, isUpdate, roundCap) {
    var rect = new Rect.default({
      shape: util.extend({}, layout),
      z2: 1
    });
    rect.__dataIndex = newIndex;
    rect.name = "item";
    if (animationModel) {
      var rectShape = rect.shape;
      var animateProperty = isHorizontal ? "height" : "width";
      rectShape[animateProperty] = 0;
    }
    return rect;
  },
  polar: function(seriesModel, data, newIndex, layout, isRadial, animationModel, axisModel, isUpdate, roundCap) {
    var ShapeClass = !isRadial && roundCap ? sausage.default : Sector.default;
    var sector = new ShapeClass({
      shape: layout,
      z2: 1
    });
    sector.name = "item";
    var positionMap = createPolarPositionMapping(isRadial);
    sector.calculateTextPosition = sectorLabel.createSectorCalculateTextPosition(positionMap, {
      isRoundCap: ShapeClass === sausage.default
    });
    if (animationModel) {
      var sectorShape = sector.shape;
      var animateProperty = isRadial ? "r" : "endAngle";
      var animateTarget = {};
      sectorShape[animateProperty] = isRadial ? layout.r0 : layout.startAngle;
      animateTarget[animateProperty] = layout[animateProperty];
      (isUpdate ? basicTransition.updateProps : basicTransition.initProps)(sector, {
        shape: animateTarget
        // __value: typeof dataValue === 'string' ? parseInt(dataValue, 10) : dataValue
      }, animationModel);
    }
    return sector;
  }
};
function shouldRealtimeSort(seriesModel, coordSys) {
  var realtimeSortOption = seriesModel.get("realtimeSort", true);
  var baseAxis = coordSys.getBaseAxis();
  if (process.env.NODE_ENV !== "production") {
    if (realtimeSortOption) {
      if (baseAxis.type !== "category") {
        log.warn("`realtimeSort` will not work because this bar series is not based on a category axis.");
      }
      if (coordSys.type !== "cartesian2d") {
        log.warn("`realtimeSort` will not work because this bar series is not on cartesian2d.");
      }
    }
  }
  if (realtimeSortOption && baseAxis.type === "category" && coordSys.type === "cartesian2d") {
    return {
      baseAxis,
      otherAxis: coordSys.getOtherAxis(baseAxis)
    };
  }
}
function updateRealtimeAnimation(realtimeSortCfg, seriesAnimationModel, el, layout, newIndex, isHorizontal, isUpdate, isChangeOrder) {
  var seriesTarget;
  var axisTarget;
  if (isHorizontal) {
    axisTarget = {
      x: layout.x,
      width: layout.width
    };
    seriesTarget = {
      y: layout.y,
      height: layout.height
    };
  } else {
    axisTarget = {
      y: layout.y,
      height: layout.height
    };
    seriesTarget = {
      x: layout.x,
      width: layout.width
    };
  }
  if (!isChangeOrder) {
    (isUpdate ? basicTransition.updateProps : basicTransition.initProps)(el, {
      shape: seriesTarget
    }, seriesAnimationModel, newIndex, null);
  }
  var axisAnimationModel = seriesAnimationModel ? realtimeSortCfg.baseAxis.model : null;
  (isUpdate ? basicTransition.updateProps : basicTransition.initProps)(el, {
    shape: axisTarget
  }, axisAnimationModel, newIndex);
}
function checkPropertiesNotValid(obj, props) {
  for (var i = 0; i < props.length; i++) {
    if (!isFinite(obj[props[i]])) {
      return true;
    }
  }
  return false;
}
var rectPropties = ["x", "y", "width", "height"];
var polarPropties = ["cx", "cy", "r", "startAngle", "endAngle"];
var isValidLayout = {
  cartesian2d: function(layout) {
    return !checkPropertiesNotValid(layout, rectPropties);
  },
  polar: function(layout) {
    return !checkPropertiesNotValid(layout, polarPropties);
  }
};
var getLayout = {
  // itemModel is only used to get borderWidth, which is not needed
  // when calculating bar background layout.
  cartesian2d: function(data, dataIndex, itemModel) {
    var layout = data.getItemLayout(dataIndex);
    if (!layout) {
      return null;
    }
    var fixedLineWidth = itemModel ? getLineWidth(itemModel, layout) : 0;
    var signX = layout.width > 0 ? 1 : -1;
    var signY = layout.height > 0 ? 1 : -1;
    return {
      x: layout.x + signX * fixedLineWidth / 2,
      y: layout.y + signY * fixedLineWidth / 2,
      width: layout.width - signX * fixedLineWidth,
      height: layout.height - signY * fixedLineWidth
    };
  },
  polar: function(data, dataIndex, itemModel) {
    var layout = data.getItemLayout(dataIndex);
    return {
      cx: layout.cx,
      cy: layout.cy,
      r0: layout.r0,
      r: layout.r,
      startAngle: layout.startAngle,
      endAngle: layout.endAngle,
      clockwise: layout.clockwise
    };
  }
};
function isZeroOnPolar(layout) {
  return layout.startAngle != null && layout.endAngle != null && layout.startAngle === layout.endAngle;
}
function createPolarPositionMapping(isRadial) {
  return /* @__PURE__ */ function(isRadial2) {
    var arcOrAngle = isRadial2 ? "Arc" : "Angle";
    return function(position) {
      switch (position) {
        case "start":
        case "insideStart":
        case "end":
        case "insideEnd":
          return position + arcOrAngle;
        default:
          return position;
      }
    };
  }(isRadial);
}
function updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, isPolar) {
  var style = data.getItemVisual(dataIndex, "style");
  if (!isPolar) {
    var borderRadius = itemModel.get(["itemStyle", "borderRadius"]) || 0;
    el.setShape("r", borderRadius);
  } else if (!seriesModel.get("roundCap")) {
    var sectorShape = el.shape;
    var cornerRadius = sectorHelper.getSectorCornerRadius(itemModel.getModel("itemStyle"), sectorShape, true);
    util.extend(sectorShape, cornerRadius);
    el.setShape(sectorShape);
  }
  el.useStyle(style);
  var cursorStyle = itemModel.getShallow("cursor");
  cursorStyle && el.attr("cursor", cursorStyle);
  var labelPositionOutside = isPolar ? isHorizontalOrRadial ? layout.r >= layout.r0 ? "endArc" : "startArc" : layout.endAngle >= layout.startAngle ? "endAngle" : "startAngle" : isHorizontalOrRadial ? layout.height >= 0 ? "bottom" : "top" : layout.width >= 0 ? "right" : "left";
  var labelStatesModels = labelStyle.getLabelStatesModels(itemModel);
  labelStyle.setLabelStyle(el, labelStatesModels, {
    labelFetcher: seriesModel,
    labelDataIndex: dataIndex,
    defaultText: labelHelper.getDefaultLabel(seriesModel.getData(), dataIndex),
    inheritColor: style.fill,
    defaultOpacity: style.opacity,
    defaultOutsidePosition: labelPositionOutside
  });
  var label = el.getTextContent();
  if (isPolar && label) {
    var position = itemModel.get(["label", "position"]);
    el.textConfig.inside = position === "middle" ? true : null;
    sectorLabel.setSectorTextRotation(el, position === "outside" ? labelPositionOutside : position, createPolarPositionMapping(isHorizontalOrRadial), itemModel.get(["label", "rotate"]));
  }
  labelStyle.setLabelValueAnimation(label, labelStatesModels, seriesModel.getRawValue(dataIndex), function(value) {
    return labelHelper.getDefaultInterpolatedLabel(data, value);
  });
  var emphasisModel = itemModel.getModel(["emphasis"]);
  states.toggleHoverEmphasis(el, emphasisModel.get("focus"), emphasisModel.get("blurScope"), emphasisModel.get("disabled"));
  states.setStatesStylesFromModel(el, itemModel);
  if (isZeroOnPolar(layout)) {
    el.style.fill = "none";
    el.style.stroke = "none";
    util.each(el.states, function(state) {
      if (state.style) {
        state.style.fill = state.style.stroke = "none";
      }
    });
  }
}
function getLineWidth(itemModel, rawLayout) {
  var borderColor = itemModel.get(["itemStyle", "borderColor"]);
  if (!borderColor || borderColor === "none") {
    return 0;
  }
  var lineWidth = itemModel.get(["itemStyle", "borderWidth"]) || 0;
  var width = isNaN(rawLayout.width) ? Number.MAX_VALUE : Math.abs(rawLayout.width);
  var height = isNaN(rawLayout.height) ? Number.MAX_VALUE : Math.abs(rawLayout.height);
  return Math.min(lineWidth, width, height);
}
var LagePathShape = (
  /** @class */
  /* @__PURE__ */ function() {
    function LagePathShape2() {
    }
    return LagePathShape2;
  }()
);
var LargePath = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(LargePath2, _super);
    function LargePath2(opts) {
      var _this = _super.call(this, opts) || this;
      _this.type = "largeBar";
      return _this;
    }
    LargePath2.prototype.getDefaultShape = function() {
      return new LagePathShape();
    };
    LargePath2.prototype.buildPath = function(ctx, shape) {
      var points = shape.points;
      var baseDimIdx = this.baseDimIdx;
      var valueDimIdx = 1 - this.baseDimIdx;
      var startPoint = [];
      var size = [];
      var barWidth = this.barWidth;
      for (var i = 0; i < points.length; i += 3) {
        size[baseDimIdx] = barWidth;
        size[valueDimIdx] = points[i + 2];
        startPoint[baseDimIdx] = points[i + baseDimIdx];
        startPoint[valueDimIdx] = points[i + valueDimIdx];
        ctx.rect(startPoint[0], startPoint[1], size[0], size[1]);
      }
    };
    return LargePath2;
  }(Path.default)
);
function createLarge(seriesModel, group, progressiveEls, incremental) {
  var data = seriesModel.getData();
  var baseDimIdx = data.getLayout("valueAxisHorizontal") ? 1 : 0;
  var largeDataIndices = data.getLayout("largeDataIndices");
  var barWidth = data.getLayout("size");
  var backgroundModel = seriesModel.getModel("backgroundStyle");
  var bgPoints = data.getLayout("largeBackgroundPoints");
  if (bgPoints) {
    var bgEl = new LargePath({
      shape: {
        points: bgPoints
      },
      incremental: !!incremental,
      silent: true,
      z2: 0
    });
    bgEl.baseDimIdx = baseDimIdx;
    bgEl.largeDataIndices = largeDataIndices;
    bgEl.barWidth = barWidth;
    bgEl.useStyle(backgroundModel.getItemStyle());
    group.add(bgEl);
    progressiveEls && progressiveEls.push(bgEl);
  }
  var el = new LargePath({
    shape: {
      points: data.getLayout("largePoints")
    },
    incremental: !!incremental,
    ignoreCoarsePointer: true,
    z2: 1
  });
  el.baseDimIdx = baseDimIdx;
  el.largeDataIndices = largeDataIndices;
  el.barWidth = barWidth;
  group.add(el);
  el.useStyle(data.getVisual("style"));
  el.style.stroke = null;
  innerStore.getECData(el).seriesIndex = seriesModel.seriesIndex;
  if (!seriesModel.get("silent")) {
    el.on("mousedown", largePathUpdateDataIndex);
    el.on("mousemove", largePathUpdateDataIndex);
  }
  progressiveEls && progressiveEls.push(el);
}
var largePathUpdateDataIndex = throttle.throttle(function(event) {
  var largePath = this;
  var dataIndex = largePathFindDataIndex(largePath, event.offsetX, event.offsetY);
  innerStore.getECData(largePath).dataIndex = dataIndex >= 0 ? dataIndex : null;
}, 30, false);
function largePathFindDataIndex(largePath, x, y) {
  var baseDimIdx = largePath.baseDimIdx;
  var valueDimIdx = 1 - baseDimIdx;
  var points = largePath.shape.points;
  var largeDataIndices = largePath.largeDataIndices;
  var startPoint = [];
  var size = [];
  var barWidth = largePath.barWidth;
  for (var i = 0, len = points.length / 3; i < len; i++) {
    var ii = i * 3;
    size[baseDimIdx] = barWidth;
    size[valueDimIdx] = points[ii + 2];
    startPoint[baseDimIdx] = points[ii + baseDimIdx];
    startPoint[valueDimIdx] = points[ii + valueDimIdx];
    if (size[valueDimIdx] < 0) {
      startPoint[valueDimIdx] += size[valueDimIdx];
      size[valueDimIdx] = -size[valueDimIdx];
    }
    if (x >= startPoint[0] && x <= startPoint[0] + size[0] && y >= startPoint[1] && y <= startPoint[1] + size[1]) {
      return largeDataIndices[i];
    }
  }
  return -1;
}
function createBackgroundShape(isHorizontalOrRadial, layout, coord) {
  if (CoordinateSystem.isCoordinateSystemType(coord, "cartesian2d")) {
    var rectShape = layout;
    var coordLayout = coord.getArea();
    return {
      x: isHorizontalOrRadial ? rectShape.x : coordLayout.x,
      y: isHorizontalOrRadial ? coordLayout.y : rectShape.y,
      width: isHorizontalOrRadial ? rectShape.width : coordLayout.width,
      height: isHorizontalOrRadial ? coordLayout.height : rectShape.height
    };
  } else {
    var coordLayout = coord.getArea();
    var sectorShape = layout;
    return {
      cx: coordLayout.cx,
      cy: coordLayout.cy,
      r0: isHorizontalOrRadial ? coordLayout.r0 : sectorShape.r0,
      r: isHorizontalOrRadial ? coordLayout.r : sectorShape.r,
      startAngle: isHorizontalOrRadial ? sectorShape.startAngle : 0,
      endAngle: isHorizontalOrRadial ? sectorShape.endAngle : Math.PI * 2
    };
  }
}
function createBackgroundEl(coord, isHorizontalOrRadial, layout) {
  var ElementClz = coord.type === "polar" ? Sector.default : Rect.default;
  return new ElementClz({
    shape: createBackgroundShape(isHorizontalOrRadial, layout, coord),
    silent: true,
    z2: 0
  });
}
exports.default = BarView;

import InsideZoomModel from "./InsideZoomModel.js";
import InsideZoomView from "./InsideZoomView.js";
import { installDataZoomRoamProcessor } from "./roams.js";
import installCommon from "./installCommon.js";
function install(registers) {
  installCommon(registers);
  registers.registerComponentModel(InsideZoomModel);
  registers.registerComponentView(InsideZoomView);
  installDataZoomRoamProcessor(registers);
}
export {
  install
};

import { defineComponent, computed, createElementBlock, openBlock, normalizeStyle, normalizeClass, unref, createElementVNode, toDisplayString, createCommentVNode, renderSlot, createTextVNode, createVNode } from "vue";
import dayjs from "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js";
import windIcon from "./assets/wind.png.js";
import _sfc_main$1 from "./WeatherIcon.vue.js";
import { getPrefixCls } from "../../config/index.js";
const _hoisted_1 = { class: "weather-container" };
const _hoisted_2 = { class: "weather-top" };
const _hoisted_3 = { class: "temperature-section" };
const _hoisted_4 = { class: "weather-info" };
const _hoisted_5 = { class: "weather-status" };
const _hoisted_6 = { class: "weather-text" };
const _hoisted_7 = ["src"];
const _hoisted_8 = { class: "date-section" };
const _hoisted_9 = { class: "date-line" };
const _hoisted_10 = { class: "forecast" };
const _hoisted_11 = { class: "weather-decoration" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbWeather"
  },
  __name: "index",
  props: {
    data: {},
    layout: { default: "horizontal" },
    currentDate: {},
    currentWeekday: {},
    forecast: {},
    showWindIcon: { type: Boolean, default: true },
    windIcon: { default: windIcon },
    iconSize: { default: () => ({ width: 120, height: 100 }) },
    customClass: {},
    customStyle: {}
  },
  setup(__props, { expose: __expose }) {
    const prefixCls = getPrefixCls("weather");
    const date = computed(() => {
      return dayjs().format("M月D号");
    });
    const weekday = computed(() => {
      const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
      return weekdays[dayjs().day()];
    });
    __expose({
      WeatherIcon: _sfc_main$1
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([
          unref(prefixCls),
          "w-full",
          `layout-${_ctx.layout}`,
          _ctx.layout === "vertical" ? "h-auto" : "h-105px",
          _ctx.customClass
        ]),
        style: normalizeStyle(_ctx.customStyle)
      }, [
        createElementVNode("div", _hoisted_1, [
          createElementVNode("div", _hoisted_2, [
            createElementVNode("div", _hoisted_3, toDisplayString(_ctx.data.temp || "-") + "℃", 1),
            createElementVNode("div", _hoisted_4, [
              createElementVNode("div", _hoisted_5, [
                createElementVNode("span", _hoisted_6, [
                  renderSlot(_ctx.$slots, "weather-text", {}, () => [
                    createTextVNode(toDisplayString(_ctx.data.text + `，风力${_ctx.data.windScale || " - "}级`), 1)
                  ])
                ]),
                _ctx.showWindIcon ? (openBlock(), createElementBlock("img", {
                  key: 0,
                  src: unref(windIcon),
                  class: "wind-icon"
                }, null, 8, _hoisted_7)) : createCommentVNode("", true)
              ])
            ])
          ]),
          createElementVNode("div", _hoisted_8, [
            createElementVNode("div", _hoisted_9, [
              renderSlot(_ctx.$slots, "date-line", {}, () => [
                createTextVNode(toDisplayString(_ctx.currentDate || date.value) + "，" + toDisplayString(_ctx.currentWeekday || weekday.value), 1)
              ])
            ]),
            createElementVNode("div", _hoisted_10, [
              renderSlot(_ctx.$slots, "forecast", {}, () => [
                createTextVNode(toDisplayString(_ctx.forecast), 1)
              ])
            ])
          ])
        ]),
        createElementVNode("div", _hoisted_11, [
          renderSlot(_ctx.$slots, "weather-icon", {}, () => [
            createVNode(_sfc_main$1, {
              code: _ctx.data.icon || "100",
              width: _ctx.iconSize.width,
              height: _ctx.iconSize.height
            }, null, 8, ["code", "width", "height"])
          ])
        ])
      ], 6);
    };
  }
});
export {
  _sfc_main as default
};

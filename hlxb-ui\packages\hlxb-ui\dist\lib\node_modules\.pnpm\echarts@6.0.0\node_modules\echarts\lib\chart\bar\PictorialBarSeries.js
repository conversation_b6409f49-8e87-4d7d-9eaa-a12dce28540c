"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const BaseBarSeries = require("./BaseBarSeries.js");
const component = require("../../util/component.js");
const tokens = require("../../visual/tokens.js");
var PictorialBarSeriesModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(PictorialBarSeriesModel2, _super);
    function PictorialBarSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = PictorialBarSeriesModel2.type;
      _this.hasSymbolVisual = true;
      _this.defaultSymbol = "roundRect";
      return _this;
    }
    PictorialBarSeriesModel2.prototype.getInitialData = function(option) {
      option.stack = null;
      return _super.prototype.getInitialData.apply(this, arguments);
    };
    PictorialBarSeriesModel2.type = "series.pictorialBar";
    PictorialBarSeriesModel2.dependencies = ["grid"];
    PictorialBarSeriesModel2.defaultOption = component.inheritDefaultOption(BaseBarSeries.default.defaultOption, {
      symbol: "circle",
      symbolSize: null,
      symbolRotate: null,
      symbolPosition: null,
      symbolOffset: null,
      symbolMargin: null,
      symbolRepeat: false,
      symbolRepeatDirection: "end",
      symbolClip: false,
      symbolBoundingData: null,
      symbolPatternSize: 400,
      barGap: "-100%",
      // Pictorial bar do not clip by default because in many cases
      // xAxis and yAxis are not displayed and it's expected not to clip
      clip: false,
      // z can be set in data item, which is z2 actually.
      // Disable progressive
      progressive: 0,
      emphasis: {
        // By default pictorialBar do not hover scale. Hover scale is not suitable
        // for the case that both has foreground and background.
        scale: false
      },
      select: {
        itemStyle: {
          borderColor: tokens.default.color.primary
        }
      }
    });
    return PictorialBarSeriesModel2;
  }(BaseBarSeries.default)
);
exports.default = PictorialBarSeriesModel;

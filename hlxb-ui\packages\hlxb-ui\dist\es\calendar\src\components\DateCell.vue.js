import { defineComponent, computed, createElementBlock, openBlock, normalizeClass, unref, renderSlot, createCommentVNode, createTextVNode, toDisplayString, normalizeStyle } from "vue";
import { dateCellPrefixCls } from "../hooks/prefixCls.js";
import { useCalendarContext } from "../hooks/useCalendarContext.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "DateCell"
  },
  __name: "DateCell",
  props: {
    dateInfo: {}
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const { calendarContext, options } = useCalendarContext();
    const emit = __emit;
    const handleClick = () => {
      emit("click", props.dateInfo.date, props.dateInfo);
    };
    const customClass = computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.cellClass) {
        return options.value.cellClass(props.dateInfo);
      }
      return "";
    });
    const cellContent = computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.cellContent) {
        return options.value.cellContent(props.dateInfo);
      }
      return props.dateInfo.date.date().toString();
    });
    const showIndicator = computed(() => {
      var _a;
      if ((_a = options.value) == null ? void 0 : _a.hasIndicator) {
        return options.value.hasIndicator(props.dateInfo.dateString);
      }
      return false;
    });
    const indicatorPositionClass = computed(() => {
      var _a, _b;
      const position = ((_b = (_a = options.value) == null ? void 0 : _a.indicatorConfig) == null ? void 0 : _b.position) || "bottom-center";
      return `indicator-${position}`;
    });
    const indicatorStyle = computed(() => {
      var _a;
      const config = ((_a = options.value) == null ? void 0 : _a.indicatorConfig) || {};
      const defaultStyle = {
        backgroundColor: config.color || "#fc7c22",
        width: `${config.size || 4}px`,
        height: `${config.size || 4}px`
      };
      return {
        ...defaultStyle,
        ...config.style
      };
    });
    return (_ctx, _cache) => {
      var _a;
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([
          unref(dateCellPrefixCls),
          {
            "current-month": _ctx.dateInfo.isCurrentMonth,
            "other-month": !_ctx.dateInfo.isCurrentMonth,
            selected: _ctx.dateInfo.date.isSame(unref(calendarContext).selectedDate, "day"),
            today: ((_a = unref(options)) == null ? void 0 : _a.showToday) && _ctx.dateInfo.isToday
          },
          customClass.value
        ]),
        onClick: handleClick
      }, [
        renderSlot(_ctx.$slots, "date-cell", { dateItem: _ctx.dateInfo }, () => [
          createTextVNode(toDisplayString(cellContent.value), 1)
        ]),
        showIndicator.value ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(["calendar-indicator", indicatorPositionClass.value]),
          style: normalizeStyle(indicatorStyle.value)
        }, null, 6)) : createCommentVNode("", true)
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

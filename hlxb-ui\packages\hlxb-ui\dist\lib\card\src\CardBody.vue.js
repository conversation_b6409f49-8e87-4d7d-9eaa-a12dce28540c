"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const data = require("./data.js");
const index = require("../../config/index.js");
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCardBody",
    inheritAttrs: false
  },
  __name: "CardBody",
  props: {
    // 卡片主体的额外类名，类型为字符串，默认值为空字符串
    bodyClassName: {
      type: String,
      default: ""
    },
    // 卡片主体的样式对象，类型为 CSSProperties，默认值为空对象
    bodyStyleDate: {
      type: Object,
      default: () => {
      }
    },
    // 深色模式 浅色 大屏等其他主题色
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("card-body");
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        style: vue.normalizeStyle(__props.bodyStyleDate ? __props.bodyStyleDate : ""),
        class: vue.normalizeClass([vue.unref(prefixCls), __props.bodyClassName ? __props.bodyClassName : "", __props.themeColor])
      }, [
        (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(vue.unref(data.cardBodyDate).slots, (data2) => {
          return vue.renderSlot(_ctx.$slots, data2, { key: data2 });
        }), 128))
      ], 6);
    };
  }
});
exports.default = _sfc_main;

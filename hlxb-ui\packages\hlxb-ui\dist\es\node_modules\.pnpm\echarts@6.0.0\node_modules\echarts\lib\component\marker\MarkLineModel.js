import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import MarkerModel from "./MarkerModel.js";
var MarkLineModel = (
  /** @class */
  function(_super) {
    __extends(MarkLineModel2, _super);
    function MarkLineModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkLineModel2.type;
      return _this;
    }
    MarkLineModel2.prototype.createMarkerModelFromSeries = function(markerOpt, masterMarkerModel, ecModel) {
      return new MarkLineModel2(markerOpt, masterMarkerModel, ecModel);
    };
    MarkLineModel2.type = "markLine";
    MarkLineModel2.defaultOption = {
      // zlevel: 0,
      z: 5,
      symbol: ["circle", "arrow"],
      symbolSize: [8, 16],
      // symbolRotate: 0,
      symbolOffset: 0,
      precision: 2,
      tooltip: {
        trigger: "item"
      },
      label: {
        show: true,
        position: "end",
        distance: 5
      },
      lineStyle: {
        type: "dashed"
      },
      emphasis: {
        label: {
          show: true
        },
        lineStyle: {
          width: 3
        }
      },
      animationEasing: "linear"
    };
    return MarkLineModel2;
  }(MarkerModel)
);
export {
  MarkLineModel as default
};

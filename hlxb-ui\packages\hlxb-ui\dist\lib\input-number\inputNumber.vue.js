"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const inputNumber_vue_vue_type_script_lang = require("./inputNumber.vue2.js");
const vue = require("vue");
;/* empty css                  */
const _pluginVue_exportHelper = require("../_virtual/_plugin-vue_export-helper.js");
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_Hlxb_icon = vue.resolveComponent("Hlxb-icon");
  const _component_HlxbInput = vue.resolveComponent("HlxbInput");
  return vue.openBlock(), vue.createElementBlock("div", null, [
    vue.createVNode(_component_HlxbInput, {
      modelValue: _ctx.inputValue,
      "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => _ctx.inputValue = $event),
      center: "",
      onChange: _ctx.handleChange
    }, {
      prepend: vue.withCtx(() => [
        vue.createElementVNode("div", {
          class: vue.normalizeClass(["cursor-pointer", [{ "is-disabled": _ctx.decreaseDisabled }, _ctx.styleClass]]),
          onClick: _cache[0] || (_cache[0] = ($event) => _ctx.downCount("decrease"))
        }, [
          vue.createVNode(_component_Hlxb_icon, { name: "jian" })
        ], 2)
      ]),
      append: vue.withCtx(() => [
        vue.createElementVNode("div", {
          class: vue.normalizeClass(["cursor-pointer", [{ "is-disabled": _ctx.increaseDisabled }, _ctx.styleClass]]),
          onClick: _cache[1] || (_cache[1] = ($event) => _ctx.addCount("increase"))
        }, [
          vue.createVNode(_component_Hlxb_icon, { name: "jia" })
        ], 2)
      ]),
      _: 1
    }, 8, ["modelValue", "onChange"])
  ]);
}
const inputNumber = /* @__PURE__ */ _pluginVue_exportHelper.default(inputNumber_vue_vue_type_script_lang.default, [["render", _sfc_render]]);
exports.default = inputNumber;

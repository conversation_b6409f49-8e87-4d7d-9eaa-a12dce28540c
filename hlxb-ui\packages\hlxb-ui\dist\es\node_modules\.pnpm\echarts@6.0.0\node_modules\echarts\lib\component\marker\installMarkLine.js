import checkMarkerInSeries from "./checkMarkerInSeries.js";
import MarkLineModel from "./MarkLineModel.js";
import MarkLineView from "./MarkLineView.js";
function install(registers) {
  registers.registerComponentModel(MarkLineModel);
  registers.registerComponentView(MarkLineView);
  registers.registerPreprocessor(function(opt) {
    if (checkMarkerInSeries(opt.series, "markLine")) {
      opt.markLine = opt.markLine || {};
    }
  });
}
export {
  install
};

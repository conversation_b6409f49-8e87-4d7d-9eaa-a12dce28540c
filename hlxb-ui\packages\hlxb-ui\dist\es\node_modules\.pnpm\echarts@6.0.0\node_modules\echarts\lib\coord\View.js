import { __extends } from "../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { applyTransform, copy as copy$1 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/vector.js";
import { copy, create, invert } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/matrix.js";
import BoundingRect from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js";
import Transformable from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Transformable.js";
import { parsePercent } from "../util/number.js";
import { clone } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { clampByZoomLimit } from "../component/helper/roamHelper.js";
var v2ApplyTransform = applyTransform;
var View = (
  /** @class */
  function(_super) {
    __extends(View2, _super);
    function View2(name, opt) {
      var _this = _super.call(this) || this;
      _this.type = "view";
      _this.dimensions = ["x", "y"];
      _this._roamTransformable = new Transformable();
      _this._rawTransformable = new Transformable();
      _this.name = name;
      _this._opt = opt;
      return _this;
    }
    View2.prototype.setBoundingRect = function(x, y, width, height) {
      this._rect = new BoundingRect(x, y, width, height);
      this._updateCenterAndZoom();
      return this._rect;
    };
    View2.prototype.getBoundingRect = function() {
      return this._rect;
    };
    View2.prototype.setViewRect = function(x, y, width, height) {
      this._transformTo(x, y, width, height);
      this._viewRect = new BoundingRect(x, y, width, height);
    };
    View2.prototype._transformTo = function(x, y, width, height) {
      var rect = this.getBoundingRect();
      var rawTransform = this._rawTransformable;
      rawTransform.transform = rect.calculateTransform(new BoundingRect(x, y, width, height));
      var rawParent = rawTransform.parent;
      rawTransform.parent = null;
      rawTransform.decomposeTransform();
      rawTransform.parent = rawParent;
      this._updateTransform();
    };
    View2.prototype.setCenter = function(centerCoord) {
      var opt = this._opt;
      if (opt && opt.api && opt.ecModel && opt.ecModel.getShallow("legacyViewCoordSysCenterBase") && centerCoord) {
        centerCoord = [parsePercent(centerCoord[0], opt.api.getWidth()), parsePercent(centerCoord[1], opt.api.getWidth())];
      }
      this._centerOption = clone(centerCoord);
      this._updateCenterAndZoom();
    };
    View2.prototype.setZoom = function(zoom) {
      this._zoom = clampByZoomLimit(zoom || 1, this.zoomLimit);
      this._updateCenterAndZoom();
    };
    View2.prototype.getDefaultCenter = function() {
      var rawRect = this.getBoundingRect();
      var cx = rawRect.x + rawRect.width / 2;
      var cy = rawRect.y + rawRect.height / 2;
      return [cx, cy];
    };
    View2.prototype.getCenter = function() {
      return this._center || this.getDefaultCenter();
    };
    View2.prototype.getZoom = function() {
      return this._zoom || 1;
    };
    View2.prototype.getRoamTransform = function() {
      return this._roamTransformable.getLocalTransform();
    };
    View2.prototype._updateCenterAndZoom = function() {
      var centerOption = this._centerOption;
      var rect = this._rect;
      if (centerOption && rect) {
        this._center = [parsePercent(centerOption[0], rect.width, rect.x), parsePercent(centerOption[1], rect.height, rect.y)];
      }
      var rawTransformMatrix = this._rawTransformable.getLocalTransform();
      var roamTransform = this._roamTransformable;
      var defaultCenter = this.getDefaultCenter();
      var center = this.getCenter();
      var zoom = this.getZoom();
      center = applyTransform([], center, rawTransformMatrix);
      defaultCenter = applyTransform([], defaultCenter, rawTransformMatrix);
      roamTransform.originX = center[0];
      roamTransform.originY = center[1];
      roamTransform.x = defaultCenter[0] - center[0];
      roamTransform.y = defaultCenter[1] - center[1];
      roamTransform.scaleX = roamTransform.scaleY = zoom;
      this._updateTransform();
    };
    View2.prototype._updateTransform = function() {
      var roamTransformable = this._roamTransformable;
      var rawTransformable = this._rawTransformable;
      rawTransformable.parent = roamTransformable;
      roamTransformable.updateTransform();
      rawTransformable.updateTransform();
      copy(this.transform || (this.transform = []), rawTransformable.transform || create());
      this._rawTransform = rawTransformable.getLocalTransform();
      this.invTransform = this.invTransform || [];
      invert(this.invTransform, this.transform);
      this.decomposeTransform();
    };
    View2.prototype.getTransformInfo = function() {
      var rawTransformable = this._rawTransformable;
      var roamTransformable = this._roamTransformable;
      var dummyTransformable = new Transformable();
      dummyTransformable.transform = roamTransformable.transform;
      dummyTransformable.decomposeTransform();
      return {
        roam: {
          x: dummyTransformable.x,
          y: dummyTransformable.y,
          scaleX: dummyTransformable.scaleX,
          scaleY: dummyTransformable.scaleY
        },
        raw: {
          x: rawTransformable.x,
          y: rawTransformable.y,
          scaleX: rawTransformable.scaleX,
          scaleY: rawTransformable.scaleY
        }
      };
    };
    View2.prototype.getViewRect = function() {
      return this._viewRect;
    };
    View2.prototype.getViewRectAfterRoam = function() {
      var rect = this.getBoundingRect().clone();
      rect.applyTransform(this.transform);
      return rect;
    };
    View2.prototype.dataToPoint = function(data, noRoam, out) {
      var transform = noRoam ? this._rawTransform : this.transform;
      out = out || [];
      return transform ? v2ApplyTransform(out, data, transform) : copy$1(out, data);
    };
    View2.prototype.pointToData = function(point, reserved, out) {
      out = out || [];
      var invTransform = this.invTransform;
      return invTransform ? v2ApplyTransform(out, point, invTransform) : (out[0] = point[0], out[1] = point[1], out);
    };
    View2.prototype.convertToPixel = function(ecModel, finder, value) {
      var coordSys = getCoordSys(finder);
      return coordSys === this ? coordSys.dataToPoint(value) : null;
    };
    View2.prototype.convertFromPixel = function(ecModel, finder, pixel) {
      var coordSys = getCoordSys(finder);
      return coordSys === this ? coordSys.pointToData(pixel) : null;
    };
    View2.prototype.containPoint = function(point) {
      return this.getViewRectAfterRoam().contain(point[0], point[1]);
    };
    View2.dimensions = ["x", "y"];
    return View2;
  }(Transformable)
);
function getCoordSys(finder) {
  var seriesModel = finder.seriesModel;
  return seriesModel ? seriesModel.coordinateSystem : null;
}
export {
  View as default
};

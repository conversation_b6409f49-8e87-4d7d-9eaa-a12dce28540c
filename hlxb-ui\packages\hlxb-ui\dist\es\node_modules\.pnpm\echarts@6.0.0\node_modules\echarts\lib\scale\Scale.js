import { enableClassManagement } from "../util/clazz.js";
import { ScaleCalculator } from "./helper.js";
var Scale = (
  /** @class */
  function() {
    function Scale2(setting) {
      this._calculator = new ScaleCalculator();
      this._setting = setting || {};
      this._extent = [Infinity, -Infinity];
    }
    Scale2.prototype.getSetting = function(name) {
      return this._setting[name];
    };
    Scale2.prototype._innerUnionExtent = function(other) {
      var extent = this._extent;
      this._innerSetExtent(other[0] < extent[0] ? other[0] : extent[0], other[1] > extent[1] ? other[1] : extent[1]);
    };
    Scale2.prototype.unionExtentFromData = function(data, dim) {
      this._innerUnionExtent(data.getApproximateExtent(dim));
    };
    Scale2.prototype.getExtent = function() {
      return this._extent.slice();
    };
    Scale2.prototype.setExtent = function(start, end) {
      this._innerSetExtent(start, end);
    };
    Scale2.prototype._innerSetExtent = function(start, end) {
      var thisExtent = this._extent;
      if (!isNaN(start)) {
        thisExtent[0] = start;
      }
      if (!isNaN(end)) {
        thisExtent[1] = end;
      }
      this._brkCtx && this._brkCtx.update(thisExtent);
    };
    Scale2.prototype.setBreaksFromOption = function(breakOptionList) {
    };
    Scale2.prototype._innerSetBreak = function(parsed) {
      if (this._brkCtx) {
        this._brkCtx.setBreaks(parsed);
        this._calculator.updateMethods(this._brkCtx);
        this._brkCtx.update(this._extent);
      }
    };
    Scale2.prototype._innerGetBreaks = function() {
      return this._brkCtx ? this._brkCtx.breaks : [];
    };
    Scale2.prototype.hasBreaks = function() {
      return this._brkCtx ? this._brkCtx.hasBreaks() : false;
    };
    Scale2.prototype._getExtentSpanWithBreaks = function() {
      return this._brkCtx && this._brkCtx.hasBreaks() ? this._brkCtx.getExtentSpan() : this._extent[1] - this._extent[0];
    };
    Scale2.prototype.isInExtentRange = function(value) {
      return this._extent[0] <= value && this._extent[1] >= value;
    };
    Scale2.prototype.isBlank = function() {
      return this._isBlank;
    };
    Scale2.prototype.setBlank = function(isBlank) {
      this._isBlank = isBlank;
    };
    return Scale2;
  }()
);
enableClassManagement(Scale);
export {
  Scale as default
};

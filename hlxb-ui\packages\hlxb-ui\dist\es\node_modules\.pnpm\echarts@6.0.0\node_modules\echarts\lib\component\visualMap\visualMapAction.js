var visualMapActionInfo = {
  type: "selectDataRange",
  event: "dataRangeSelected",
  // FIXME use updateView appears wrong
  update: "update"
};
var visualMapActionHander = function(payload, ecModel) {
  ecModel.eachComponent({
    mainType: "visualMap",
    query: payload
  }, function(model) {
    model.setSelected(payload.selected);
  });
};
export {
  visualMapActionHander,
  visualMapActionInfo
};

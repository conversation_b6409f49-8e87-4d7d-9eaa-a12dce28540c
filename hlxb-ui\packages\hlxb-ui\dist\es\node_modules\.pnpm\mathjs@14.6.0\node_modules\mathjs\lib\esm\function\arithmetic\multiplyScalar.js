import { factory } from "../../utils/factory.js";
import { multiplyNumber } from "../../plain/number/arithmetic.js";
var name = "multiplyScalar";
var dependencies = ["typed"];
var createMultiplyScalar = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed
  } = _ref;
  return typed("multiplyScalar", {
    "number, number": multiplyNumber,
    "Complex, Complex": function Complex_Complex(x, y) {
      return x.mul(y);
    },
    "BigNumber, BigNumber": function BigNumber_BigNumber(x, y) {
      return x.times(y);
    },
    "bigint, bigint": function bigint_bigint(x, y) {
      return x * y;
    },
    "Fraction, Fraction": function Fraction_Fraction(x, y) {
      return x.mul(y);
    },
    "number | Fraction | BigNumber | Complex, Unit": (x, y) => y.multiply(x),
    "Unit, number | Fraction | BigNumber | Complex | Unit": (x, y) => x.multiply(y)
  });
});
export {
  createMultiplyScalar
};

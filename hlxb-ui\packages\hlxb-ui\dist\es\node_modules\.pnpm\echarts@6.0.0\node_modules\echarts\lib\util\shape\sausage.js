import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import "../graphic.js";
import Path from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Path.js";
var SausageShape = (
  /** @class */
  /* @__PURE__ */ function() {
    function SausageShape2() {
      this.cx = 0;
      this.cy = 0;
      this.r0 = 0;
      this.r = 0;
      this.startAngle = 0;
      this.endAngle = Math.PI * 2;
      this.clockwise = true;
    }
    return SausageShape2;
  }()
);
var SausagePath = (
  /** @class */
  function(_super) {
    __extends(SausagePath2, _super);
    function SausagePath2(opts) {
      var _this = _super.call(this, opts) || this;
      _this.type = "sausage";
      return _this;
    }
    SausagePath2.prototype.getDefaultShape = function() {
      return new SausageShape();
    };
    SausagePath2.prototype.buildPath = function(ctx, shape) {
      var cx = shape.cx;
      var cy = shape.cy;
      var r0 = Math.max(shape.r0 || 0, 0);
      var r = Math.max(shape.r, 0);
      var dr = (r - r0) * 0.5;
      var rCenter = r0 + dr;
      var startAngle = shape.startAngle;
      var endAngle = shape.endAngle;
      var clockwise = shape.clockwise;
      var PI2 = Math.PI * 2;
      var lessThanCircle = clockwise ? endAngle - startAngle < PI2 : startAngle - endAngle < PI2;
      if (!lessThanCircle) {
        startAngle = endAngle - (clockwise ? PI2 : -PI2);
      }
      var unitStartX = Math.cos(startAngle);
      var unitStartY = Math.sin(startAngle);
      var unitEndX = Math.cos(endAngle);
      var unitEndY = Math.sin(endAngle);
      if (lessThanCircle) {
        ctx.moveTo(unitStartX * r0 + cx, unitStartY * r0 + cy);
        ctx.arc(unitStartX * rCenter + cx, unitStartY * rCenter + cy, dr, -Math.PI + startAngle, startAngle, !clockwise);
      } else {
        ctx.moveTo(unitStartX * r + cx, unitStartY * r + cy);
      }
      ctx.arc(cx, cy, r, startAngle, endAngle, !clockwise);
      ctx.arc(unitEndX * rCenter + cx, unitEndY * rCenter + cy, dr, endAngle - Math.PI * 2, endAngle - Math.PI, !clockwise);
      if (r0 !== 0) {
        ctx.arc(cx, cy, r0, endAngle, startAngle, clockwise);
      }
    };
    return SausagePath2;
  }(Path)
);
export {
  SausagePath as default
};

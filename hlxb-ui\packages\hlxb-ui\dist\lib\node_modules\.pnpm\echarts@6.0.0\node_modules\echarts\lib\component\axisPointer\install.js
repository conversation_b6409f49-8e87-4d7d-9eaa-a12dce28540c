"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const AxisView = require("../axis/AxisView.js");
const CartesianAxisPointer = require("./CartesianAxisPointer.js");
const AxisPointerModel = require("./AxisPointerModel.js");
const AxisPointerView = require("./AxisPointerView.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const modelHelper = require("./modelHelper.js");
const axisTrigger = require("./axisTrigger.js");
function install(registers) {
  AxisView.default.registerAxisPointerClass("CartesianAxisPointer", CartesianAxisPointer.default);
  registers.registerComponentModel(AxisPointerModel.default);
  registers.registerComponentView(AxisPointerView.default);
  registers.registerPreprocessor(function(option) {
    if (option) {
      (!option.axisPointer || option.axisPointer.length === 0) && (option.axisPointer = {});
      var link = option.axisPointer.link;
      if (link && !util.isArray(link)) {
        option.axisPointer.link = [link];
      }
    }
  });
  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, function(ecModel, api) {
    ecModel.getComponent("axisPointer").coordSysAxesInfo = modelHelper.collect(ecModel, api);
  });
  registers.registerAction({
    type: "updateAxisPointer",
    event: "updateAxisPointer",
    update: ":updateAxisPointer"
  }, axisTrigger.default);
}
exports.install = install;

import { defineComponent, getCurrentInstance, computed, unref } from "vue";
import { getPrefixCls } from "../config/index.js";
const _sfc_main = defineComponent({
  name: "HlxbInputNumber",
  props: {
    modelValue: {
      type: [Number, String],
      default: ""
    },
    step: {
      type: Number,
      default: 1
    },
    max: {
      type: Number,
      default: Infinity
    },
    min: {
      type: Number,
      default: -Infinity
    },
    disabled: Boolean,
    // 保留位数
    precision: {
      type: Number
    }
  },
  emit: ["update:modelValue"],
  setup(props, { emit }) {
    const instance = getCurrentInstance();
    const prefixCls = getPrefixCls("input-number");
    const styleClass = computed(() => {
      return {
        [`${prefixCls}`]: true
      };
    });
    const inputValue = computed({
      get() {
        return props.modelValue;
      },
      set(val) {
        let { max, min } = props;
        const isNumber = (num) => {
          return !isNaN(num * 1) && Object.prototype.toString.call(num * 1) === "[object Number]";
        };
        console.log("val", val, !isNumber(val));
        let limit = [
          {
            validate: (value) => !isNumber(value),
            res: inputValue.value
          },
          {
            validate: (value) => value >= max,
            res: max
          },
          {
            validate: (value) => value <= min,
            res: min
          },
          {
            validate: (_) => true,
            res: val
          }
        ];
        let _value = limit.find((v) => v.validate(val)).res;
        console.log("_value", _value);
        emit("update:modelValue", _value.toFixed(props.precision));
      }
    });
    const decreaseDisabled = computed(() => {
      return props.disabled || Number(inputValue.value) <= props.min;
    });
    const increaseDisabled = computed(() => {
      return props.disabled || Number(inputValue.value) >= props.max;
    });
    const countHandler = (type) => {
      let num;
      let valTemp = Number(unref(inputValue));
      if (type === "increase") {
        num = valTemp + props.step;
      } else {
        num = valTemp - props.step;
      }
      console.log("num", num);
      return num;
    };
    const addCount = (type) => {
      if (increaseDisabled.value) return;
      inputValue.value = countHandler(type);
    };
    const downCount = (type) => {
      if (decreaseDisabled.value) return;
      inputValue.value = countHandler(type);
    };
    const handleChange = () => {
      var _a;
      (_a = instance == null ? void 0 : instance.proxy) == null ? void 0 : _a.$forceUpdate();
    };
    return {
      styleClass,
      inputValue,
      decreaseDisabled,
      increaseDisabled,
      addCount,
      downCount,
      handleChange
    };
  }
});
export {
  _sfc_main as default
};

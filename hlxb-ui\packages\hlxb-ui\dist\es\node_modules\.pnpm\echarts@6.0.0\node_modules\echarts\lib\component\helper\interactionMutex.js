import { registerAction } from "../../core/echarts.js";
import { noop } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { makeInner } from "../../util/model.js";
var inner = makeInner();
function take(zr, resourceKey, userKey) {
  inner(zr)[resourceKey] = userKey;
}
function release(zr, resourceKey, userKey) {
  var store = inner(zr);
  var uKey = store[resourceKey];
  if (uKey === userKey) {
    store[resourceKey] = null;
  }
}
function isTaken(zr, resourceKey) {
  return !!inner(zr)[resourceKey];
}
registerAction({
  type: "takeGlobalCursor",
  event: "globalCursorTaken",
  update: "update"
}, noop);
export {
  isTaken,
  release,
  take
};

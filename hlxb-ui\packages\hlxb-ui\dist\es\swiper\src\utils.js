const moduleMap = {
  A11y: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["A11y"],
  Autoplay: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Autoplay"],
  Controller: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Controller"],
  EffectCards: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectCards"],
  EffectCoverflow: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectCoverflow"],
  EffectCreative: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectCreative"],
  EffectCube: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectCube"],
  EffectFade: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectFade"],
  EffectFlip: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["EffectFlip"],
  FreeMode: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["FreeMode"],
  Grid: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Grid"],
  HashNavigation: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["HashNavigation"],
  History: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["History"],
  Keyboard: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Keyboard"],
  Manipulation: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Manipulation"],
  Mousewheel: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Mousewheel"],
  Navigation: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Navigation"],
  Pagination: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Pagination"],
  Parallax: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Parallax"],
  Scrollbar: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Scrollbar"],
  Thumbs: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Thumbs"],
  Virtual: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Virtual"],
  Zoom: async () => (await import("../../node_modules/.pnpm/swiper@10.3.1/node_modules/swiper/modules/index.js"))["Zoom"]
};
const loadSwiperModules = async (moduleNames) => {
  try {
    const modules = await Promise.all(
      moduleNames.map(async (moduleName) => {
        const moduleLoader = moduleMap[moduleName];
        return await moduleLoader();
      })
    );
    return modules;
  } catch (error) {
    console.error("加载 Swiper 模块失败:", error);
    return [];
  }
};
const moduleConfigMap = {
  Pagination: {
    pagination: {
      el: ".swiper-pagination",
      clickable: true
    }
  },
  Navigation: {
    navigation: {
      prevEl: ".swiper-button-prev",
      nextEl: ".swiper-button-next"
    }
  },
  Scrollbar: {
    scrollbar: {
      el: ".swiper-scrollbar",
      draggable: true
    }
  },
  Autoplay: {
    autoplay: {
      delay: 3e3,
      disableOnInteraction: false
    }
  }
};
export {
  loadSwiperModules,
  moduleConfigMap,
  moduleMap
};

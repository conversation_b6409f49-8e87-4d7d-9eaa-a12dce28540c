"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const index = require("../utils/index.js");
const CardHeader_vue_vue_type_script_setup_true_lang = require("./src/CardHeader.vue.js");
;/* empty css                     */
const CardBody_vue_vue_type_script_setup_true_lang = require("./src/CardBody.vue.js");
;/* empty css                   */
const index_vue_vue_type_script_setup_true_lang = require("./src/index.vue.js");
;/* empty css                */
const HlxbCard = index.withInstall(index_vue_vue_type_script_setup_true_lang.default);
const HlxbCardBody = index.withInstall(CardBody_vue_vue_type_script_setup_true_lang.default);
const HlxbCardHeader = index.withInstall(CardHeader_vue_vue_type_script_setup_true_lang.default);
exports.HlxbCard = HlxbCard;
exports.HlxbCardBody = HlxbCardBody;
exports.HlxbCardHeader = HlxbCardHeader;

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const antDesignVue = require("ant-design-vue");
const index$1 = require("../../../config/index.js");
const index = require("../../../node_modules/.pnpm/@vueuse_shared@13.7.0_vue@3.5.20_typescript@4.9.5_/node_modules/@vueuse/shared/index.js");
const _hoisted_1 = { class: "total-list" };
const _hoisted_2 = {
  key: 0,
  class: "number-value",
  style: { "font-weight": "400" }
};
const _hoisted_3 = {
  key: 2,
  class: "unit"
};
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbHorizontaSmallSquare"
  },
  __name: "HorizontaSmallSquare",
  props: {
    className: {
      type: String,
      default: "horizonta-small-square"
    },
    pleftType: {
      type: Boolean,
      // value是否有左内间距
      default: false
    },
    classType: {
      type: Boolean,
      // value name是否有换行
      default: false
    },
    nameBefore: {
      // name 左边是否有小圆圈
      type: Boolean,
      default: false
    },
    dataList: {
      type: Array,
      default: () => []
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index$1.getPrefixCls("horizonta-small-square");
    const props = __props;
    function showTooltip(e) {
      if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none";
      }
    }
    async function getIndicatorList() {
    }
    const { pause, resume } = index.useIntervalFn(getIndicatorList, 60 * 1e3);
    vue.watch(
      () => props.dataList,
      (newVal) => {
        if (!newVal) {
          pause();
        }
      }
    );
    vue.onMounted(() => {
      resume();
    });
    vue.onActivated(() => {
      resume();
    });
    vue.onUnmounted(() => {
      pause();
    });
    vue.onDeactivated(() => {
      pause();
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        vue.createElementVNode("div", _hoisted_1, [
          (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(__props.dataList, (item, index2) => {
            return vue.openBlock(), vue.createElementBlock("div", {
              class: "item",
              key: index2
            }, [
              vue.createElementVNode("div", {
                class: vue.normalizeClass(["content", __props.classType ? "content-column" : ""])
              }, [
                vue.createElementVNode("div", {
                  class: vue.normalizeClass(["name", __props.nameBefore ? `beforecor_${index2}` : ""])
                }, vue.toDisplayString(item.indexName), 3),
                vue.createElementVNode("div", {
                  class: vue.normalizeClass(["value", __props.pleftType ? `value_pleft` : ""])
                }, [
                  item.value === null ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_2, "-")) : (vue.openBlock(), vue.createBlock(vue.unref(antDesignVue.Tooltip), {
                    key: 1,
                    onMouseenter: showTooltip
                  }, {
                    title: vue.withCtx(() => [
                      vue.createTextVNode(vue.toDisplayString(item.value) + vue.toDisplayString(item.unitName), 1)
                    ]),
                    default: vue.withCtx(() => [
                      vue.createElementVNode("div", {
                        class: "number-value",
                        style: vue.normalizeStyle({ color: item.color ? item.color : "unset" })
                      }, vue.toDisplayString(item.value ? item.value : ""), 5)
                    ]),
                    _: 2
                  }, 1024)),
                  item.value !== null ? (vue.openBlock(), vue.createElementBlock("span", _hoisted_3, vue.toDisplayString(item.unitName), 1)) : vue.createCommentVNode("", true)
                ], 2)
              ], 2)
            ]);
          }), 128))
        ])
      ], 2);
    };
  }
});
exports.default = _sfc_main;

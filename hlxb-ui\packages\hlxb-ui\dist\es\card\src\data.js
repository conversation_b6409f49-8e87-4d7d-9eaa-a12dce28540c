const cardHeaderDate = {
  slots: ["headerLeftBefore", "headerLeftAfter", "headerRight"]
};
const cardBodyDate = {
  slots: ["defaultBody", "bodyTop", "bodyBottom"]
};
function filterAllowedSlots(slots, cardHeaderDate2) {
  const allowedSlots = new Set(cardHeaderDate2.slots);
  return Object.keys(slots).filter(allowedSlots.has, allowedSlots);
}
export {
  cardBodyDate,
  cardHeaderDate,
  filterAllowedSlots
};

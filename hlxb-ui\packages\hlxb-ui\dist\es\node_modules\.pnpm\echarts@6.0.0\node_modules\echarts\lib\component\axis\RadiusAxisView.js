import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each, defaults } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import * as graphic from "../../util/graphic.js";
import { groupTransition, mergePath } from "../../util/graphic.js";
import AxisBuilder from "./AxisBuilder.js";
import AxisView from "./AxisView.js";
import Group from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
import Sector from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Sector.js";
import Circle from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Circle.js";
var selfBuilderAttrs = ["splitLine", "splitArea", "minorSplitLine"];
var RadiusAxisView = (
  /** @class */
  function(_super) {
    __extends(RadiusAxisView2, _super);
    function RadiusAxisView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = RadiusAxisView2.type;
      _this.axisPointerClass = "PolarAxisPointer";
      return _this;
    }
    RadiusAxisView2.prototype.render = function(radiusAxisModel, ecModel, api) {
      this.group.removeAll();
      if (!radiusAxisModel.get("show")) {
        return;
      }
      var oldAxisGroup = this._axisGroup;
      var newAxisGroup = this._axisGroup = new Group();
      this.group.add(newAxisGroup);
      var radiusAxis = radiusAxisModel.axis;
      var polar = radiusAxis.polar;
      var angleAxis = polar.getAngleAxis();
      var ticksCoords = radiusAxis.getTicksCoords();
      var minorTicksCoords = radiusAxis.getMinorTicksCoords();
      var axisAngle = angleAxis.getExtent()[0];
      var radiusExtent = radiusAxis.getExtent();
      var layout = layoutAxis(polar, radiusAxisModel, axisAngle);
      var axisBuilder = new AxisBuilder(radiusAxisModel, api, layout);
      axisBuilder.build();
      newAxisGroup.add(axisBuilder.group);
      groupTransition(oldAxisGroup, newAxisGroup, radiusAxisModel);
      each(selfBuilderAttrs, function(name) {
        if (radiusAxisModel.get([name, "show"]) && !radiusAxis.scale.isBlank()) {
          axisElementBuilders[name](this.group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords);
        }
      }, this);
    };
    RadiusAxisView2.type = "radiusAxis";
    return RadiusAxisView2;
  }(AxisView)
);
var axisElementBuilders = {
  splitLine: function(group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {
    var splitLineModel = radiusAxisModel.getModel("splitLine");
    var lineStyleModel = splitLineModel.getModel("lineStyle");
    var lineColors = lineStyleModel.get("color");
    var lineCount = 0;
    var angleAxis = polar.getAngleAxis();
    var RADIAN = Math.PI / 180;
    var angleExtent = angleAxis.getExtent();
    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? "Circle" : "Arc";
    lineColors = lineColors instanceof Array ? lineColors : [lineColors];
    var splitLines = [];
    for (var i = 0; i < ticksCoords.length; i++) {
      var colorIndex = lineCount++ % lineColors.length;
      splitLines[colorIndex] = splitLines[colorIndex] || [];
      splitLines[colorIndex].push(new graphic[shapeType]({
        shape: {
          cx: polar.cx,
          cy: polar.cy,
          // ensure circle radius >= 0
          r: Math.max(ticksCoords[i].coord, 0),
          startAngle: -angleExtent[0] * RADIAN,
          endAngle: -angleExtent[1] * RADIAN,
          clockwise: angleAxis.inverse
        }
      }));
    }
    for (var i = 0; i < splitLines.length; i++) {
      group.add(mergePath(splitLines[i], {
        style: defaults({
          stroke: lineColors[i % lineColors.length],
          fill: null
        }, lineStyleModel.getLineStyle()),
        silent: true
      }));
    }
  },
  minorSplitLine: function(group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords) {
    if (!minorTicksCoords.length) {
      return;
    }
    var minorSplitLineModel = radiusAxisModel.getModel("minorSplitLine");
    var lineStyleModel = minorSplitLineModel.getModel("lineStyle");
    var lines = [];
    for (var i = 0; i < minorTicksCoords.length; i++) {
      for (var k = 0; k < minorTicksCoords[i].length; k++) {
        lines.push(new Circle({
          shape: {
            cx: polar.cx,
            cy: polar.cy,
            r: minorTicksCoords[i][k].coord
          }
        }));
      }
    }
    group.add(mergePath(lines, {
      style: defaults({
        fill: null
      }, lineStyleModel.getLineStyle()),
      silent: true
    }));
  },
  splitArea: function(group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {
    if (!ticksCoords.length) {
      return;
    }
    var splitAreaModel = radiusAxisModel.getModel("splitArea");
    var areaStyleModel = splitAreaModel.getModel("areaStyle");
    var areaColors = areaStyleModel.get("color");
    var lineCount = 0;
    areaColors = areaColors instanceof Array ? areaColors : [areaColors];
    var splitAreas = [];
    var prevRadius = ticksCoords[0].coord;
    for (var i = 1; i < ticksCoords.length; i++) {
      var colorIndex = lineCount++ % areaColors.length;
      splitAreas[colorIndex] = splitAreas[colorIndex] || [];
      splitAreas[colorIndex].push(new Sector({
        shape: {
          cx: polar.cx,
          cy: polar.cy,
          r0: prevRadius,
          r: ticksCoords[i].coord,
          startAngle: 0,
          endAngle: Math.PI * 2
        },
        silent: true
      }));
      prevRadius = ticksCoords[i].coord;
    }
    for (var i = 0; i < splitAreas.length; i++) {
      group.add(mergePath(splitAreas[i], {
        style: defaults({
          fill: areaColors[i % areaColors.length]
        }, areaStyleModel.getAreaStyle()),
        silent: true
      }));
    }
  }
};
function layoutAxis(polar, radiusAxisModel, axisAngle) {
  return {
    position: [polar.cx, polar.cy],
    rotation: axisAngle / 180 * Math.PI,
    labelDirection: -1,
    tickDirection: -1,
    nameDirection: 1,
    labelRotate: radiusAxisModel.getModel("axisLabel").get("rotate"),
    // Over splitLine and splitArea
    z2: 1
  };
}
export {
  RadiusAxisView as default
};

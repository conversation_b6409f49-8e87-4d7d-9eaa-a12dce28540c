"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const number = require("../../util/number.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
const Sector = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Sector.js");
const Rect = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js");
const basicTransition = require("../../animation/basicTransition.js");
function createGridClipPath(cartesian, hasAnimation, seriesModel, done, during) {
  var rect = cartesian.getArea();
  var x = rect.x;
  var y = rect.y;
  var width = rect.width;
  var height = rect.height;
  var lineWidth = seriesModel.get(["lineStyle", "width"]) || 0;
  x -= lineWidth / 2;
  y -= lineWidth / 2;
  width += lineWidth;
  height += lineWidth;
  width = Math.ceil(width);
  if (x !== Math.floor(x)) {
    x = Math.floor(x);
    width++;
  }
  var clipPath = new Rect.default({
    shape: {
      x,
      y,
      width,
      height
    }
  });
  if (hasAnimation) {
    var baseAxis = cartesian.getBaseAxis();
    var isHorizontal = baseAxis.isHorizontal();
    var isAxisInversed = baseAxis.inverse;
    if (isHorizontal) {
      if (isAxisInversed) {
        clipPath.shape.x += width;
      }
      clipPath.shape.width = 0;
    } else {
      if (!isAxisInversed) {
        clipPath.shape.y += height;
      }
      clipPath.shape.height = 0;
    }
    var duringCb = util.isFunction(during) ? function(percent) {
      during(percent, clipPath);
    } : null;
    basicTransition.initProps(clipPath, {
      shape: {
        width,
        height,
        x,
        y
      }
    }, seriesModel, null, done, duringCb);
  }
  return clipPath;
}
function createPolarClipPath(polar, hasAnimation, seriesModel) {
  var sectorArea = polar.getArea();
  var r0 = number.round(sectorArea.r0, 1);
  var r = number.round(sectorArea.r, 1);
  var clipPath = new Sector.default({
    shape: {
      cx: number.round(polar.cx, 1),
      cy: number.round(polar.cy, 1),
      r0,
      r,
      startAngle: sectorArea.startAngle,
      endAngle: sectorArea.endAngle,
      clockwise: sectorArea.clockwise
    }
  });
  if (hasAnimation) {
    var isRadial = polar.getBaseAxis().dim === "angle";
    if (isRadial) {
      clipPath.shape.endAngle = sectorArea.startAngle;
    } else {
      clipPath.shape.r = r0;
    }
    basicTransition.initProps(clipPath, {
      shape: {
        endAngle: sectorArea.endAngle,
        r
      }
    }, seriesModel);
  }
  return clipPath;
}
function createClipPath(coordSys, hasAnimation, seriesModel, done, during) {
  if (!coordSys) {
    return null;
  } else if (coordSys.type === "polar") {
    return createPolarClipPath(coordSys, hasAnimation, seriesModel);
  } else if (coordSys.type === "cartesian2d") {
    return createGridClipPath(coordSys, hasAnimation, seriesModel, done, during);
  }
  return null;
}
exports.createClipPath = createClipPath;
exports.createGridClipPath = createGridClipPath;
exports.createPolarClipPath = createPolarClipPath;

import { each, find } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function radarLayout(ecModel) {
  ecModel.eachSeriesByType("radar", function(seriesModel) {
    var data = seriesModel.getData();
    var points = [];
    var coordSys = seriesModel.coordinateSystem;
    if (!coordSys) {
      return;
    }
    var axes = coordSys.getIndicatorAxes();
    each(axes, function(axis, axisIndex) {
      data.each(data.mapDimension(axes[axisIndex].dim), function(val, dataIndex) {
        points[dataIndex] = points[dataIndex] || [];
        var point = coordSys.dataToPoint(val, axisIndex);
        points[dataIndex][axisIndex] = isValidPoint(point) ? point : getValueMissingPoint(coordSys);
      });
    });
    data.each(function(idx) {
      var firstPoint = find(points[idx], function(point) {
        return isValidPoint(point);
      }) || getValueMissingPoint(coordSys);
      points[idx].push(firstPoint.slice());
      data.setItemLayout(idx, points[idx]);
    });
  });
}
function isValidPoint(point) {
  return !isNaN(point[0]) && !isNaN(point[1]);
}
function getValueMissingPoint(coordSys) {
  return [coordSys.cx, coordSys.cy];
}
export {
  radarLayout as default
};

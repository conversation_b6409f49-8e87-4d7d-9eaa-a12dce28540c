.hlxb-weather {
  background: linear-gradient(92deg, #93e9fa 0%, #35abff 100%);
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  color: #333;
  display: flex;
}
.hlxb-weather.layout-vertical {
  flex-direction: column-reverse;
  align-items: center;
}
.hlxb-weather.layout-vertical .weather-container {
  width: fit-content;
  align-items: center;
  padding-top: 12px;
}
.hlxb-weather.layout-vertical .weather-container .date-section {
  width: fit-content;
}
.hlxb-weather.layout-vertical .weather-container .date-section .date-line {
  margin-right: 0;
}
.hlxb-weather.layout-vertical .weather-decoration {
  padding-right: 0;
}
.hlxb-weather .weather-container {
  padding: 20px;
  height: 100%;
  flex: 1;
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.hlxb-weather .weather-container .weather-top {
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .weather-top .temperature-section {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-right: 16px;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status {
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status .weather-text {
  font-size: 16px;
  font-weight: 400;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status .wind-icon {
  width: 33px;
  height: 26px;
  margin-left: 12px;
}
.hlxb-weather .weather-container .date-section {
  width: 100%;
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .date-section .date-line {
  font-size: 14px;
  font-weight: bold;
  margin-right: 24px;
}
.hlxb-weather .weather-container .date-section .forecast {
  font-size: 14px;
}
.hlxb-weather .weather-decoration {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 32px;
}

import { factory } from "../../../utils/factory.js";
var name = "matrix";
var dependencies = ["typed", "Matrix", "DenseMatrix", "SparseMatrix"];
var createMatrix = /* @__PURE__ */ factory(name, dependencies, (_ref) => {
  var {
    typed,
    Matrix,
    DenseMatrix,
    SparseMatrix
  } = _ref;
  return typed(name, {
    "": function _() {
      return _create([]);
    },
    string: function string(format) {
      return _create([], format);
    },
    "string, string": function string_string(format, datatype) {
      return _create([], format, datatype);
    },
    Array: function Array(data) {
      return _create(data);
    },
    Matrix: function Matrix2(data) {
      return _create(data, data.storage());
    },
    "Array | Matrix, string": _create,
    "Array | Matrix, string, string": _create
  });
  function _create(data, format, datatype) {
    if (format === "dense" || format === "default" || format === void 0) {
      return new DenseMatrix(data, datatype);
    }
    if (format === "sparse") {
      return new SparseMatrix(data, datatype);
    }
    throw new TypeError("Unknown matrix type " + JSON.stringify(format) + ".");
  }
});
export {
  createMatrix
};

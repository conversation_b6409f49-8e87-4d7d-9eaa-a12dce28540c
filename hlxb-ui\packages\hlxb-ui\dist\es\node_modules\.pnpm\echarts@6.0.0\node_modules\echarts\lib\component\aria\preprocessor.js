import { each } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function ariaPreprocessor(option) {
  if (!option || !option.aria) {
    return;
  }
  var aria = option.aria;
  if (aria.show != null) {
    aria.enabled = aria.show;
  }
  aria.label = aria.label || {};
  each(["description", "general", "series", "data"], function(name) {
    if (aria[name] != null) {
      aria.label[name] = aria[name];
    }
  });
}
export {
  ariaPreprocessor as default
};

import { factory } from "../../utils/factory.js";
var name = "Matrix";
var dependencies = [];
var createMatrixClass = /* @__PURE__ */ factory(name, dependencies, () => {
  function Matrix() {
    if (!(this instanceof Matrix)) {
      throw new SyntaxError("Constructor must be called with the new operator");
    }
  }
  Matrix.prototype.type = "Matrix";
  Matrix.prototype.isMatrix = true;
  Matrix.prototype.storage = function() {
    throw new Error("Cannot invoke storage on a Matrix interface");
  };
  Matrix.prototype.datatype = function() {
    throw new Error("Cannot invoke datatype on a Matrix interface");
  };
  Matrix.prototype.create = function(data, datatype) {
    throw new Error("Cannot invoke create on a Matrix interface");
  };
  Matrix.prototype.subset = function(index, replacement, defaultValue) {
    throw new Error("Cannot invoke subset on a Matrix interface");
  };
  Matrix.prototype.get = function(index) {
    throw new Error("Cannot invoke get on a Matrix interface");
  };
  Matrix.prototype.set = function(index, value, defaultValue) {
    throw new Error("Cannot invoke set on a Matrix interface");
  };
  Matrix.prototype.resize = function(size, defaultValue) {
    throw new Error("Cannot invoke resize on a Matrix interface");
  };
  Matrix.prototype.reshape = function(size, defaultValue) {
    throw new Error("Cannot invoke reshape on a Matrix interface");
  };
  Matrix.prototype.clone = function() {
    throw new Error("Cannot invoke clone on a Matrix interface");
  };
  Matrix.prototype.size = function() {
    throw new Error("Cannot invoke size on a Matrix interface");
  };
  Matrix.prototype.map = function(callback, skipZeros) {
    throw new Error("Cannot invoke map on a Matrix interface");
  };
  Matrix.prototype.forEach = function(callback) {
    throw new Error("Cannot invoke forEach on a Matrix interface");
  };
  Matrix.prototype[Symbol.iterator] = function() {
    throw new Error("Cannot iterate a Matrix interface");
  };
  Matrix.prototype.toArray = function() {
    throw new Error("Cannot invoke toArray on a Matrix interface");
  };
  Matrix.prototype.valueOf = function() {
    throw new Error("Cannot invoke valueOf on a Matrix interface");
  };
  Matrix.prototype.format = function(options) {
    throw new Error("Cannot invoke format on a Matrix interface");
  };
  Matrix.prototype.toString = function() {
    throw new Error("Cannot invoke toString on a Matrix interface");
  };
  return Matrix;
}, {
  isClass: true
});
export {
  createMatrixClass
};

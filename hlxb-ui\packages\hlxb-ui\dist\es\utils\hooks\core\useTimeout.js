import { watch, ref } from "vue";
import { isFunction } from "../../is.js";
import { tryOnUnmounted } from "../../../node_modules/.pnpm/@vueuse_shared@13.7.0_vue@3.5.20_typescript@4.9.5_/node_modules/@vueuse/shared/index.js";
function useTimeoutFn(handle, wait, native = false) {
  if (!isFunction(handle)) {
    throw new Error("handle is not Function!");
  }
  const { readyRef, stop, start } = useTimeoutRef(wait);
  if (native) {
    handle();
  } else {
    watch(
      readyRef,
      (maturity) => {
        maturity && handle();
      },
      { immediate: false }
    );
  }
  return { readyRef, stop, start };
}
function useTimeoutRef(wait) {
  const readyRef = ref(false);
  let timer;
  function stop() {
    readyRef.value = false;
    timer && window.clearTimeout(timer);
  }
  function start() {
    stop();
    timer = setTimeout(() => {
      readyRef.value = true;
    }, wait);
  }
  start();
  tryOnUnmounted(stop);
  return { readyRef, stop, start };
}
export {
  useTimeoutFn,
  useTimeoutRef
};

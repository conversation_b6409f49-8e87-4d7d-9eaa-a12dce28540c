import { defineComponent, createElementBlock, openBlock, normalizeClass, unref, createVNode, mergeProps, createSlots, withCtx, createBlock, renderList, renderSlot } from "vue";
import { HlxbCard } from "../../../../card/index.js";
import _sfc_main$2 from "../../basicComponents/Ranking.vue.js";
/* empty css                                 */
import _sfc_main$3 from "../../basicComponents/Empty.vue.js";
/* empty css                               */
import _sfc_main$1 from "../../basicComponents/Loading.vue.js";
/* empty css                                 */
import { useFilterSlots } from "../../hooks/index.js";
import { getPrefixCls } from "../../../../config/index.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbRankingPlusCard",
    inheritAttrs: false
  },
  __name: "RankingPlusCard",
  props: {
    // 底部排名数据列表，类型为 pieDataListType 数组，默认值为空数组
    bottomList: {
      type: Array,
      default: () => []
    },
    // 数据是否为空 默认值为 false
    empty: {
      type: Boolean,
      default: false
    },
    // 点击标志位，控制排名项点击行为，类型为布尔值，默认值为 false
    clickFlags: {
      type: Boolean,
      default: false
    },
    // 是否显示加载状态组件，类型为布尔值，默认值为 false
    loading: {
      type: Boolean,
      default: false
    },
    // 颜色模式
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  emits: ["ranking"],
  setup(__props, { emit: __emit }) {
    const prefixCls = getPrefixCls("card-combination-ranking-plus");
    const emit = __emit;
    const setItem = (item) => {
      emit("ranking", item);
    };
    const { filtersSlots } = useFilterSlots();
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.themeColor])
      }, [
        createVNode(unref(HlxbCard), mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), createSlots({
          defaultBody: withCtx(() => [
            __props.loading ? (openBlock(), createBlock(_sfc_main$1, {
              key: 0,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"])) : !__props.empty ? (openBlock(), createBlock(_sfc_main$2, mergeProps({
              key: 1,
              onSetItem: setItem,
              themeColor: __props.themeColor
            }, _ctx.$attrs, {
              dataList: __props.bottomList,
              clickFlags: __props.clickFlags
            }), null, 16, ["themeColor", "dataList", "clickFlags"])) : (openBlock(), createBlock(_sfc_main$3, {
              key: 2,
              themeColor: __props.themeColor
            }, null, 8, ["themeColor"]))
          ]),
          _: 2
        }, [
          renderList(unref(filtersSlots), (item) => {
            return {
              name: item,
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, item)
              ])
            };
          })
        ]), 1040, ["themeColor"])
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

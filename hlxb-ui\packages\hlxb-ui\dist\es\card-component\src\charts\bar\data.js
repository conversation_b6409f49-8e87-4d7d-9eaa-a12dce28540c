import { colorRgba } from "../../data.js";
import { roundAndConvert } from "../../utils/number.js";
function optionFormate(props) {
  let totalNum = 0;
  props.dataList.forEach((item) => {
    totalNum += item.data.reduce((a, b) => {
      let ac = a;
      let bc = b;
      if (isNaN(a)) {
        ac = 0;
      }
      if (isNaN(b)) {
        bc = 0;
      }
      return Number(ac) + Number(bc);
    }, 0);
  });
  const unitName = props.dataList.length ? `单位（${props.dataList[0].unitName}）` : "";
  const unitNameBox = props.dataList.length ? Array.from(new Set(props.dataList.map((item) => item.unitName))) : [];
  const tooltipNameBox = props.dataList.length ? Array.from(
    new Set(
      props.dataList.map((item) => ({ unitName: item.unitName, indexName: item.indexName }))
    )
  ) : [];
  const yAxisBox = unitNameBox.length ? unitNameBox.map((item, index) => {
    return {
      type: "value",
      name: `单位（${item}）`,
      // offset: -5,
      // splitNumber: 5,
      nameTextStyle: {
        color: "#333333",
        fontSize: 14,
        align: index === 0 ? "left" : "right"
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#E9E9E9",
          type: "solid"
        }
      },
      splitLine: {
        show: index === 0 ? true : false,
        lineStyle: {
          color: "#E9E9E9",
          type: "dashed"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#666666",
          fontSize: 14
        }
      }
    };
  }) : [
    {
      type: "value",
      name: unitName,
      // offset: -5,
      nameTextStyle: {
        color: "#333333",
        fontSize: 14,
        align: "left"
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#E9E9E9",
          type: "solid"
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#E9E9E9",
          type: "dashed"
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: true,
        textStyle: {
          color: "#666666",
          fontSize: 14
        }
      }
    }
  ];
  return {
    color: colorRgba,
    tooltip: {
      trigger: "axis",
      appendTo: () => document.body,
      renderMode: "html",
      axisPointer: {
        type: "shadow"
      },
      backgroundColor: "#fff",
      textStyle: {
        color: "#333",
        fontSize: 14,
        // lineHeight: 28,
        // height: 28,
        fontWeight: 400
      },
      borderColor: "transparent",
      formatter: (params) => {
        const item = params.filter((item2) => item2.value !== void 0);
        if (item.length) {
          let htmlStr = ``;
          item.forEach((val, index) => {
            var _a;
            htmlStr += `<div style="color: #999;">${index === 0 ? val.name : ""}</div>
              ${val.marker} <span style="display: inline-block; width: 30px;">${val.seriesName}</span><span  style="display: inline-block; width: 150px; font-weight: bold; text-align: right;">${val.value}${((_a = tooltipNameBox.find((item2) => item2.indexName === val.seriesName)) == null ? void 0 : _a.unitName) ?? ""}</span>`;
          });
          return htmlStr;
        }
        return;
      }
    },
    legend: {
      icon: "circle",
      itemWidth: 12,
      itemHeight: 12,
      itemGap: 24,
      color: colorRgba,
      top: 21,
      data: props.dataList.map((item) => item.indexName),
      textStyle: {
        fontSize: 14,
        color: "#333"
      }
    },
    grid: {
      top: "25%",
      left: "0",
      right: "0",
      bottom: "1%",
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        boundaryGap: true,
        data: props.dataList[0].XAxis,
        axisLine: {
          show: true,
          lineStyle: {
            color: "#E9E9E9",
            type: "solid"
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          textStyle: {
            color: "#666666",
            fontSize: 14
          }
        },
        axisPointer: {
          type: "shadow"
        }
      }
    ],
    yAxis: yAxisBox,
    series: props.dataList.map((item, index) => {
      const data = {
        name: item.indexName,
        type: item.type ? item.type : "bar",
        stack: props.stack,
        label: {
          show: item.seriesLabel ? item.seriesLabel : false,
          position: "top",
          // fontWeight: 600,
          // fontSize: 16,
          color: "#333333",
          formatter: (params) => {
            console.log("params", params.value);
            if (!isNaN(params.value && totalNum > 0)) {
              console.log("params", params.value, Number(params.value) / totalNum);
              return `${roundAndConvert(Number(params.value) / totalNum * 100, 2)}%`;
            }
          }
        },
        // barWidth: 20,
        barMaxWidth: "20%",
        barGap: "10%",
        itemStyle: {
          color: colorRgba[index],
          barBorderRadius: [2, 2, 0, 0]
        },
        data: item.data ? item.data.map((i) => i == null ? null : roundAndConvert(Number(i), 2)) : [],
        yAxisIndex: 0
      };
      if (item.type === "line") {
        data.step = "start";
        data.symbol = "none";
        data.type = "line";
        data.yAxisIndex = 1;
        data.barWidth = 0;
        data.barGap = "%";
      }
      return data;
    }),
    animation: false
  };
}
export {
  optionFormate
};

"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const index = require("../../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js");
function generateFullMonthDates(currentDate, fillPrevNextMonth = true) {
  const year = currentDate.year();
  const month = currentDate.month();
  const today = index.default().startOf("day");
  const firstDay = index.default().year(year).month(month).date(1);
  const lastDay = index.default().year(year).month(month).endOf("month");
  let startDate;
  let endDate;
  if (fillPrevNextMonth) {
    startDate = firstDay.startOf("week");
    endDate = lastDay.endOf("week");
  } else {
    startDate = firstDay;
    endDate = lastDay;
  }
  const dates = [];
  let tempDate = index.default(startDate);
  while (tempDate.isBefore(endDate) || tempDate.isSame(endDate)) {
    const dateString = tempDate.format("YYYY-MM-DD");
    const isCurrentMonth = tempDate.month() === month;
    const isToday = tempDate.isSame(today, "day");
    dates.push({
      date: index.default(tempDate),
      isCurrentMonth,
      isToday,
      dateString
    });
    tempDate = tempDate.add(1, "day");
  }
  return dates;
}
exports.generateFullMonthDates = generateFullMonthDates;

import { __extends } from "../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Displayable from "./Displayable.js";
import { DEFAULT_PATH_STYLE } from "./Path.js";
import { createObject, defaults } from "../core/util.js";
import { DEFAULT_FONT } from "../core/platform.js";
import { tSpanHasStroke, tSpanCreateBoundingRect } from "./helper/parseText.js";
var DEFAULT_TSPAN_STYLE = defaults({
  strokeFirst: true,
  font: DEFAULT_FONT,
  x: 0,
  y: 0,
  textAlign: "left",
  textBaseline: "top",
  miterLimit: 2
}, DEFAULT_PATH_STYLE);
var TSpan = function(_super) {
  __extends(TSpan2, _super);
  function TSpan2() {
    return _super !== null && _super.apply(this, arguments) || this;
  }
  TSpan2.prototype.hasStroke = function() {
    return tSpanHasStroke(this.style);
  };
  TSpan2.prototype.hasFill = function() {
    var style = this.style;
    var fill = style.fill;
    return fill != null && fill !== "none";
  };
  TSpan2.prototype.createStyle = function(obj) {
    return createObject(DEFAULT_TSPAN_STYLE, obj);
  };
  TSpan2.prototype.setBoundingRect = function(rect) {
    this._rect = rect;
  };
  TSpan2.prototype.getBoundingRect = function() {
    if (!this._rect) {
      this._rect = tSpanCreateBoundingRect(this.style);
    }
    return this._rect;
  };
  TSpan2.initDefaultProps = function() {
    var tspanProto = TSpan2.prototype;
    tspanProto.dirtyRectTolerance = 10;
  }();
  return TSpan2;
}(Displayable);
TSpan.prototype.type = "tspan";
export {
  DEFAULT_TSPAN_STYLE,
  TSpan as default
};

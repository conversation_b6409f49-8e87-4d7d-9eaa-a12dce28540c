import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import createSeriesData from "../helper/createSeriesData.js";
import SeriesModel from "../../model/Series.js";
import tokens from "../../visual/tokens.js";
var ScatterSeriesModel = (
  /** @class */
  function(_super) {
    __extends(ScatterSeriesModel2, _super);
    function ScatterSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ScatterSeriesModel2.type;
      _this.hasSymbolVisual = true;
      return _this;
    }
    ScatterSeriesModel2.prototype.getInitialData = function(option, ecModel) {
      return createSeriesData(null, this, {
        useEncodeDefaulter: true
      });
    };
    ScatterSeriesModel2.prototype.getProgressive = function() {
      var progressive = this.option.progressive;
      if (progressive == null) {
        return this.option.large ? 5e3 : this.get("progressive");
      }
      return progressive;
    };
    ScatterSeriesModel2.prototype.getProgressiveThreshold = function() {
      var progressiveThreshold = this.option.progressiveThreshold;
      if (progressiveThreshold == null) {
        return this.option.large ? 1e4 : this.get("progressiveThreshold");
      }
      return progressiveThreshold;
    };
    ScatterSeriesModel2.prototype.brushSelector = function(dataIndex, data, selectors) {
      return selectors.point(data.getItemLayout(dataIndex));
    };
    ScatterSeriesModel2.prototype.getZLevelKey = function() {
      return this.getData().count() > this.getProgressiveThreshold() ? this.id : "";
    };
    ScatterSeriesModel2.type = "series.scatter";
    ScatterSeriesModel2.dependencies = ["grid", "polar", "geo", "singleAxis", "calendar", "matrix"];
    ScatterSeriesModel2.defaultOption = {
      coordinateSystem: "cartesian2d",
      // zlevel: 0,
      z: 2,
      legendHoverLink: true,
      symbolSize: 10,
      // symbolRotate: null,  // 图形旋转控制
      large: false,
      // Available when large is true
      largeThreshold: 2e3,
      // cursor: null,
      itemStyle: {
        opacity: 0.8
        // color: 各异
      },
      emphasis: {
        scale: true
      },
      // If clip the overflow graphics
      // Works on cartesian / polar series
      clip: true,
      select: {
        itemStyle: {
          borderColor: tokens.color.primary
        }
      },
      universalTransition: {
        divideShape: "clone"
      }
      // progressive: null
    };
    return ScatterSeriesModel2;
  }(SeriesModel)
);
export {
  ScatterSeriesModel as default
};

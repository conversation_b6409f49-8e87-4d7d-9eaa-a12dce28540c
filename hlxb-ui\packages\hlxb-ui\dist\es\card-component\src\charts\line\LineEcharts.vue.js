import { defineComponent, ref, watch, createElementBlock, openBlock, createElementVNode } from "vue";
import { useECharts } from "../../../../utils/hooks/useECharts.js";
import { linechartsData } from "../../data.js";
import { optionFormate } from "./data.js";
const _hoisted_1 = { style: { "height": "100%", "width": "100%", "flex": "1" } };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "LineEcharts",
  props: {
    dataList: {
      type: Array,
      default: () => linechartsData
    },
    stack: {
      type: String,
      // 堆叠类型
      default: ""
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const props = __props;
    const chartRef = ref(null);
    const { setOptions } = useECharts(chartRef);
    function handleSetVisitChart() {
      if (props.dataList instanceof Array && props.dataList.length) {
        setOptions(optionFormate(props));
      }
    }
    watch(
      () => props.dataList,
      () => {
        handleSetVisitChart();
      },
      { immediate: true }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createElementVNode("div", {
          ref_key: "chartRef",
          ref: chartRef,
          style: { "height": "100%", "width": "100%" }
        }, null, 512)
      ]);
    };
  }
});
export {
  _sfc_main as default
};

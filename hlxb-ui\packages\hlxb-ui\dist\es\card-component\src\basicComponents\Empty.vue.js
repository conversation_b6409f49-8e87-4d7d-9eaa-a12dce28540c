import { defineComponent, watch, createElementBlock, openBlock, normalizeClass, unref, createElementVNode } from "vue";
import emptyImg from "../assets/images/table-empty.png.js";
import { getPrefixCls } from "../../../config/index.js";
const _hoisted_1 = ["src"];
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbCardEmpty"
  },
  __name: "Empty",
  props: {
    // 主题class
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("card-empty");
    const props = __props;
    watch(
      () => props.themeColor,
      () => {
        console.log("themeColor", props.themeColor);
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass([unref(prefixCls), __props.themeColor])
      }, [
        createElementVNode("img", { src: unref(emptyImg) }, null, 8, _hoisted_1),
        _cache[0] || (_cache[0] = createElementVNode("div", { class: "text-center" }, " 暂无数据", -1))
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};

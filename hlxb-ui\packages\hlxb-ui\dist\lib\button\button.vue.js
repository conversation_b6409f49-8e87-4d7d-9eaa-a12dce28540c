"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const index = require("../config/index.js");
const _hoisted_1 = ["disabled", "icon"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{ name: "HlxbButton" },
  __name: "button",
  props: {
    type: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 圆角
    round: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: ""
    }
  },
  emits: ["click"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const prefixCls = index.getPrefixCls("button");
    const styleClass = vue.computed(() => {
      return {
        [`${prefixCls}`]: true,
        [`r-button--${props.type}`]: props.type,
        "is-round": props.round,
        "is-disabled": props.disabled
      };
    });
    const handleClick = () => {
      emit("click");
    };
    return (_ctx, _cache) => {
      const _component_hlxb_icon = vue.resolveComponent("hlxb-icon");
      return vue.openBlock(), vue.createElementBlock("button", {
        class: vue.normalizeClass(["r-button", styleClass.value]),
        disabled: __props.disabled,
        icon: __props.icon,
        onClick: handleClick
      }, [
        __props.icon ? (vue.openBlock(), vue.createBlock(_component_hlxb_icon, {
          key: 0,
          name: __props.icon
        }, null, 8, ["name"])) : vue.createCommentVNode("", true),
        vue.renderSlot(_ctx.$slots, "default")
      ], 10, _hoisted_1);
    };
  }
});
exports.default = _sfc_main;

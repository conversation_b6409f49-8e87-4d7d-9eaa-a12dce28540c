"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const CardHeader_vue_vue_type_script_setup_true_lang = require("./CardHeader.vue.js");
;/* empty css                 */
const CardBody_vue_vue_type_script_setup_true_lang = require("./CardBody.vue.js");
;/* empty css               */
const data = require("./data.js");
const index = require("../../config/index.js");
const _hoisted_1 = { class: "container-content" };
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCard",
    inheritAttrs: false
  },
  __name: "index",
  props: {
    // 卡片的额外类名，类型为字符串，默认值为空字符串
    className: {
      type: String,
      default: ""
    },
    // 卡片的样式对象，类型为 CSSProperties，默认值为空对象
    styleData: {
      type: Object,
      default: () => ({})
    },
    // 深色模式 浅色 大屏等其他主题色
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("card");
    const slots = vue.useSlots();
    const filterCardHeader = vue.computed(() => {
      return data.filterAllowedSlots(slots, data.cardHeaderDate);
    });
    const filterCardBody = vue.computed(() => {
      return data.filterAllowedSlots(slots, data.cardBodyDate);
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        style: vue.normalizeStyle(Object.keys(__props.styleData).length ? __props.styleData : ""),
        class: vue.normalizeClass([vue.unref(prefixCls), __props.className ? __props.className : "", __props.themeColor])
      }, [
        vue.renderSlot(_ctx.$slots, "default"),
        vue.createVNode(CardHeader_vue_vue_type_script_setup_true_lang.default, vue.mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), vue.createSlots({ _: 2 }, [
          vue.renderList(filterCardHeader.value, (item) => {
            return {
              name: item,
              fn: vue.withCtx(() => [
                vue.renderSlot(_ctx.$slots, item)
              ])
            };
          })
        ]), 1040, ["themeColor"]),
        vue.createElementVNode("div", _hoisted_1, [
          vue.createVNode(CardBody_vue_vue_type_script_setup_true_lang.default, vue.mergeProps(_ctx.$attrs, { themeColor: __props.themeColor }), vue.createSlots({ _: 2 }, [
            vue.renderList(filterCardBody.value, (item) => {
              return {
                name: item,
                fn: vue.withCtx(() => [
                  vue.renderSlot(_ctx.$slots, item)
                ])
              };
            })
          ]), 1040, ["themeColor"])
        ])
      ], 6);
    };
  }
});
exports.default = _sfc_main;

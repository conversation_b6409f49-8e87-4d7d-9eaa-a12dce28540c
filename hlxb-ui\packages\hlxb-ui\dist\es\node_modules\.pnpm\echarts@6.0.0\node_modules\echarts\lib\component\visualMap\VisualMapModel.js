import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { bind, map, each as each$1, isArray as isArray$1, isString, isFunction, merge, clone } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import visualDefault from "../../visual/visualDefault.js";
import VisualMapping from "../../visual/VisualMapping.js";
import { replaceVisualOption, createVisualMappings } from "../../visual/visualSolution.js";
import { queryReferringComponents } from "../../util/model.js";
import { asc as asc$1, linearMap as linearMap$1 } from "../../util/number.js";
import ComponentModel from "../../model/Component.js";
import tokens from "../../visual/tokens.js";
var mapVisual = VisualMapping.mapVisual;
var eachVisual = VisualMapping.eachVisual;
var isArray = isArray$1;
var each = each$1;
var asc = asc$1;
var linearMap = linearMap$1;
var VisualMapModel = (
  /** @class */
  function(_super) {
    __extends(VisualMapModel2, _super);
    function VisualMapModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = VisualMapModel2.type;
      _this.stateList = ["inRange", "outOfRange"];
      _this.replacableOptionKeys = ["inRange", "outOfRange", "target", "controller", "color"];
      _this.layoutMode = {
        type: "box",
        ignoreSize: true
      };
      _this.dataBound = [-Infinity, Infinity];
      _this.targetVisuals = {};
      _this.controllerVisuals = {};
      return _this;
    }
    VisualMapModel2.prototype.init = function(option, parentModel, ecModel) {
      this.mergeDefaultAndTheme(option, ecModel);
    };
    VisualMapModel2.prototype.optionUpdated = function(newOption, isInit) {
      var thisOption = this.option;
      !isInit && replaceVisualOption(thisOption, newOption, this.replacableOptionKeys);
      this.textStyleModel = this.getModel("textStyle");
      this.resetItemSize();
      this.completeVisualOption();
    };
    VisualMapModel2.prototype.resetVisual = function(supplementVisualOption) {
      var stateList = this.stateList;
      supplementVisualOption = bind(supplementVisualOption, this);
      this.controllerVisuals = createVisualMappings(this.option.controller, stateList, supplementVisualOption);
      this.targetVisuals = createVisualMappings(this.option.target, stateList, supplementVisualOption);
    };
    VisualMapModel2.prototype.getItemSymbol = function() {
      return null;
    };
    VisualMapModel2.prototype.getTargetSeriesIndices = function() {
      var optionSeriesId = this.option.seriesId;
      var optionSeriesIndex = this.option.seriesIndex;
      if (optionSeriesIndex == null && optionSeriesId == null) {
        optionSeriesIndex = "all";
      }
      var seriesModels = queryReferringComponents(this.ecModel, "series", {
        index: optionSeriesIndex,
        id: optionSeriesId
      }, {
        useDefault: false,
        enableAll: true,
        enableNone: false
      }).models;
      return map(seriesModels, function(seriesModel) {
        return seriesModel.componentIndex;
      });
    };
    VisualMapModel2.prototype.eachTargetSeries = function(callback, context) {
      each$1(this.getTargetSeriesIndices(), function(seriesIndex) {
        var seriesModel = this.ecModel.getSeriesByIndex(seriesIndex);
        if (seriesModel) {
          callback.call(context, seriesModel);
        }
      }, this);
    };
    VisualMapModel2.prototype.isTargetSeries = function(seriesModel) {
      var is = false;
      this.eachTargetSeries(function(model) {
        model === seriesModel && (is = true);
      });
      return is;
    };
    VisualMapModel2.prototype.formatValueText = function(value, isCategory, edgeSymbols) {
      var option = this.option;
      var precision = option.precision;
      var dataBound = this.dataBound;
      var formatter = option.formatter;
      var isMinMax;
      edgeSymbols = edgeSymbols || ["<", ">"];
      if (isArray$1(value)) {
        value = value.slice();
        isMinMax = true;
      }
      var textValue = isCategory ? value : isMinMax ? [toFixed(value[0]), toFixed(value[1])] : toFixed(value);
      if (isString(formatter)) {
        return formatter.replace("{value}", isMinMax ? textValue[0] : textValue).replace("{value2}", isMinMax ? textValue[1] : textValue);
      } else if (isFunction(formatter)) {
        return isMinMax ? formatter(value[0], value[1]) : formatter(value);
      }
      if (isMinMax) {
        if (value[0] === dataBound[0]) {
          return edgeSymbols[0] + " " + textValue[1];
        } else if (value[1] === dataBound[1]) {
          return edgeSymbols[1] + " " + textValue[0];
        } else {
          return textValue[0] + " - " + textValue[1];
        }
      } else {
        return textValue;
      }
      function toFixed(val) {
        return val === dataBound[0] ? "min" : val === dataBound[1] ? "max" : (+val).toFixed(Math.min(precision, 20));
      }
    };
    VisualMapModel2.prototype.resetExtent = function() {
      var thisOption = this.option;
      var extent = asc([thisOption.min, thisOption.max]);
      this._dataExtent = extent;
    };
    VisualMapModel2.prototype.getDataDimensionIndex = function(data) {
      var optDim = this.option.dimension;
      if (optDim != null) {
        return data.getDimensionIndex(optDim);
      }
      var dimNames = data.dimensions;
      for (var i = dimNames.length - 1; i >= 0; i--) {
        var dimName = dimNames[i];
        var dimInfo = data.getDimensionInfo(dimName);
        if (!dimInfo.isCalculationCoord) {
          return dimInfo.storeDimIndex;
        }
      }
    };
    VisualMapModel2.prototype.getExtent = function() {
      return this._dataExtent.slice();
    };
    VisualMapModel2.prototype.completeVisualOption = function() {
      var ecModel = this.ecModel;
      var thisOption = this.option;
      var base = {
        inRange: thisOption.inRange,
        outOfRange: thisOption.outOfRange
      };
      var target = thisOption.target || (thisOption.target = {});
      var controller = thisOption.controller || (thisOption.controller = {});
      merge(target, base);
      merge(controller, base);
      var isCategory = this.isCategory();
      completeSingle.call(this, target);
      completeSingle.call(this, controller);
      completeInactive.call(this, target, "inRange", "outOfRange");
      completeController.call(this, controller);
      function completeSingle(base2) {
        if (isArray(thisOption.color) && !base2.inRange) {
          base2.inRange = {
            color: thisOption.color.slice().reverse()
          };
        }
        base2.inRange = base2.inRange || {
          color: ecModel.get("gradientColor")
        };
      }
      function completeInactive(base2, stateExist, stateAbsent) {
        var optExist = base2[stateExist];
        var optAbsent = base2[stateAbsent];
        if (optExist && !optAbsent) {
          optAbsent = base2[stateAbsent] = {};
          each(optExist, function(visualData, visualType) {
            if (!VisualMapping.isValidType(visualType)) {
              return;
            }
            var defa = visualDefault.get(visualType, "inactive", isCategory);
            if (defa != null) {
              optAbsent[visualType] = defa;
              if (visualType === "color" && !optAbsent.hasOwnProperty("opacity") && !optAbsent.hasOwnProperty("colorAlpha")) {
                optAbsent.opacity = [0, 0];
              }
            }
          });
        }
      }
      function completeController(controller2) {
        var symbolExists = (controller2.inRange || {}).symbol || (controller2.outOfRange || {}).symbol;
        var symbolSizeExists = (controller2.inRange || {}).symbolSize || (controller2.outOfRange || {}).symbolSize;
        var inactiveColor = this.get("inactiveColor");
        var itemSymbol = this.getItemSymbol();
        var defaultSymbol = itemSymbol || "roundRect";
        each(this.stateList, function(state) {
          var itemSize = this.itemSize;
          var visuals = controller2[state];
          if (!visuals) {
            visuals = controller2[state] = {
              color: isCategory ? inactiveColor : [inactiveColor]
            };
          }
          if (visuals.symbol == null) {
            visuals.symbol = symbolExists && clone(symbolExists) || (isCategory ? defaultSymbol : [defaultSymbol]);
          }
          if (visuals.symbolSize == null) {
            visuals.symbolSize = symbolSizeExists && clone(symbolSizeExists) || (isCategory ? itemSize[0] : [itemSize[0], itemSize[0]]);
          }
          visuals.symbol = mapVisual(visuals.symbol, function(symbol) {
            return symbol === "none" ? defaultSymbol : symbol;
          });
          var symbolSize = visuals.symbolSize;
          if (symbolSize != null) {
            var max_1 = -Infinity;
            eachVisual(symbolSize, function(value) {
              value > max_1 && (max_1 = value);
            });
            visuals.symbolSize = mapVisual(symbolSize, function(value) {
              return linearMap(value, [0, max_1], [0, itemSize[0]], true);
            });
          }
        }, this);
      }
    };
    VisualMapModel2.prototype.resetItemSize = function() {
      this.itemSize = [parseFloat(this.get("itemWidth")), parseFloat(this.get("itemHeight"))];
    };
    VisualMapModel2.prototype.isCategory = function() {
      return !!this.option.categories;
    };
    VisualMapModel2.prototype.setSelected = function(selected) {
    };
    VisualMapModel2.prototype.getSelected = function() {
      return null;
    };
    VisualMapModel2.prototype.getValueState = function(value) {
      return null;
    };
    VisualMapModel2.prototype.getVisualMeta = function(getColorVisual) {
      return null;
    };
    VisualMapModel2.type = "visualMap";
    VisualMapModel2.dependencies = ["series"];
    VisualMapModel2.defaultOption = {
      show: true,
      // zlevel: 0,
      z: 4,
      // seriesIndex: 'all',
      min: 0,
      max: 200,
      left: 0,
      right: null,
      top: null,
      bottom: 0,
      itemWidth: null,
      itemHeight: null,
      inverse: false,
      orient: "vertical",
      backgroundColor: tokens.color.transparent,
      borderColor: tokens.color.borderTint,
      contentColor: tokens.color.theme[0],
      inactiveColor: tokens.color.disabled,
      borderWidth: 0,
      padding: tokens.size.m,
      // 接受数组分别设定上右下左边距，同css
      textGap: 10,
      precision: 0,
      textStyle: {
        color: tokens.color.secondary
        // 值域文字颜色
      }
    };
    return VisualMapModel2;
  }(ComponentModel)
);
export {
  VisualMapModel as default
};

import { each, assert, createHashMap } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { SINGLE_REFERRING } from "../util/model.js";
import { error } from "../util/log.js";
var nonSeriesBoxCoordSysCreators = {};
var normalCoordSysCreators = {};
var CoordinateSystemManager = (
  /** @class */
  function() {
    function CoordinateSystemManager2() {
      this._normalMasterList = [];
      this._nonSeriesBoxMasterList = [];
    }
    CoordinateSystemManager2.prototype.create = function(ecModel, api) {
      this._nonSeriesBoxMasterList = dealCreate(nonSeriesBoxCoordSysCreators, true);
      this._normalMasterList = dealCreate(normalCoordSysCreators, false);
      function dealCreate(creatorMap, canBeNonSeriesBox) {
        var coordinateSystems = [];
        each(creatorMap, function(creator, type) {
          var list = creator.create(ecModel, api);
          coordinateSystems = coordinateSystems.concat(list || []);
          if (process.env.NODE_ENV !== "production") {
            if (canBeNonSeriesBox) {
              each(list, function(master) {
                return assert(!master.update);
              });
            }
          }
        });
        return coordinateSystems;
      }
    };
    CoordinateSystemManager2.prototype.update = function(ecModel, api) {
      each(this._normalMasterList, function(coordSys) {
        coordSys.update && coordSys.update(ecModel, api);
      });
    };
    CoordinateSystemManager2.prototype.getCoordinateSystems = function() {
      return this._normalMasterList.concat(this._nonSeriesBoxMasterList);
    };
    CoordinateSystemManager2.register = function(type, creator) {
      if (type === "matrix" || type === "calendar") {
        nonSeriesBoxCoordSysCreators[type] = creator;
        return;
      }
      normalCoordSysCreators[type] = creator;
    };
    CoordinateSystemManager2.get = function(type) {
      return normalCoordSysCreators[type] || nonSeriesBoxCoordSysCreators[type];
    };
    return CoordinateSystemManager2;
  }()
);
function canBeNonSeriesBoxCoordSys(coordSysType) {
  return !!nonSeriesBoxCoordSysCreators[coordSysType];
}
var BoxCoordinateSystemCoordFrom = {
  // By default fetch coord from `model.get('coord')`.
  coord: 1,
  // Some model/series, such as pie, is allowed to also get coord from `model.get('center')`,
  // if cannot get from `model.get('coord')`. But historically pie use `center` option, but
  // geo use `layoutCenter` option to specify layout center; they are not able to be unified.
  // Therefor it is not recommended.
  coord2: 2
};
function registerLayOutOnCoordSysUsage(opt) {
  if (process.env.NODE_ENV !== "production") {
    assert(!coordSysUseMap.get(opt.fullType));
  }
  coordSysUseMap.set(opt.fullType, {
    getCoord2: void 0
  }).getCoord2 = opt.getCoord2;
}
var coordSysUseMap = createHashMap();
function getCoordForBoxCoordSys(model) {
  var coord = model.getShallow("coord", true);
  var from = BoxCoordinateSystemCoordFrom.coord;
  if (coord == null) {
    var store = coordSysUseMap.get(model.type);
    if (store && store.getCoord2) {
      from = BoxCoordinateSystemCoordFrom.coord2;
      coord = store.getCoord2(model);
    }
  }
  return {
    coord,
    from
  };
}
var CoordinateSystemUsageKind = {
  none: 0,
  dataCoordSys: 1,
  boxCoordSys: 2
};
function decideCoordSysUsageKind(model, printError) {
  var coordSysType = model.getShallow("coordinateSystem");
  var coordSysUsageOption = model.getShallow("coordinateSystemUsage", true);
  var isDeclaredExplicitly = coordSysUsageOption != null;
  var kind = CoordinateSystemUsageKind.none;
  if (coordSysType) {
    var isSeries = model.mainType === "series";
    if (coordSysUsageOption == null) {
      coordSysUsageOption = isSeries ? "data" : "box";
    }
    if (coordSysUsageOption === "data") {
      kind = CoordinateSystemUsageKind.dataCoordSys;
      if (!isSeries) {
        if (process.env.NODE_ENV !== "production") {
          if (isDeclaredExplicitly && printError) {
            error('coordinateSystemUsage "data" is not supported in non-series components.');
          }
        }
        kind = CoordinateSystemUsageKind.none;
      }
    } else if (coordSysUsageOption === "box") {
      kind = CoordinateSystemUsageKind.boxCoordSys;
      if (!isSeries && !canBeNonSeriesBoxCoordSys(coordSysType)) {
        if (process.env.NODE_ENV !== "production") {
          if (isDeclaredExplicitly && printError) {
            error('coordinateSystem "' + coordSysType + '" cannot be used' + (' as coordinateSystemUsage "box" for "' + model.type + '" yet.'));
          }
        }
        kind = CoordinateSystemUsageKind.none;
      }
    }
  }
  return {
    coordSysType,
    kind
  };
}
function injectCoordSysByOption(opt) {
  var targetModel = opt.targetModel, coordSysType = opt.coordSysType, coordSysProvider = opt.coordSysProvider, isDefaultDataCoordSys = opt.isDefaultDataCoordSys, allowNotFound = opt.allowNotFound;
  if (process.env.NODE_ENV !== "production") {
    assert(!!coordSysType);
  }
  var _a = decideCoordSysUsageKind(targetModel, true), kind = _a.kind, declaredType = _a.coordSysType;
  if (isDefaultDataCoordSys && kind !== CoordinateSystemUsageKind.dataCoordSys) {
    kind = CoordinateSystemUsageKind.dataCoordSys;
    declaredType = coordSysType;
  }
  if (kind === CoordinateSystemUsageKind.none || declaredType !== coordSysType) {
    return false;
  }
  var coordSys = coordSysProvider(coordSysType, targetModel);
  if (!coordSys) {
    if (process.env.NODE_ENV !== "production") {
      if (!allowNotFound) {
        error(coordSysType + " cannot be found for" + (" " + targetModel.type + " (index: " + targetModel.componentIndex + ")."));
      }
    }
    return false;
  }
  if (kind === CoordinateSystemUsageKind.dataCoordSys) {
    if (process.env.NODE_ENV !== "production") {
      assert(targetModel.mainType === "series");
    }
    targetModel.coordinateSystem = coordSys;
  } else {
    targetModel.boxCoordinateSystem = coordSys;
  }
  return true;
}
var simpleCoordSysInjectionProvider = function(coordSysType, injectTargetModel) {
  var coordSysModel = injectTargetModel.getReferringComponents(coordSysType, SINGLE_REFERRING).models[0];
  return coordSysModel && coordSysModel.coordinateSystem;
};
export {
  BoxCoordinateSystemCoordFrom,
  CoordinateSystemUsageKind,
  decideCoordSysUsageKind,
  CoordinateSystemManager as default,
  getCoordForBoxCoordSys,
  injectCoordSysByOption,
  registerLayOutOnCoordSysUsage,
  simpleCoordSysInjectionProvider
};

import { defineComponent, getCurrentInstance, provide, renderSlot } from "vue";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbRadioGroup"
  },
  __name: "radioGroup",
  props: {
    modelValue: {
      type: [String, Number],
      default: ""
    }
  },
  emits: ["update:modelValue"],
  setup(__props, { expose: __expose }) {
    const instance = getCurrentInstance();
    const props = __props;
    provide("RadioGroup", instance == null ? void 0 : instance.proxy);
    __expose({
      modelValue: props.modelValue
    });
    return (_ctx, _cache) => {
      return renderSlot(_ctx.$slots, "default", {}, void 0, true);
    };
  }
});
export {
  _sfc_main as default
};

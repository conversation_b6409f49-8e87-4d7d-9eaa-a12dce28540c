import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import DataZoomModel from "./DataZoomModel.js";
var SelectDataZoomModel = (
  /** @class */
  function(_super) {
    __extends(SelectDataZoomModel2, _super);
    function SelectDataZoomModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = SelectDataZoomModel2.type;
      return _this;
    }
    SelectDataZoomModel2.type = "dataZoom.select";
    return SelectDataZoomModel2;
  }(DataZoomModel)
);
export {
  SelectDataZoomModel as default
};

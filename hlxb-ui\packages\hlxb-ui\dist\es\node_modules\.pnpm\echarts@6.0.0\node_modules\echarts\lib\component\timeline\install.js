import SliderTimelineModel from "./SliderTimelineModel.js";
import SliderTimelineView from "./SliderTimelineView.js";
import { installTimelineAction } from "./timelineAction.js";
import timelinePreprocessor from "./preprocessor.js";
function install(registers) {
  registers.registerComponentModel(SliderTimelineModel);
  registers.registerComponentView(SliderTimelineView);
  registers.registerSubTypeDefaulter("timeline", function() {
    return "slider";
  });
  installTimelineAction(registers);
  registers.registerPreprocessor(timelinePreprocessor);
}
export {
  install
};

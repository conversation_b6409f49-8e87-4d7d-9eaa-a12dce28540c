"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const tableEmpty = require("../assets/images/table-empty.png.js");
const index = require("../../../config/index.js");
const _hoisted_1 = ["src"];
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "HlxbCardEmpty"
  },
  __name: "Empty",
  props: {
    // 主题class
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = index.getPrefixCls("card-empty");
    const props = __props;
    vue.watch(
      () => props.themeColor,
      () => {
        console.log("themeColor", props.themeColor);
      }
    );
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass([vue.unref(prefixCls), __props.themeColor])
      }, [
        vue.createElementVNode("img", { src: vue.unref(tableEmpty.default) }, null, 8, _hoisted_1),
        _cache[0] || (_cache[0] = vue.createElementVNode("div", { class: "text-center" }, " 暂无数据", -1))
      ], 2);
    };
  }
});
exports.default = _sfc_main;

"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const Series = require("../../model/Series.js");
const createSeriesData = require("../helper/createSeriesData.js");
const util = require("../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js");
var BaseBarSeriesModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(BaseBarSeriesModel2, _super);
    function BaseBarSeriesModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = BaseBarSeriesModel2.type;
      return _this;
    }
    BaseBarSeriesModel2.prototype.getInitialData = function(option, ecModel) {
      return createSeriesData.default(null, this, {
        useEncodeDefaulter: true
      });
    };
    BaseBarSeriesModel2.prototype.getMarkerPosition = function(value, dims, startingAtTick) {
      var coordSys = this.coordinateSystem;
      if (coordSys && coordSys.clampData) {
        var clampData_1 = coordSys.clampData(value);
        var pt_1 = coordSys.dataToPoint(clampData_1);
        if (startingAtTick) {
          util.each(coordSys.getAxes(), function(axis, idx) {
            if (axis.type === "category" && dims != null) {
              var tickCoords = axis.getTicksCoords();
              var alignTicksWithLabel = axis.getTickModel().get("alignWithLabel");
              var targetTickId = clampData_1[idx];
              var isEnd = dims[idx] === "x1" || dims[idx] === "y1";
              if (isEnd && !alignTicksWithLabel) {
                targetTickId += 1;
              }
              if (tickCoords.length < 2) {
                return;
              } else if (tickCoords.length === 2) {
                pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);
                return;
              }
              var leftCoord = void 0;
              var coord = void 0;
              var stepTickValue = 1;
              for (var i = 0; i < tickCoords.length; i++) {
                var tickCoord = tickCoords[i].coord;
                var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;
                if (tickValue === targetTickId) {
                  coord = tickCoord;
                  break;
                } else if (tickValue < targetTickId) {
                  leftCoord = tickCoord;
                } else if (leftCoord != null && tickValue > targetTickId) {
                  coord = (tickCoord + leftCoord) / 2;
                  break;
                }
                if (i === 1) {
                  stepTickValue = tickValue - tickCoords[0].tickValue;
                }
              }
              if (coord == null) {
                if (!leftCoord) {
                  coord = tickCoords[0].coord;
                } else if (leftCoord) {
                  coord = tickCoords[tickCoords.length - 1].coord;
                }
              }
              pt_1[idx] = axis.toGlobalCoord(coord);
            }
          });
        } else {
          var data = this.getData();
          var offset = data.getLayout("offset");
          var size = data.getLayout("size");
          var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;
          pt_1[offsetIndex] += offset + size / 2;
        }
        return pt_1;
      }
      return [NaN, NaN];
    };
    BaseBarSeriesModel2.type = "series.__base_bar__";
    BaseBarSeriesModel2.defaultOption = {
      // zlevel: 0,
      z: 2,
      coordinateSystem: "cartesian2d",
      legendHoverLink: true,
      // stack: null
      // Cartesian coordinate system
      // xAxisIndex: 0,
      // yAxisIndex: 0,
      barMinHeight: 0,
      barMinAngle: 0,
      // cursor: null,
      large: false,
      largeThreshold: 400,
      progressive: 3e3,
      progressiveChunkMode: "mod",
      defaultBarGap: "10%"
    };
    return BaseBarSeriesModel2;
  }(Series.default)
);
Series.default.registerClass(BaseBarSeriesModel);
exports.default = BaseBarSeriesModel;

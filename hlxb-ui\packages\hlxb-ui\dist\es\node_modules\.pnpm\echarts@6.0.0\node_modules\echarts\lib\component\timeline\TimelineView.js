import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import ComponentView from "../../view/Component.js";
var TimelineView = (
  /** @class */
  function(_super) {
    __extends(TimelineView2, _super);
    function TimelineView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = TimelineView2.type;
      return _this;
    }
    TimelineView2.type = "timeline";
    return TimelineView2;
  }(ComponentView)
);
export {
  TimelineView as default
};

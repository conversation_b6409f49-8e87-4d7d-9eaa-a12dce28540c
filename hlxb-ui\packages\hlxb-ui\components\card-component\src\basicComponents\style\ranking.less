@import '../../../../style/index.less';

@ranking-prefix-cls: ~'@{hlxb-prefix}-ranking';

// 图片路径变量，解决 pnpm link 时的路径问题
@ranking-image-path: '../../assets/images/';

.@{ranking-prefix-cls} {
  // height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  &.Dark {
    color: #fff;
    // padding: 16px 8px 16px 16px;
    // height: calc(100% - 50px);
    // overflow-y: auto;
    // box-sizing: border-box;
  }

  &.light {
    color: #333;
  }

  &.screenColor {
    color: #fff;
    background: transparent;
  }

  .item-list {
    width: 100%;
    // height: 100%;
    // overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;

    .activeItem {
      position: relative;
      border-radius: 4px;
      border: 1px solid var(--theme-color-88p) !important;

      .img_box {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }

    .hoverItem {
      cursor: pointer;

      &:hover {
        border: 1px solid var(--theme-color-88p);
      }
    }

    .item {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;
      padding: 16px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      color: #fff;

      .top-title {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .bar-box {
        width: 100%;
        margin-top: 12px;
        height: 12px;
        background: #eeeff1;
        border-radius: 4px;

        .bar-conter {
          height: 12px;
          background: #999;
          border-radius: 4px;
        }

        .abar-conter {
          height: 12px;
          background: var(--theme-color-88p);
          border-radius: 4px;
        }
      }

      .abar-box {
        background: rgb(11 98 203 / 16%);
      }

      .item-index {
        flex: 1;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #333;

        .num-index {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          // background: #999;
          color: #fff;
          // border-radius: 10px;
        }

        .label {
          padding-left: 8px;
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          width: 120px;
          white-space: nowrap; /* 让文本不换行 */
          overflow: hidden; /* 超出部分隐藏 */
          text-overflow: ellipsis; /* 使用省略号代替超出部分 */
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        align-items: baseline;
        justify-content: end;

        .value {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: inline-block;
          max-width: 120px;
          white-space: nowrap;
          overflow: hidden;
          // text-overflow: ellipsis;
          // .number-value {
          // }
        }

        .unit {
          font-weight: 400;
          font-size: 14px;
          color: #666;
          margin-left: 8px;
        }
      }

      .num-index {
        color: #fff;
        background: url('@{ranking-image-path}item_fo.png') no-repeat center;
        background-size: 100% 100%;
      }

      &:nth-child(1) {
        .num-index {
          // color: #ffffff;
          // background: var(--theme-color);
          background: url('@{ranking-image-path}item_o.png') no-repeat center;
          background-size: 100% 100%;
        }
      }

      &:nth-child(2) {
        .num-index {
          color: #fff;
          // background: var(--theme-color);
          background: url('@{ranking-image-path}item_tw.png') no-repeat center;
          background-size: 100% 100%;
        }
      }

      &:nth-child(3) {
        .num-index {
          color: #fff;
          // background: var(--theme-color);
          background: url('@{ranking-image-path}item_th.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
}

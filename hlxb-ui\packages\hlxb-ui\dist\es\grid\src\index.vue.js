import { defineComponent, computed, resolveDirective, withDirectives, createElementBlock, openBlock, normalizeStyle, normalizeClass, unref, createCommentVNode, Fragment, renderList, renderSlot, createElementVNode, createTextVNode, toDisplayString, createVNode } from "vue";
import { getPrefixCls } from "../../config/index.js";
import HlxbEmpty from "../../empty/index.js";
const _hoisted_1 = ["onClick"];
const _hoisted_2 = { class: "nav-icon-container" };
const _hoisted_3 = ["src", "alt"];
const _hoisted_4 = { class: "grid-item-text text-14px mt-12px truncate max-w-[90%]" };
const _hoisted_5 = {
  key: 0,
  class: "w-full h-full flex justify-center items-center"
};
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbGrid"
  },
  __name: "index",
  props: {
    data: { default: () => [] },
    loading: { type: Boolean, default: false },
    columns: { default: 6 },
    itemHeight: { default: "86px" },
    gap: { default: "8px" },
    emptyText: { default: "暂无数据" },
    hoverShow: { type: Boolean, default: true },
    containerClass: {},
    containerStyle: {},
    itemClass: {},
    itemStyle: {}
  },
  emits: ["item-click"],
  setup(__props, { emit: __emit }) {
    const prefixCls = getPrefixCls("grid");
    const props = __props;
    const emit = __emit;
    const isEmpty = computed(() => {
      var _a;
      return !((_a = props.data) == null ? void 0 : _a.length) && !props.loading;
    });
    const handleItemClick = (item, index) => {
      emit("item-click", item, index);
    };
    return (_ctx, _cache) => {
      const _directive_loading = resolveDirective("loading");
      return withDirectives((openBlock(), createElementBlock("div", {
        class: normalizeClass([
          unref(prefixCls),
          "flex flex-wrap px-[16px] relative",
          _ctx.containerClass,
          { "h-full": isEmpty.value || _ctx.loading }
        ]),
        style: normalizeStyle(_ctx.containerStyle)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.data, (item, index) => {
          return openBlock(), createElementBlock("div", {
            class: normalizeClass([
              "grid-item",
              "flex flex-col justify-center items-center cursor-pointer",
              { "hover-show": _ctx.hoverShow },
              _ctx.itemClass
            ]),
            style: normalizeStyle([
              _ctx.itemStyle,
              {
                height: _ctx.itemHeight,
                marginBottom: _ctx.gap,
                width: `${100 / _ctx.columns}%`
              }
            ]),
            key: index,
            onClick: ($event) => handleItemClick(item, index)
          }, [
            renderSlot(_ctx.$slots, "item", {
              item,
              index
            }, () => [
              createElementVNode("div", _hoisted_2, [
                renderSlot(_ctx.$slots, "icon", {
                  item,
                  index
                }, () => [
                  createElementVNode("img", {
                    class: "grid-item-icon",
                    style: { "width": "44px", "height": "44px" },
                    src: item.icon,
                    alt: item.name
                  }, null, 8, _hoisted_3)
                ])
              ]),
              createElementVNode("span", _hoisted_4, [
                renderSlot(_ctx.$slots, "title", {
                  item,
                  index
                }, () => [
                  createTextVNode(toDisplayString(item.name), 1)
                ])
              ])
            ])
          ], 14, _hoisted_1);
        }), 128)),
        isEmpty.value ? (openBlock(), createElementBlock("div", _hoisted_5, [
          renderSlot(_ctx.$slots, "empty", {}, () => [
            createVNode(unref(HlxbEmpty), { description: _ctx.emptyText }, null, 8, ["description"])
          ])
        ])) : createCommentVNode("", true)
      ], 6)), [
        [_directive_loading, _ctx.loading]
      ]);
    };
  }
});
export {
  _sfc_main as default
};

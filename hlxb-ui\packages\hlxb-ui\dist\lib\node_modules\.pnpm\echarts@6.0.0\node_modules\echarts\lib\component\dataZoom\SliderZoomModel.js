"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const tslib_es6 = require("../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js");
const DataZoomModel = require("./DataZoomModel.js");
const component = require("../../util/component.js");
const tokens = require("../../visual/tokens.js");
var SliderZoomModel = (
  /** @class */
  function(_super) {
    tslib_es6.__extends(SliderZoomModel2, _super);
    function SliderZoomModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = SliderZoomModel2.type;
      return _this;
    }
    SliderZoomModel2.type = "dataZoom.slider";
    SliderZoomModel2.layoutMode = "box";
    SliderZoomModel2.defaultOption = component.inheritDefaultOption(DataZoomModel.default.defaultOption, {
      show: true,
      // deault value can only be drived in view stage.
      right: "ph",
      top: "ph",
      width: "ph",
      height: "ph",
      left: null,
      bottom: null,
      borderColor: tokens.default.color.accent10,
      borderRadius: 0,
      backgroundColor: tokens.default.color.transparent,
      // dataBackgroundColor: '#ddd',
      dataBackground: {
        lineStyle: {
          color: tokens.default.color.accent30,
          width: 0.5
        },
        areaStyle: {
          color: tokens.default.color.accent20,
          opacity: 0.2
        }
      },
      selectedDataBackground: {
        lineStyle: {
          color: tokens.default.color.accent40,
          width: 0.5
        },
        areaStyle: {
          color: tokens.default.color.accent20,
          opacity: 0.3
        }
      },
      // Color of selected window.
      fillerColor: "rgba(135,175,274,0.2)",
      handleIcon: "path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",
      // Percent of the slider height
      handleSize: "100%",
      handleStyle: {
        color: tokens.default.color.neutral00,
        borderColor: tokens.default.color.accent20
      },
      moveHandleSize: 7,
      moveHandleIcon: "path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",
      moveHandleStyle: {
        color: tokens.default.color.accent40,
        opacity: 0.5
      },
      showDetail: true,
      showDataShadow: "auto",
      realtime: true,
      zoomLock: false,
      textStyle: {
        color: tokens.default.color.tertiary
      },
      brushSelect: true,
      brushStyle: {
        color: tokens.default.color.accent30,
        opacity: 0.3
      },
      emphasis: {
        handleLabel: {
          show: true
        },
        handleStyle: {
          borderColor: tokens.default.color.accent40
        },
        moveHandleStyle: {
          opacity: 0.8
        }
      },
      defaultLocationEdgeGap: 15
    });
    return SliderZoomModel2;
  }(DataZoomModel.default)
);
exports.default = SliderZoomModel;

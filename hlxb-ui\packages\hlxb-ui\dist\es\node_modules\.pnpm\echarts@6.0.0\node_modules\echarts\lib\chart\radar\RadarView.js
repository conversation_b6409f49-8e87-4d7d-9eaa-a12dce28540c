import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import * as graphic from "../../util/graphic.js";
import { setStatesStylesFromModel, toggleHoverEmphasis } from "../../util/states.js";
import { defaults, each, clone, extend, map } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { normalizeSymbolSize, createSymbol } from "../../util/symbol.js";
import ChartView from "../../view/Chart.js";
import { setLabelStyle, getLabelStatesModels } from "../../label/labelStyle.js";
import ZRImage from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Image.js";
import { initProps, saveOldStyle, updateProps } from "../../animation/basicTransition.js";
import Polygon from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Polygon.js";
import Polyline from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Polyline.js";
import Group from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
var RadarView = (
  /** @class */
  function(_super) {
    __extends(RadarView2, _super);
    function RadarView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = RadarView2.type;
      return _this;
    }
    RadarView2.prototype.render = function(seriesModel, ecModel, api) {
      var polar = seriesModel.coordinateSystem;
      var group = this.group;
      var data = seriesModel.getData();
      var oldData = this._data;
      function createSymbol$1(data2, idx) {
        var symbolType = data2.getItemVisual(idx, "symbol") || "circle";
        if (symbolType === "none") {
          return;
        }
        var symbolSize = normalizeSymbolSize(data2.getItemVisual(idx, "symbolSize"));
        var symbolPath = createSymbol(symbolType, -1, -1, 2, 2);
        var symbolRotate = data2.getItemVisual(idx, "symbolRotate") || 0;
        symbolPath.attr({
          style: {
            strokeNoScale: true
          },
          z2: 100,
          scaleX: symbolSize[0] / 2,
          scaleY: symbolSize[1] / 2,
          rotation: symbolRotate * Math.PI / 180 || 0
        });
        return symbolPath;
      }
      function updateSymbols(oldPoints, newPoints, symbolGroup, data2, idx, isInit) {
        symbolGroup.removeAll();
        for (var i = 0; i < newPoints.length - 1; i++) {
          var symbolPath = createSymbol$1(data2, idx);
          if (symbolPath) {
            symbolPath.__dimIdx = i;
            if (oldPoints[i]) {
              symbolPath.setPosition(oldPoints[i]);
              graphic[isInit ? "initProps" : "updateProps"](symbolPath, {
                x: newPoints[i][0],
                y: newPoints[i][1]
              }, seriesModel, idx);
            } else {
              symbolPath.setPosition(newPoints[i]);
            }
            symbolGroup.add(symbolPath);
          }
        }
      }
      function getInitialPoints(points) {
        return map(points, function(pt) {
          return [polar.cx, polar.cy];
        });
      }
      data.diff(oldData).add(function(idx) {
        var points = data.getItemLayout(idx);
        if (!points) {
          return;
        }
        var polygon = new Polygon();
        var polyline = new Polyline();
        var target = {
          shape: {
            points
          }
        };
        polygon.shape.points = getInitialPoints(points);
        polyline.shape.points = getInitialPoints(points);
        initProps(polygon, target, seriesModel, idx);
        initProps(polyline, target, seriesModel, idx);
        var itemGroup = new Group();
        var symbolGroup = new Group();
        itemGroup.add(polyline);
        itemGroup.add(polygon);
        itemGroup.add(symbolGroup);
        updateSymbols(polyline.shape.points, points, symbolGroup, data, idx, true);
        data.setItemGraphicEl(idx, itemGroup);
      }).update(function(newIdx, oldIdx) {
        var itemGroup = oldData.getItemGraphicEl(oldIdx);
        var polyline = itemGroup.childAt(0);
        var polygon = itemGroup.childAt(1);
        var symbolGroup = itemGroup.childAt(2);
        var target = {
          shape: {
            points: data.getItemLayout(newIdx)
          }
        };
        if (!target.shape.points) {
          return;
        }
        updateSymbols(polyline.shape.points, target.shape.points, symbolGroup, data, newIdx, false);
        saveOldStyle(polygon);
        saveOldStyle(polyline);
        updateProps(polyline, target, seriesModel);
        updateProps(polygon, target, seriesModel);
        data.setItemGraphicEl(newIdx, itemGroup);
      }).remove(function(idx) {
        group.remove(oldData.getItemGraphicEl(idx));
      }).execute();
      data.eachItemGraphicEl(function(itemGroup, idx) {
        var itemModel = data.getItemModel(idx);
        var polyline = itemGroup.childAt(0);
        var polygon = itemGroup.childAt(1);
        var symbolGroup = itemGroup.childAt(2);
        var itemStyle = data.getItemVisual(idx, "style");
        var color = itemStyle.fill;
        group.add(itemGroup);
        polyline.useStyle(defaults(itemModel.getModel("lineStyle").getLineStyle(), {
          fill: "none",
          stroke: color
        }));
        setStatesStylesFromModel(polyline, itemModel, "lineStyle");
        setStatesStylesFromModel(polygon, itemModel, "areaStyle");
        var areaStyleModel = itemModel.getModel("areaStyle");
        var polygonIgnore = areaStyleModel.isEmpty() && areaStyleModel.parentModel.isEmpty();
        polygon.ignore = polygonIgnore;
        each(["emphasis", "select", "blur"], function(stateName) {
          var stateModel = itemModel.getModel([stateName, "areaStyle"]);
          var stateIgnore = stateModel.isEmpty() && stateModel.parentModel.isEmpty();
          polygon.ensureState(stateName).ignore = stateIgnore && polygonIgnore;
          var lineStyle = itemModel.getModel([stateName, "lineStyle"]).getLineStyle();
          polyline.ensureState(stateName).style = lineStyle;
          var areaStyle = stateModel.getAreaStyle();
          polygon.ensureState(stateName).style = areaStyle;
          var itemStateStyle = itemModel.getModel([stateName, "itemStyle"]).getItemStyle();
          symbolGroup.eachChild(function(symbolPath) {
            symbolPath.ensureState(stateName).style = clone(itemStateStyle);
          });
        });
        polygon.useStyle(defaults(itemModel.getModel("areaStyle").getAreaStyle(), {
          fill: color,
          opacity: 0.7,
          decal: itemStyle.decal
        }));
        var emphasisModel = itemModel.getModel("emphasis");
        symbolGroup.eachChild(function(symbolPath) {
          if (symbolPath instanceof ZRImage) {
            var pathStyle = symbolPath.style;
            symbolPath.useStyle(extend({
              // TODO other properties like x, y ?
              image: pathStyle.image,
              x: pathStyle.x,
              y: pathStyle.y,
              width: pathStyle.width,
              height: pathStyle.height
            }, itemStyle));
          } else {
            symbolPath.useStyle(itemStyle);
            symbolPath.setColor(color);
            symbolPath.style.strokeNoScale = true;
          }
          var defaultText = data.getStore().get(data.getDimensionIndex(symbolPath.__dimIdx), idx);
          (defaultText == null || isNaN(defaultText)) && (defaultText = "");
          setLabelStyle(symbolPath, getLabelStatesModels(itemModel), {
            labelFetcher: data.hostModel,
            labelDataIndex: idx,
            labelDimIndex: symbolPath.__dimIdx,
            defaultText,
            inheritColor: color,
            defaultOpacity: itemStyle.opacity
          });
        });
        toggleHoverEmphasis(itemGroup, emphasisModel.get("focus"), emphasisModel.get("blurScope"), emphasisModel.get("disabled"));
      });
      this._data = data;
    };
    RadarView2.prototype.remove = function() {
      this.group.removeAll();
      this._data = null;
    };
    RadarView2.type = "radar";
    return RadarView2;
  }(ChartView)
);
export {
  RadarView as default
};

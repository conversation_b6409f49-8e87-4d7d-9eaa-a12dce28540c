import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { each, merge } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getFeature } from "./featureManager.js";
import ComponentModel from "../../model/Component.js";
import tokens from "../../visual/tokens.js";
var ToolboxModel = (
  /** @class */
  function(_super) {
    __extends(ToolboxModel2, _super);
    function ToolboxModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = ToolboxModel2.type;
      return _this;
    }
    ToolboxModel2.prototype.optionUpdated = function() {
      _super.prototype.optionUpdated.apply(this, arguments);
      var ecModel = this.ecModel;
      each(this.option.feature, function(featureOpt, featureName) {
        var Feature = getFeature(featureName);
        if (Feature) {
          if (Feature.getDefaultOption) {
            Feature.defaultOption = Feature.getDefaultOption(ecModel);
          }
          merge(featureOpt, Feature.defaultOption);
        }
      });
    };
    ToolboxModel2.type = "toolbox";
    ToolboxModel2.layoutMode = {
      type: "box",
      ignoreSize: true
    };
    ToolboxModel2.defaultOption = {
      show: true,
      z: 6,
      // zlevel: 0,
      orient: "horizontal",
      left: "right",
      top: "top",
      // right
      // bottom
      backgroundColor: "transparent",
      borderColor: tokens.color.border,
      borderRadius: 0,
      borderWidth: 0,
      padding: tokens.size.m,
      itemSize: 15,
      itemGap: tokens.size.s,
      showTitle: true,
      iconStyle: {
        borderColor: tokens.color.accent50,
        color: "none"
      },
      emphasis: {
        iconStyle: {
          borderColor: tokens.color.accent50
        }
      },
      // textStyle: {},
      // feature
      tooltip: {
        show: false,
        position: "bottom"
      }
    };
    return ToolboxModel2;
  }(ComponentModel)
);
export {
  ToolboxModel as default
};

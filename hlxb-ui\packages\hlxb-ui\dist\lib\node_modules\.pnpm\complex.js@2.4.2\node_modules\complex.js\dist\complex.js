"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const cosh = Math.cosh || function(x) {
  return Math.abs(x) < 1e-9 ? 1 - x : (Math.exp(x) + Math.exp(-x)) * 0.5;
};
const sinh = Math.sinh || function(x) {
  return Math.abs(x) < 1e-9 ? x : (Math.exp(x) - Math.exp(-x)) * 0.5;
};
const cosm1 = function(x) {
  const b = Math.PI / 4;
  if (-b > x || x > b) {
    return Math.cos(x) - 1;
  }
  const xx = x * x;
  return xx * (xx * (xx * (xx * (xx * (xx * (xx * (xx / 20922789888e3 - 1 / 87178291200) + 1 / *********) - 1 / 3628800) + 1 / 40320) - 1 / 720) + 1 / 24) - 1 / 2);
};
const hypot = function(x, y) {
  x = Math.abs(x);
  y = Math.abs(y);
  if (x < y) [x, y] = [y, x];
  if (x < 1e8) return Math.sqrt(x * x + y * y);
  y /= x;
  return x * Math.sqrt(1 + y * y);
};
const parser_exit = function() {
  throw SyntaxError("Invalid Param");
};
function logHypot(a, b) {
  const _a = Math.abs(a);
  const _b = Math.abs(b);
  if (a === 0) {
    return Math.log(_b);
  }
  if (b === 0) {
    return Math.log(_a);
  }
  if (_a < 3e3 && _b < 3e3) {
    return Math.log(a * a + b * b) * 0.5;
  }
  a = a * 0.5;
  b = b * 0.5;
  return 0.5 * Math.log(a * a + b * b) + Math.LN2;
}
const P = { "re": 0, "im": 0 };
const parse = function(a, b) {
  const z = P;
  if (a === void 0 || a === null) {
    z["re"] = z["im"] = 0;
  } else if (b !== void 0) {
    z["re"] = a;
    z["im"] = b;
  } else
    switch (typeof a) {
      case "object":
        if ("im" in a && "re" in a) {
          z["re"] = a["re"];
          z["im"] = a["im"];
        } else if ("abs" in a && "arg" in a) {
          if (!isFinite(a["abs"]) && isFinite(a["arg"])) {
            return Complex["INFINITY"];
          }
          z["re"] = a["abs"] * Math.cos(a["arg"]);
          z["im"] = a["abs"] * Math.sin(a["arg"]);
        } else if ("r" in a && "phi" in a) {
          if (!isFinite(a["r"]) && isFinite(a["phi"])) {
            return Complex["INFINITY"];
          }
          z["re"] = a["r"] * Math.cos(a["phi"]);
          z["im"] = a["r"] * Math.sin(a["phi"]);
        } else if (a.length === 2) {
          z["re"] = a[0];
          z["im"] = a[1];
        } else {
          parser_exit();
        }
        break;
      case "string":
        z["im"] = /* void */
        z["re"] = 0;
        const tokens = a.replace(/_/g, "").match(/\d+\.?\d*e[+-]?\d+|\d+\.?\d*|\.\d+|./g);
        let plus = 1;
        let minus = 0;
        if (tokens === null) {
          parser_exit();
        }
        for (let i = 0; i < tokens.length; i++) {
          const c = tokens[i];
          if (c === " " || c === "	" || c === "\n") ;
          else if (c === "+") {
            plus++;
          } else if (c === "-") {
            minus++;
          } else if (c === "i" || c === "I") {
            if (plus + minus === 0) {
              parser_exit();
            }
            if (tokens[i + 1] !== " " && !isNaN(tokens[i + 1])) {
              z["im"] += parseFloat((minus % 2 ? "-" : "") + tokens[i + 1]);
              i++;
            } else {
              z["im"] += parseFloat((minus % 2 ? "-" : "") + "1");
            }
            plus = minus = 0;
          } else {
            if (plus + minus === 0 || isNaN(c)) {
              parser_exit();
            }
            if (tokens[i + 1] === "i" || tokens[i + 1] === "I") {
              z["im"] += parseFloat((minus % 2 ? "-" : "") + c);
              i++;
            } else {
              z["re"] += parseFloat((minus % 2 ? "-" : "") + c);
            }
            plus = minus = 0;
          }
        }
        if (plus + minus > 0) {
          parser_exit();
        }
        break;
      case "number":
        z["im"] = 0;
        z["re"] = a;
        break;
      default:
        parser_exit();
    }
  if (isNaN(z["re"]) || isNaN(z["im"])) ;
  return z;
};
function Complex(a, b) {
  if (!(this instanceof Complex)) {
    return new Complex(a, b);
  }
  const z = parse(a, b);
  this["re"] = z["re"];
  this["im"] = z["im"];
}
Complex.prototype = {
  "re": 0,
  "im": 0,
  /**
   * Calculates the sign of a complex number, which is a normalized complex
   *
   * @returns {Complex}
   */
  "sign": function() {
    const abs = hypot(this["re"], this["im"]);
    return new Complex(
      this["re"] / abs,
      this["im"] / abs
    );
  },
  /**
   * Adds two complex numbers
   *
   * @returns {Complex}
   */
  "add": function(a, b) {
    const z = parse(a, b);
    const tInfin = this["isInfinite"]();
    const zInfin = !(isFinite(z["re"]) && isFinite(z["im"]));
    if (tInfin || zInfin) {
      if (tInfin && zInfin) {
        return Complex["NAN"];
      }
      return Complex["INFINITY"];
    }
    return new Complex(
      this["re"] + z["re"],
      this["im"] + z["im"]
    );
  },
  /**
   * Subtracts two complex numbers
   *
   * @returns {Complex}
   */
  "sub": function(a, b) {
    const z = parse(a, b);
    const tInfin = this["isInfinite"]();
    const zInfin = !(isFinite(z["re"]) && isFinite(z["im"]));
    if (tInfin || zInfin) {
      if (tInfin && zInfin) {
        return Complex["NAN"];
      }
      return Complex["INFINITY"];
    }
    return new Complex(
      this["re"] - z["re"],
      this["im"] - z["im"]
    );
  },
  /**
   * Multiplies two complex numbers
   *
   * @returns {Complex}
   */
  "mul": function(a, b) {
    const z = parse(a, b);
    const tInfin = this["isInfinite"]();
    const zInfin = !(isFinite(z["re"]) && isFinite(z["im"]));
    const tIsZero = this["re"] === 0 && this["im"] === 0;
    const zIsZero = z["re"] === 0 && z["im"] === 0;
    if (tInfin && zIsZero || zInfin && tIsZero) {
      return Complex["NAN"];
    }
    if (tInfin || zInfin) {
      return Complex["INFINITY"];
    }
    if (z["im"] === 0 && this["im"] === 0) {
      return new Complex(this["re"] * z["re"], 0);
    }
    return new Complex(
      this["re"] * z["re"] - this["im"] * z["im"],
      this["re"] * z["im"] + this["im"] * z["re"]
    );
  },
  /**
   * Divides two complex numbers
   *
   * @returns {Complex}
   */
  "div": function(a, b) {
    const z = parse(a, b);
    const tInfin = this["isInfinite"]();
    const zInfin = !(isFinite(z["re"]) && isFinite(z["im"]));
    const tIsZero = this["re"] === 0 && this["im"] === 0;
    const zIsZero = z["re"] === 0 && z["im"] === 0;
    if (tIsZero && zIsZero || tInfin && zInfin) {
      return Complex["NAN"];
    }
    if (zIsZero || tInfin) {
      return Complex["INFINITY"];
    }
    if (tIsZero || zInfin) {
      return Complex["ZERO"];
    }
    if (0 === z["im"]) {
      return new Complex(this["re"] / z["re"], this["im"] / z["re"]);
    }
    if (Math.abs(z["re"]) < Math.abs(z["im"])) {
      const x = z["re"] / z["im"];
      const t = z["re"] * x + z["im"];
      return new Complex(
        (this["re"] * x + this["im"]) / t,
        (this["im"] * x - this["re"]) / t
      );
    } else {
      const x = z["im"] / z["re"];
      const t = z["im"] * x + z["re"];
      return new Complex(
        (this["re"] + this["im"] * x) / t,
        (this["im"] - this["re"] * x) / t
      );
    }
  },
  /**
   * Calculate the power of two complex numbers
   *
   * @returns {Complex}
   */
  "pow": function(a, b) {
    const z = parse(a, b);
    const tIsZero = this["re"] === 0 && this["im"] === 0;
    const zIsZero = z["re"] === 0 && z["im"] === 0;
    if (zIsZero) {
      return Complex["ONE"];
    }
    if (z["im"] === 0) {
      if (this["im"] === 0 && this["re"] > 0) {
        return new Complex(Math.pow(this["re"], z["re"]), 0);
      } else if (this["re"] === 0) {
        switch ((z["re"] % 4 + 4) % 4) {
          case 0:
            return new Complex(Math.pow(this["im"], z["re"]), 0);
          case 1:
            return new Complex(0, Math.pow(this["im"], z["re"]));
          case 2:
            return new Complex(-Math.pow(this["im"], z["re"]), 0);
          case 3:
            return new Complex(0, -Math.pow(this["im"], z["re"]));
        }
      }
    }
    if (tIsZero && z["re"] > 0) {
      return Complex["ZERO"];
    }
    const arg = Math.atan2(this["im"], this["re"]);
    const loh = logHypot(this["re"], this["im"]);
    let re = Math.exp(z["re"] * loh - z["im"] * arg);
    let im = z["im"] * loh + z["re"] * arg;
    return new Complex(
      re * Math.cos(im),
      re * Math.sin(im)
    );
  },
  /**
   * Calculate the complex square root
   *
   * @returns {Complex}
   */
  "sqrt": function() {
    const a = this["re"];
    const b = this["im"];
    if (b === 0) {
      if (a >= 0) {
        return new Complex(Math.sqrt(a), 0);
      } else {
        return new Complex(0, Math.sqrt(-a));
      }
    }
    const r = hypot(a, b);
    let re = Math.sqrt(0.5 * (r + Math.abs(a)));
    let im = Math.abs(b) / (2 * re);
    if (a >= 0) {
      return new Complex(re, b < 0 ? -im : im);
    } else {
      return new Complex(im, b < 0 ? -re : re);
    }
  },
  /**
   * Calculate the complex exponent
   *
   * @returns {Complex}
   */
  "exp": function() {
    const er = Math.exp(this["re"]);
    if (this["im"] === 0) {
      return new Complex(er, 0);
    }
    return new Complex(
      er * Math.cos(this["im"]),
      er * Math.sin(this["im"])
    );
  },
  /**
   * Calculate the complex exponent and subtracts one.
   *
   * This may be more accurate than `Complex(x).exp().sub(1)` if
   * `x` is small.
   *
   * @returns {Complex}
   */
  "expm1": function() {
    const a = this["re"];
    const b = this["im"];
    return new Complex(
      Math.expm1(a) * Math.cos(b) + cosm1(b),
      Math.exp(a) * Math.sin(b)
    );
  },
  /**
   * Calculate the natural log
   *
   * @returns {Complex}
   */
  "log": function() {
    const a = this["re"];
    const b = this["im"];
    if (b === 0 && a > 0) {
      return new Complex(Math.log(a), 0);
    }
    return new Complex(
      logHypot(a, b),
      Math.atan2(b, a)
    );
  },
  /**
   * Calculate the magnitude of the complex number
   *
   * @returns {number}
   */
  "abs": function() {
    return hypot(this["re"], this["im"]);
  },
  /**
   * Calculate the angle of the complex number
   *
   * @returns {number}
   */
  "arg": function() {
    return Math.atan2(this["im"], this["re"]);
  },
  /**
   * Calculate the sine of the complex number
   *
   * @returns {Complex}
   */
  "sin": function() {
    const a = this["re"];
    const b = this["im"];
    return new Complex(
      Math.sin(a) * cosh(b),
      Math.cos(a) * sinh(b)
    );
  },
  /**
   * Calculate the cosine
   *
   * @returns {Complex}
   */
  "cos": function() {
    const a = this["re"];
    const b = this["im"];
    return new Complex(
      Math.cos(a) * cosh(b),
      -Math.sin(a) * sinh(b)
    );
  },
  /**
   * Calculate the tangent
   *
   * @returns {Complex}
   */
  "tan": function() {
    const a = 2 * this["re"];
    const b = 2 * this["im"];
    const d = Math.cos(a) + cosh(b);
    return new Complex(
      Math.sin(a) / d,
      sinh(b) / d
    );
  },
  /**
   * Calculate the cotangent
   *
   * @returns {Complex}
   */
  "cot": function() {
    const a = 2 * this["re"];
    const b = 2 * this["im"];
    const d = Math.cos(a) - cosh(b);
    return new Complex(
      -Math.sin(a) / d,
      sinh(b) / d
    );
  },
  /**
   * Calculate the secant
   *
   * @returns {Complex}
   */
  "sec": function() {
    const a = this["re"];
    const b = this["im"];
    const d = 0.5 * cosh(2 * b) + 0.5 * Math.cos(2 * a);
    return new Complex(
      Math.cos(a) * cosh(b) / d,
      Math.sin(a) * sinh(b) / d
    );
  },
  /**
   * Calculate the cosecans
   *
   * @returns {Complex}
   */
  "csc": function() {
    const a = this["re"];
    const b = this["im"];
    const d = 0.5 * cosh(2 * b) - 0.5 * Math.cos(2 * a);
    return new Complex(
      Math.sin(a) * cosh(b) / d,
      -Math.cos(a) * sinh(b) / d
    );
  },
  /**
   * Calculate the complex arcus sinus
   *
   * @returns {Complex}
   */
  "asin": function() {
    const a = this["re"];
    const b = this["im"];
    const t1 = new Complex(
      b * b - a * a + 1,
      -2 * a * b
    )["sqrt"]();
    const t2 = new Complex(
      t1["re"] - b,
      t1["im"] + a
    )["log"]();
    return new Complex(t2["im"], -t2["re"]);
  },
  /**
   * Calculate the complex arcus cosinus
   *
   * @returns {Complex}
   */
  "acos": function() {
    const a = this["re"];
    const b = this["im"];
    const t1 = new Complex(
      b * b - a * a + 1,
      -2 * a * b
    )["sqrt"]();
    const t2 = new Complex(
      t1["re"] - b,
      t1["im"] + a
    )["log"]();
    return new Complex(Math.PI / 2 - t2["im"], t2["re"]);
  },
  /**
   * Calculate the complex arcus tangent
   *
   * @returns {Complex}
   */
  "atan": function() {
    const a = this["re"];
    const b = this["im"];
    if (a === 0) {
      if (b === 1) {
        return new Complex(0, Infinity);
      }
      if (b === -1) {
        return new Complex(0, -Infinity);
      }
    }
    const d = a * a + (1 - b) * (1 - b);
    const t1 = new Complex(
      (1 - b * b - a * a) / d,
      -2 * a / d
    ).log();
    return new Complex(-0.5 * t1["im"], 0.5 * t1["re"]);
  },
  /**
   * Calculate the complex arcus cotangent
   *
   * @returns {Complex}
   */
  "acot": function() {
    const a = this["re"];
    const b = this["im"];
    if (b === 0) {
      return new Complex(Math.atan2(1, a), 0);
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).atan() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).atan();
  },
  /**
   * Calculate the complex arcus secant
   *
   * @returns {Complex}
   */
  "asec": function() {
    const a = this["re"];
    const b = this["im"];
    if (a === 0 && b === 0) {
      return new Complex(0, Infinity);
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).acos() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).acos();
  },
  /**
   * Calculate the complex arcus cosecans
   *
   * @returns {Complex}
   */
  "acsc": function() {
    const a = this["re"];
    const b = this["im"];
    if (a === 0 && b === 0) {
      return new Complex(Math.PI / 2, Infinity);
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).asin() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).asin();
  },
  /**
   * Calculate the complex sinh
   *
   * @returns {Complex}
   */
  "sinh": function() {
    const a = this["re"];
    const b = this["im"];
    return new Complex(
      sinh(a) * Math.cos(b),
      cosh(a) * Math.sin(b)
    );
  },
  /**
   * Calculate the complex cosh
   *
   * @returns {Complex}
   */
  "cosh": function() {
    const a = this["re"];
    const b = this["im"];
    return new Complex(
      cosh(a) * Math.cos(b),
      sinh(a) * Math.sin(b)
    );
  },
  /**
   * Calculate the complex tanh
   *
   * @returns {Complex}
   */
  "tanh": function() {
    const a = 2 * this["re"];
    const b = 2 * this["im"];
    const d = cosh(a) + Math.cos(b);
    return new Complex(
      sinh(a) / d,
      Math.sin(b) / d
    );
  },
  /**
   * Calculate the complex coth
   *
   * @returns {Complex}
   */
  "coth": function() {
    const a = 2 * this["re"];
    const b = 2 * this["im"];
    const d = cosh(a) - Math.cos(b);
    return new Complex(
      sinh(a) / d,
      -Math.sin(b) / d
    );
  },
  /**
   * Calculate the complex coth
   *
   * @returns {Complex}
   */
  "csch": function() {
    const a = this["re"];
    const b = this["im"];
    const d = Math.cos(2 * b) - cosh(2 * a);
    return new Complex(
      -2 * sinh(a) * Math.cos(b) / d,
      2 * cosh(a) * Math.sin(b) / d
    );
  },
  /**
   * Calculate the complex sech
   *
   * @returns {Complex}
   */
  "sech": function() {
    const a = this["re"];
    const b = this["im"];
    const d = Math.cos(2 * b) + cosh(2 * a);
    return new Complex(
      2 * cosh(a) * Math.cos(b) / d,
      -2 * sinh(a) * Math.sin(b) / d
    );
  },
  /**
   * Calculate the complex asinh
   *
   * @returns {Complex}
   */
  "asinh": function() {
    let tmp = this["im"];
    this["im"] = -this["re"];
    this["re"] = tmp;
    const res = this["asin"]();
    this["re"] = -this["im"];
    this["im"] = tmp;
    tmp = res["re"];
    res["re"] = -res["im"];
    res["im"] = tmp;
    return res;
  },
  /**
   * Calculate the complex acosh
   *
   * @returns {Complex}
   */
  "acosh": function() {
    const res = this["acos"]();
    if (res["im"] <= 0) {
      const tmp = res["re"];
      res["re"] = -res["im"];
      res["im"] = tmp;
    } else {
      const tmp = res["im"];
      res["im"] = -res["re"];
      res["re"] = tmp;
    }
    return res;
  },
  /**
   * Calculate the complex atanh
   *
   * @returns {Complex}
   */
  "atanh": function() {
    const a = this["re"];
    const b = this["im"];
    const noIM = a > 1 && b === 0;
    const oneMinus = 1 - a;
    const onePlus = 1 + a;
    const d = oneMinus * oneMinus + b * b;
    const x = d !== 0 ? new Complex(
      (onePlus * oneMinus - b * b) / d,
      (b * oneMinus + onePlus * b) / d
    ) : new Complex(
      a !== -1 ? a / 0 : 0,
      b !== 0 ? b / 0 : 0
    );
    const temp = x["re"];
    x["re"] = logHypot(x["re"], x["im"]) / 2;
    x["im"] = Math.atan2(x["im"], temp) / 2;
    if (noIM) {
      x["im"] = -x["im"];
    }
    return x;
  },
  /**
   * Calculate the complex acoth
   *
   * @returns {Complex}
   */
  "acoth": function() {
    const a = this["re"];
    const b = this["im"];
    if (a === 0 && b === 0) {
      return new Complex(0, Math.PI / 2);
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).atanh() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).atanh();
  },
  /**
   * Calculate the complex acsch
   *
   * @returns {Complex}
   */
  "acsch": function() {
    const a = this["re"];
    const b = this["im"];
    if (b === 0) {
      return new Complex(
        a !== 0 ? Math.log(a + Math.sqrt(a * a + 1)) : Infinity,
        0
      );
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).asinh() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).asinh();
  },
  /**
   * Calculate the complex asech
   *
   * @returns {Complex}
   */
  "asech": function() {
    const a = this["re"];
    const b = this["im"];
    if (this["isZero"]()) {
      return Complex["INFINITY"];
    }
    const d = a * a + b * b;
    return d !== 0 ? new Complex(
      a / d,
      -b / d
    ).acosh() : new Complex(
      a !== 0 ? a / 0 : 0,
      b !== 0 ? -b / 0 : 0
    ).acosh();
  },
  /**
   * Calculate the complex inverse 1/z
   *
   * @returns {Complex}
   */
  "inverse": function() {
    if (this["isZero"]()) {
      return Complex["INFINITY"];
    }
    if (this["isInfinite"]()) {
      return Complex["ZERO"];
    }
    const a = this["re"];
    const b = this["im"];
    const d = a * a + b * b;
    return new Complex(a / d, -b / d);
  },
  /**
   * Returns the complex conjugate
   *
   * @returns {Complex}
   */
  "conjugate": function() {
    return new Complex(this["re"], -this["im"]);
  },
  /**
   * Gets the negated complex number
   *
   * @returns {Complex}
   */
  "neg": function() {
    return new Complex(-this["re"], -this["im"]);
  },
  /**
   * Ceils the actual complex number
   *
   * @returns {Complex}
   */
  "ceil": function(places) {
    places = Math.pow(10, places || 0);
    return new Complex(
      Math.ceil(this["re"] * places) / places,
      Math.ceil(this["im"] * places) / places
    );
  },
  /**
   * Floors the actual complex number
   *
   * @returns {Complex}
   */
  "floor": function(places) {
    places = Math.pow(10, places || 0);
    return new Complex(
      Math.floor(this["re"] * places) / places,
      Math.floor(this["im"] * places) / places
    );
  },
  /**
   * Ceils the actual complex number
   *
   * @returns {Complex}
   */
  "round": function(places) {
    places = Math.pow(10, places || 0);
    return new Complex(
      Math.round(this["re"] * places) / places,
      Math.round(this["im"] * places) / places
    );
  },
  /**
   * Compares two complex numbers
   *
   * **Note:** new Complex(Infinity).equals(Infinity) === false
   *
   * @returns {boolean}
   */
  "equals": function(a, b) {
    const z = parse(a, b);
    return Math.abs(z["re"] - this["re"]) <= Complex["EPSILON"] && Math.abs(z["im"] - this["im"]) <= Complex["EPSILON"];
  },
  /**
   * Clones the actual object
   *
   * @returns {Complex}
   */
  "clone": function() {
    return new Complex(this["re"], this["im"]);
  },
  /**
   * Gets a string of the actual complex number
   *
   * @returns {string}
   */
  "toString": function() {
    let a = this["re"];
    let b = this["im"];
    let ret = "";
    if (this["isNaN"]()) {
      return "NaN";
    }
    if (this["isInfinite"]()) {
      return "Infinity";
    }
    if (Math.abs(a) < Complex["EPSILON"]) {
      a = 0;
    }
    if (Math.abs(b) < Complex["EPSILON"]) {
      b = 0;
    }
    if (b === 0) {
      return ret + a;
    }
    if (a !== 0) {
      ret += a;
      ret += " ";
      if (b < 0) {
        b = -b;
        ret += "-";
      } else {
        ret += "+";
      }
      ret += " ";
    } else if (b < 0) {
      b = -b;
      ret += "-";
    }
    if (1 !== b) {
      ret += b;
    }
    return ret + "i";
  },
  /**
   * Returns the actual number as a vector
   *
   * @returns {Array}
   */
  "toVector": function() {
    return [this["re"], this["im"]];
  },
  /**
   * Returns the actual real value of the current object
   *
   * @returns {number|null}
   */
  "valueOf": function() {
    if (this["im"] === 0) {
      return this["re"];
    }
    return null;
  },
  /**
   * Determines whether a complex number is not on the Riemann sphere.
   *
   * @returns {boolean}
   */
  "isNaN": function() {
    return isNaN(this["re"]) || isNaN(this["im"]);
  },
  /**
   * Determines whether or not a complex number is at the zero pole of the
   * Riemann sphere.
   *
   * @returns {boolean}
   */
  "isZero": function() {
    return this["im"] === 0 && this["re"] === 0;
  },
  /**
   * Determines whether a complex number is not at the infinity pole of the
   * Riemann sphere.
   *
   * @returns {boolean}
   */
  "isFinite": function() {
    return isFinite(this["re"]) && isFinite(this["im"]);
  },
  /**
   * Determines whether or not a complex number is at the infinity pole of the
   * Riemann sphere.
   *
   * @returns {boolean}
   */
  "isInfinite": function() {
    return !this["isFinite"]();
  }
};
Complex["ZERO"] = new Complex(0, 0);
Complex["ONE"] = new Complex(1, 0);
Complex["I"] = new Complex(0, 1);
Complex["PI"] = new Complex(Math.PI, 0);
Complex["E"] = new Complex(Math.E, 0);
Complex["INFINITY"] = new Complex(Infinity, Infinity);
Complex["NAN"] = new Complex(NaN, NaN);
Complex["EPSILON"] = 1e-15;
exports.Complex = Complex;
exports.default = Complex;

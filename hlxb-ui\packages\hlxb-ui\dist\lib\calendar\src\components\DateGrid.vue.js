"use strict";
Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: "Module" } });
const vue = require("vue");
const DateCell_vue_vue_type_script_setup_true_lang = require("./DateCell.vue.js");
;/* empty css               */
const useCalendarContext = require("../hooks/useCalendarContext.js");
const prefixCls = require("../hooks/prefixCls.js");
const _hoisted_1 = {
  key: 0,
  class: "bg-month"
};
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "DateGrid"
  },
  __name: "DateGrid",
  props: {
    displayedDates: {}
  },
  emits: ["date-click"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const { calendarContext } = useCalendarContext.useCalendarContext();
    const emit = __emit;
    const datesByWeeks = vue.computed(() => {
      const weeks = [];
      const dates = props.displayedDates;
      for (let i = 0; i < dates.length; i += 7) {
        weeks.push(dates.slice(i, i + 7));
      }
      return weeks;
    });
    const handleDateClick = (date, dateItem) => {
      emit("date-click", date, dateItem);
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", {
        class: vue.normalizeClass(vue.unref(prefixCls.dateGridPrefixCls))
      }, [
        (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(datesByWeeks.value, (week, weekIndex) => {
          return vue.openBlock(), vue.createElementBlock("div", {
            key: weekIndex,
            class: "date-week"
          }, [
            (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(week, (dateInfo) => {
              return vue.openBlock(), vue.createBlock(DateCell_vue_vue_type_script_setup_true_lang.default, {
                key: dateInfo.dateString,
                dateInfo,
                onClick: _cache[0] || (_cache[0] = (date, dateItem) => handleDateClick(date, dateItem))
              }, {
                "date-cell": vue.withCtx(({ dateItem }) => [
                  vue.renderSlot(_ctx.$slots, "date-cell", { dateItem }, () => [
                    vue.createTextVNode(vue.toDisplayString(dateItem.date.date()), 1)
                  ])
                ]),
                _: 2
              }, 1032, ["dateInfo"]);
            }), 128))
          ]);
        }), 128)),
        vue.unref(calendarContext).isExpanded ? (vue.openBlock(), vue.createElementBlock("div", _hoisted_1, vue.toDisplayString(vue.unref(calendarContext).currentDate.month() + 1), 1)) : vue.createCommentVNode("", true)
      ], 2);
    };
  }
});
exports.default = _sfc_main;

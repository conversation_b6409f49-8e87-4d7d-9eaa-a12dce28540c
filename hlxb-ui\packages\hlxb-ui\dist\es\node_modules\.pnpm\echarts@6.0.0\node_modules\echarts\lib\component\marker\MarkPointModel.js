import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import MarkerModel from "./MarkerModel.js";
var MarkPointModel = (
  /** @class */
  function(_super) {
    __extends(MarkPointModel2, _super);
    function MarkPointModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = MarkPointModel2.type;
      return _this;
    }
    MarkPointModel2.prototype.createMarkerModelFromSeries = function(markerOpt, masterMarkerModel, ecModel) {
      return new MarkPointModel2(markerOpt, masterMarkerModel, ecModel);
    };
    MarkPointModel2.type = "markPoint";
    MarkPointModel2.defaultOption = {
      // zlevel: 0,
      z: 5,
      symbol: "pin",
      symbolSize: 50,
      // symbolRotate: 0,
      // symbolOffset: [0, 0]
      tooltip: {
        trigger: "item"
      },
      label: {
        show: true,
        position: "inside"
      },
      itemStyle: {
        borderWidth: 2
      },
      emphasis: {
        label: {
          show: true
        }
      }
    };
    return MarkPointModel2;
  }(<PERSON>erModel)
);
export {
  MarkPointModel as default
};

import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { reduce, createHashMap, isFunction, isString } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { defaultEmphasis } from "../../util/model.js";
import ComponentModel from "../../model/Component.js";
import Model from "../../model/Model.js";
import geoCreator from "./geoCreator.js";
import geoSourceManager from "./geoSourceManager.js";
import tokens from "../../visual/tokens.js";
var GeoModel = (
  /** @class */
  function(_super) {
    __extends(GeoModel2, _super);
    function GeoModel2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = GeoModel2.type;
      return _this;
    }
    GeoModel2.prototype.init = function(option, parentModel, ecModel) {
      this.mergeDefaultAndTheme(option, ecModel);
      var source = geoSourceManager.getGeoResource(option.map);
      if (source && source.type === "geoJSON") {
        var itemStyle = option.itemStyle = option.itemStyle || {};
        if (!("color" in itemStyle)) {
          itemStyle.color = option.defaultItemStyleColor || tokens.color.backgroundTint;
        }
      }
      defaultEmphasis(option, "label", ["show"]);
    };
    GeoModel2.prototype.optionUpdated = function() {
      var _this = this;
      var option = this.option;
      option.regions = geoCreator.getFilledRegions(option.regions, option.map, option.nameMap, option.nameProperty);
      var selectedMap = {};
      this._optionModelMap = reduce(option.regions || [], function(optionModelMap, regionOpt) {
        var regionName = regionOpt.name;
        if (regionName) {
          optionModelMap.set(regionName, new Model(regionOpt, _this, _this.ecModel));
          if (regionOpt.selected) {
            selectedMap[regionName] = true;
          }
        }
        return optionModelMap;
      }, createHashMap());
      if (!option.selectedMap) {
        option.selectedMap = selectedMap;
      }
    };
    GeoModel2.prototype.getRegionModel = function(name) {
      return this._optionModelMap.get(name) || new Model(null, this, this.ecModel);
    };
    GeoModel2.prototype.getFormattedLabel = function(name, status) {
      var regionModel = this.getRegionModel(name);
      var formatter = status === "normal" ? regionModel.get(["label", "formatter"]) : regionModel.get(["emphasis", "label", "formatter"]);
      var params = {
        name
      };
      if (isFunction(formatter)) {
        params.status = status;
        return formatter(params);
      } else if (isString(formatter)) {
        return formatter.replace("{a}", name != null ? name : "");
      }
    };
    GeoModel2.prototype.setZoom = function(zoom) {
      this.option.zoom = zoom;
    };
    GeoModel2.prototype.setCenter = function(center) {
      this.option.center = center;
    };
    GeoModel2.prototype.select = function(name) {
      var option = this.option;
      var selectedMode = option.selectedMode;
      if (!selectedMode) {
        return;
      }
      if (selectedMode !== "multiple") {
        option.selectedMap = null;
      }
      var selectedMap = option.selectedMap || (option.selectedMap = {});
      selectedMap[name] = true;
    };
    GeoModel2.prototype.unSelect = function(name) {
      var selectedMap = this.option.selectedMap;
      if (selectedMap) {
        selectedMap[name] = false;
      }
    };
    GeoModel2.prototype.toggleSelected = function(name) {
      this[this.isSelected(name) ? "unSelect" : "select"](name);
    };
    GeoModel2.prototype.isSelected = function(name) {
      var selectedMap = this.option.selectedMap;
      return !!(selectedMap && selectedMap[name]);
    };
    GeoModel2.type = "geo";
    GeoModel2.layoutMode = "box";
    GeoModel2.defaultOption = {
      // zlevel: 0,
      z: 0,
      show: true,
      left: "center",
      top: "center",
      // Default value:
      // for geoSVG source: 1,
      // for geoJSON source: 0.75.
      aspectScale: null,
      // /// Layout with center and size
      // If you want to put map in a fixed size box with right aspect ratio
      // This two properties may be more convenient
      // layoutCenter: [50%, 50%]
      // layoutSize: 100
      silent: false,
      // Map type
      map: "",
      // Define left-top, right-bottom coords to control view
      // For example, [ [180, 90], [-180, -90] ]
      boundingCoords: null,
      // Default on center of map
      center: null,
      zoom: 1,
      scaleLimit: null,
      // selectedMode: false
      label: {
        show: false,
        color: tokens.color.tertiary
      },
      itemStyle: {
        borderWidth: 0.5,
        borderColor: tokens.color.border
      },
      emphasis: {
        label: {
          show: true,
          color: tokens.color.primary
        },
        itemStyle: {
          color: tokens.color.highlight
        }
      },
      select: {
        label: {
          show: true,
          color: tokens.color.primary
        },
        itemStyle: {
          color: tokens.color.highlight
        }
      },
      regions: []
      // tooltip: {
      //     show: false
      // }
    };
    return GeoModel2;
  }(ComponentModel)
);
export {
  GeoModel as default
};

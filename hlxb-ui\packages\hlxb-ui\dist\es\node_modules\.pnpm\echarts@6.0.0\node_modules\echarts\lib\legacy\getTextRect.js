import "../util/graphic.js";
import ZRText from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
function getTextRect(text, font, align, verticalAlign, padding, rich, truncate, lineHeight) {
  var textEl = new ZRText({
    style: {
      text,
      font,
      align,
      verticalAlign,
      padding,
      rich,
      overflow: truncate ? "truncate" : null,
      lineHeight
    }
  });
  return textEl.getBoundingRect();
}
export {
  getTextRect
};

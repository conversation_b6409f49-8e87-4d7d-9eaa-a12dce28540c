import { defineComponent, createElementBlock, createCommentVNode, unref, openBlock, normalizeClass, renderSlot, createTextVNode, createVNode, toDisplayString } from "vue";
import { expandBtnPrefixCls } from "../hooks/prefixCls.js";
import _sfc_main$1 from "./DoubleDownIcon.vue2.js";
import { useCalendarContext } from "../hooks/useCalendarContext.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "ExpandButton"
  },
  __name: "ExpandButton",
  emits: ["toggle-expand"],
  setup(__props, { emit: __emit }) {
    const { calendarContext } = useCalendarContext();
    const emit = __emit;
    const handleClick = () => {
      emit("toggle-expand");
    };
    return (_ctx, _cache) => {
      return unref(calendarContext).options.showExpandButton ? (openBlock(), createElementBlock("div", {
        key: 0,
        onClick: handleClick,
        class: normalizeClass(unref(expandBtnPrefixCls))
      }, [
        renderSlot(_ctx.$slots, "expand-button", {
          isExpanded: unref(calendarContext).isExpanded
        }, () => [
          createTextVNode(toDisplayString(unref(calendarContext).isExpanded ? "收起" : "展开") + " ", 1),
          createVNode(_sfc_main$1, {
            isExpanded: unref(calendarContext).isExpanded
          }, null, 8, ["isExpanded"])
        ])
      ], 2)) : createCommentVNode("", true);
    };
  }
});
export {
  _sfc_main as default
};

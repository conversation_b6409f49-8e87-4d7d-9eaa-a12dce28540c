.hlxb-button {
  /**  圆角  **/
  /**  size  **/
}
.hlxb-button.r-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  transition: 0.1s;
  font-weight: 500;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px;
}
.hlxb-button.r-button + .r-button {
  margin-left: 10px;
}
.hlxb-button.r-button:focus,
.hlxb-button.r-button:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}
.hlxb-button.r-button:active {
  color: #3a8ee6;
  border-color: #3a8ee6;
  outline: 0;
}
.hlxb-button.r-button.is-disabled,
.hlxb-button.r-button.is-disabled:focus,
.hlxb-button.r-button.is-disabled:hover {
  color: #c0c4cc;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff;
  border-color: #ebeef5;
}
.hlxb-button.r-button--primary {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}
.hlxb-button.r-button--primary:focus,
.hlxb-button.r-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}
.hlxb-button.r-button--primary.is-disabled,
.hlxb-button.r-button--primary.is-disabled:active,
.hlxb-button.r-button--primary.is-disabled:focus,
.hlxb-button.r-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #a0cfff;
  border-color: #a0cfff;
}
.hlxb-button.r-button--success {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}
.hlxb-button.r-button--success:focus,
.hlxb-button.r-button--success:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}
.hlxb-button.r-button--success.is-active,
.hlxb-button.r-button--success:active {
  background: #5daf34;
  border-color: #5daf34;
  color: #fff;
}
.hlxb-button.r-button--success:active {
  outline: 0;
}
.hlxb-button.r-button--success.is-disabled,
.hlxb-button.r-button--success.is-disabled:active,
.hlxb-button.r-button--success.is-disabled:focus,
.hlxb-button.r-button--success.is-disabled:hover {
  color: #fff;
  background-color: #b3e19d;
  border-color: #b3e19d;
}
.hlxb-button.r-button--warning {
  color: #fff;
  background-color: #e6a23c;
  border-color: #e6a23c;
}
.hlxb-button.r-button--warning:focus,
.hlxb-button.r-button--warning:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}
.hlxb-button.r-button--warning.is-active,
.hlxb-button.r-button--warning:active {
  background: #cf9236;
  border-color: #cf9236;
  color: #fff;
}
.hlxb-button.r-button--warning:active {
  outline: 0;
}
.hlxb-button.r-button--warning.is-disabled,
.hlxb-button.r-button--warning.is-disabled:active,
.hlxb-button.r-button--warning.is-disabled:focus,
.hlxb-button.r-button--warning.is-disabled:hover {
  color: #fff;
  background-color: #f3d19e;
  border-color: #f3d19e;
}
.hlxb-button.r-button--danger {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
.hlxb-button.r-button--danger:focus,
.hlxb-button.r-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}
.hlxb-button.r-button--danger.is-active,
.hlxb-button.r-button--danger:active {
  background: #dd6161;
  border-color: #dd6161;
  color: #fff;
}
.hlxb-button.r-button--danger:active {
  outline: 0;
}
.hlxb-button.r-button--danger.is-disabled,
.hlxb-button.r-button--danger.is-disabled:active,
.hlxb-button.r-button--danger.is-disabled:focus,
.hlxb-button.r-button--danger.is-disabled:hover {
  color: #fff;
  background-color: #fab6b6;
  border-color: #fab6b6;
}
.hlxb-button.r-button--info {
  color: #fff;
  background-color: #909399;
  border-color: #909399;
}
.hlxb-button.r-button--info:focus,
.hlxb-button.r-button--info:hover {
  background: #a6a9ad;
  border-color: #a6a9ad;
  color: #fff;
}
.hlxb-button.r-button--info.is-active,
.hlxb-button.r-button--info:active {
  background: #82848a;
  border-color: #82848a;
  color: #fff;
}
.hlxb-button.r-button--info:active {
  outline: 0;
}
.hlxb-button.r-button--info.is-disabled,
.hlxb-button.r-button--info.is-disabled:active,
.hlxb-button.r-button--info.is-disabled:focus,
.hlxb-button.r-button--info.is-disabled:hover {
  color: #fff;
  background-color: #c8c9cc;
  border-color: #c8c9cc;
}
.hlxb-button.r-button.is-round {
  border-radius: 20px;
  padding: 12px 23px;
}
.hlxb-button.r-button--medium {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px;
}
.hlxb-button.r-button--mini,
.hlxb-button.r-button--small {
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button--small {
  padding: 9px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button--mini {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 3px;
}
.hlxb-button.r-button::-moz-focus-inner {
  border: 0;
}
.hlxb-icon[data-v-d7cc5ab6] {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentcolor;
  overflow: hidden;
}
.hlxb-input.inline-container {
  width: 100%;
  display: inline-flex;
}
.hlxb-input.inline-container .r-input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
.hlxb-input.inline-container .r-input.is-disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
.hlxb-input.inline-container .r-input.is-center {
  text-align: center;
}
.hlxb-input.inline-container .r-input:hover {
  border-color: #c0c4cc;
}
.hlxb-input.inline-container .r-input:focus {
  outline: none;
  border-color: #409eff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar {
  z-index: 11;
  width: 6px;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar:horizontal {
  height: 6px;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 6px;
  background: #b4bccc;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-corner {
  background: #fff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-track {
  background: #fff;
}
.hlxb-input.inline-container .r-input::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 6px;
}
.hlxb-input.inline-container .r-textarea {
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.hlxb-input.inline-container .r-textarea:focus {
  outline: none;
  border-color: #409eff;
}
.hlxb-input.inline-container .r-input-prepend,
.hlxb-input.inline-container .r-input-append {
  background-color: #f5f7fa;
  color: #909399;
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 0;
  padding: 0 10px;
  white-space: nowrap;
  width: -moz-max-content;
  width: max-content;
}
.hlxb-input.inline-container.has-prepend .r-input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}
.hlxb-input.inline-container.has-append .r-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}
.hlxb-input.inline-container.has-prepend .r-input-prepend {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.hlxb-input.inline-container.has-prepend .r-input-append {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.hlxb-input.inline-container.has-append .r-input-append {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.hlxb-input.inline-container .r-input--medium {
  height: 36px;
  line-height: 36px;
}
.hlxb-input.inline-container .r-input--small {
  height: 32px;
  line-height: 32px;
}
.hlxb-input.inline-container .r-input-outer {
  width: 100%;
  position: relative;
}
.hlxb-input.inline-container .r-input-clear {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  cursor: pointer;
}.hlxb-input-number.is-disabled {
  cursor: not-allowed !important;
}
.hlxb-input-number.cursor-pointer {
  cursor: pointer;
}
.hlxb-input-number.input-number-inner {
  width: 150px;
}
.hlxb-radio.r-radio[data-v-2d9e8ee7] {
  color: #606266;
  margin-right: 10px;
  cursor: pointer;
  font-weight: 500;
}
.hlxb-radio.r-radio .r-radio--outer[data-v-2d9e8ee7] {
  white-space: nowrap;
  outline: none;
  display: inline-block;
  line-height: 1;
  position: relative;
  vertical-align: middle;
}
.hlxb-radio.r-radio .r-radio--inner[data-v-2d9e8ee7] {
  border: 1px solid #dcdfe6;
  border-radius: 100%;
  width: 14px;
  height: 14px;
  background-color: #fff;
  position: relative;
  cursor: pointer;
  display: inline-block;
  box-sizing: border-box;
}
.hlxb-radio.r-radio .r-radio--inner.is-checked[data-v-2d9e8ee7] {
  border-color: #409eff;
  background: #409eff;
}
.hlxb-radio.r-radio .r-radio--inner[data-v-2d9e8ee7]::after {
  width: 4px;
  height: 4px;
  border-radius: 100%;
  background-color: #fff;
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.15s ease-in;
}
.hlxb-radio.r-radio .r-radio--inner.is-checked[data-v-2d9e8ee7]::after {
  transform: translate(-50%, -50%) scale(1);
}
.hlxb-radio.r-radio .r-radio--label[data-v-2d9e8ee7] {
  margin-left: 4px;
  font-size: 14px;
}
.hlxb-radio.r-radio .r-radio[data-v-2d9e8ee7]:last-child {
  margin-right: 0;
}
.hlxb-radio.r-radio .r-radio-native[data-v-2d9e8ee7] {
  opacity: 0;
  display: none;
}
.hlxb-checkbox.r-checkbox[data-v-eefd8a13] {
  color: #606266;
  margin-right: 10px;
  cursor: pointer;
}
.hlxb-checkbox .r-checkbox--outer[data-v-eefd8a13] {
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  display: inline-block;
  line-height: 1;
  position: relative;
  vertical-align: middle;
}
.hlxb-checkbox .r-checkbox--inner[data-v-eefd8a13] {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
}
.hlxb-checkbox .r-checkbox--inner[data-v-eefd8a13]::after {
  box-sizing: content-box;
  content: '';
  border: 1px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(0);
  width: 3px;
  transition: transform 0.15s ease-in 0.05s;
  transform-origin: center;
}
.hlxb-checkbox .r-checkbox--inner.is-checked[data-v-eefd8a13] {
  background-color: #409eff;
  border-color: #409eff;
}
.hlxb-checkbox .r-checkbox--inner.is-checked[data-v-eefd8a13]::after {
  transform: rotate(45deg) scaleY(1);
}
.hlxb-checkbox .r-checkbox-native[data-v-eefd8a13] {
  opacity: 0;
  display: none;
}
.hlxb-checkbox .r-checkbox__label[data-v-eefd8a13] {
  margin-left: 10px;
  display: inline-block;
  line-height: 19px;
  font-size: 14px;
}
.hlxb-calendar-date-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
  font-weight: bold;
}
.hlxb-calendar-date-cell:hover {
  background-color: #f3f4f6;
}
.hlxb-calendar-date-cell.current-month {
  color: #333;
}
.hlxb-calendar-date-cell.other-month {
  color: #999;
}
.hlxb-calendar-date-cell.selected {
  border: var(--theme-color) 1px solid;
}
.hlxb-calendar-date-cell.today {
  background-color: var(--theme-color);
  color: white;
}
.hlxb-calendar-date-cell .calendar-indicator {
  position: absolute;
  border-radius: 50%;
  z-index: 3;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-center {
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-right {
  bottom: 2px;
  right: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-bottom-left {
  bottom: 2px;
  left: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-top-right {
  top: 2px;
  right: 2px;
}
.hlxb-calendar-date-cell .calendar-indicator.indicator-top-left {
  top: 2px;
  left: 2px;
}
.hlxb-calendar-date-grid {
  min-height: 0;
  position: relative;
}
.hlxb-calendar-date-grid .bg-month {
  position: absolute;
  z-index: 0;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 160px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.06);
}
.hlxb-calendar-date-grid .date-week {
  display: flex;
  justify-content: space-around;
  height: 32px;
  margin-top: 8px;
}
.hlxb-calendar-expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  color: #999;
  cursor: pointer;
  position: relative;
  z-index: 2;
  transition: all 0.5s;
}
.hlxb-calendar-expand-btn:hover {
  color: var(--theme-color);
}
.hlxb-calendar-expand-btn .double-down-icon {
  margin-left: 4px;
  transition: all 0.5s;
}
.hlxb-calendar-expand-btn .double-down-icon.isExpanded {
  transform: rotateZ(180deg);
}
.hlxb-calendar-weekdays-header {
  display: flex;
  justify-content: space-around;
  height: 32px;
  background-color: #f2f3f5;
  border-radius: 4px;
}
.hlxb-calendar-weekdays-header .weekday {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  height: 100%;
  width: 32px;
}
.hlxb-calendar {
  margin: 0 20px;
  border-bottom: #ebebeb 1px solid;
}
.hlxb-grid .grid-item {
  transition: all 0.2s;
  border-radius: 4px;
}
.hlxb-grid .grid-item.hover-show:hover {
  background-color: #f0f0f0;
}
.hlxb-grid .nav-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
/**
 * Swiper 10.3.1
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2023 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: September 28, 2023
 */
/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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');
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  overflow: clip;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */
/**
 * Swiper 10.3.1
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2023 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: September 28, 2023
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url('data:application/font-woff;charset=utf-8;base64, 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');
  font-weight: 400;
  font-style: normal;
}

/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  overflow: clip;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}

/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}

/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}

/* Slide styles start */

/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}

/* Slide styles end */
.swiper-virtual .swiper-slide {
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
}
.swiper-virtual.swiper-css-mode .swiper-wrapper::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after {
  height: 1px;
  width: var(--swiper-virtual-size);
}
.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after {
  width: 1px;
  height: var(--swiper-virtual-size);
}
:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}

/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}

/* Navigation font end */
:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}

/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}

/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
          -moz-appearance: none;
       appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet:only-child {
  display: none !important;
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}
.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}
.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform, 200ms top;
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms left;
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}

/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}

/* Progress */
.swiper-pagination-progressbar {
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}
.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}
.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}
.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-lock {
  display: none;
}
:root {
  /*
  --swiper-scrollbar-border-radius: 10px;
  --swiper-scrollbar-top: auto;
  --swiper-scrollbar-bottom: 4px;
  --swiper-scrollbar-left: auto;
  --swiper-scrollbar-right: 4px;
  --swiper-scrollbar-sides-offset: 1%;
  --swiper-scrollbar-bg-color: rgba(0, 0, 0, 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(0, 0, 0, 0.5);
  --swiper-scrollbar-size: 4px;
  */
}
.swiper-scrollbar {
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  position: relative;
  touch-action: none;
  background: var(--swiper-scrollbar-bg-color, rgba(0, 0, 0, 0.1));
}
.swiper-scrollbar-disabled > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-disabled {
  display: none !important;
}
.swiper-horizontal > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-horizontal {
  position: absolute;
  left: var(--swiper-scrollbar-sides-offset, 1%);
  bottom: var(--swiper-scrollbar-bottom, 4px);
  top: var(--swiper-scrollbar-top, auto);
  z-index: 50;
  height: var(--swiper-scrollbar-size, 4px);
  width: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}
.swiper-vertical > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-vertical {
  position: absolute;
  left: var(--swiper-scrollbar-left, auto);
  right: var(--swiper-scrollbar-right, 4px);
  top: var(--swiper-scrollbar-sides-offset, 1%);
  z-index: 50;
  width: var(--swiper-scrollbar-size, 4px);
  height: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}
.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--swiper-scrollbar-drag-bg-color, rgba(0, 0, 0, 0.5));
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  left: 0;
  top: 0;
}
.swiper-scrollbar-cursor-drag {
  cursor: move;
}
.swiper-scrollbar-lock {
  display: none;
}

/* Zoom container styles start */
.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.swiper-zoom-container > img,
.swiper-zoom-container > svg,
.swiper-zoom-container > canvas {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}

/* Zoom container styles end */
.swiper-slide-zoomed {
  cursor: move;
  touch-action: none;
}

/* a11y */
.swiper .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}
.swiper-free-mode > .swiper-wrapper {
  transition-timing-function: ease-out;
  margin: 0 auto;
}
.swiper-grid > .swiper-wrapper {
  flex-wrap: wrap;
}
.swiper-grid-column > .swiper-wrapper {
  flex-wrap: wrap;
  flex-direction: column;
}
.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}
.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}
.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-cube {
  overflow: visible;
}
.swiper-cube .swiper-slide {
  pointer-events: none;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}
.swiper-cube .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-cube.swiper-rtl .swiper-slide {
  transform-origin: 100% 0;
}
.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-next,
.swiper-cube .swiper-slide-prev {
  pointer-events: auto;
  visibility: visible;
}
.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
}
.swiper-cube .swiper-cube-shadow:before {
  content: '';
  background: #000;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px);
}
.swiper-cube .swiper-slide-next + .swiper-slide {
  pointer-events: auto;
  visibility: visible;
}

/* Cube slide shadows start */
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right {
  z-index: 0;
  backface-visibility: hidden;
}

/* Cube slide shadows end */
.swiper-flip {
  overflow: visible;
}
.swiper-flip .swiper-slide {
  pointer-events: none;
  backface-visibility: hidden;
  z-index: 1;
}
.swiper-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-flip .swiper-slide-active,
.swiper-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

/* Flip slide shadows start */
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right {
  z-index: 0;
  backface-visibility: hidden;
}

/* Flip slide shadows end */
.swiper-creative .swiper-slide {
  backface-visibility: hidden;
  overflow: hidden;
  transition-property: transform, opacity, height;
}
.swiper-cards {
  overflow: visible;
}
.swiper-cards .swiper-slide {
  transform-origin: center bottom;
  backface-visibility: hidden;
  overflow: hidden;
}
.hlxb-swiper {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}
.hlxb-swiper .swiper {
  width: 100%;
  height: 100%;
}
.hlxb-swiper .swiper .shadow {
  width: 100%;
  height: 103px;
  border-radius: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  z-index: 1;
}
.hlxb-swiper .swiper img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
}
.hlxb-swiper .swiper .swiper-pagination {
  bottom: 16px;
  height: 8px;
  display: flex;
  justify-content: center;
  z-index: 2;
}
.hlxb-swiper .swiper .swiper-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  transition: all 0.2s;
  margin: 0 4px;
}
.hlxb-swiper .swiper .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 26px;
  background: #fff;
}
.hlxb-swiper .swiper .swiper-button-prev,
.hlxb-swiper .swiper .swiper-button-next {
  color: #fff;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  transition: all 0.3s;
}
.hlxb-swiper .swiper .swiper-button-prev:hover,
.hlxb-swiper .swiper .swiper-button-next:hover {
  background: rgba(0, 0, 0, 0.6);
}
.hlxb-swiper .swiper .swiper-button-prev::after,
.hlxb-swiper .swiper .swiper-button-next::after {
  font-size: 16px;
  font-weight: bold;
}
.hlxb-swiper .swiper .swiper-button-prev {
  left: 10px;
}
.hlxb-swiper .swiper .swiper-button-next {
  right: 10px;
}
.hlxb-swiper .swiper .swiper-scrollbar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  height: 4px;
}
.hlxb-swiper .swiper .swiper-scrollbar .swiper-scrollbar-drag {
  background: #fff;
  border-radius: 4px;
}
.hlxb-swiper .swiper-slide {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.hlxb-weather {
  background: linear-gradient(92deg, #93e9fa 0%, #35abff 100%);
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  color: #333;
  display: flex;
}
.hlxb-weather.layout-vertical {
  flex-direction: column-reverse;
  align-items: center;
}
.hlxb-weather.layout-vertical .weather-container {
  width: -moz-fit-content;
  width: fit-content;
  align-items: center;
  padding-top: 12px;
}
.hlxb-weather.layout-vertical .weather-container .date-section {
  width: -moz-fit-content;
  width: fit-content;
}
.hlxb-weather.layout-vertical .weather-container .date-section .date-line {
  margin-right: 0;
}
.hlxb-weather.layout-vertical .weather-decoration {
  padding-right: 0;
}
.hlxb-weather .weather-container {
  padding: 20px;
  height: 100%;
  flex: 1;
  color: #333;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.hlxb-weather .weather-container .weather-top {
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .weather-top .temperature-section {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-right: 16px;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status {
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status .weather-text {
  font-size: 16px;
  font-weight: 400;
}
.hlxb-weather .weather-container .weather-top .weather-info .weather-status .wind-icon {
  width: 33px;
  height: 26px;
  margin-left: 12px;
}
.hlxb-weather .weather-container .date-section {
  width: 100%;
  display: flex;
  align-items: center;
}
.hlxb-weather .weather-container .date-section .date-line {
  font-size: 14px;
  font-weight: bold;
  margin-right: 24px;
}
.hlxb-weather .weather-container .date-section .forecast {
  font-size: 14px;
}
.hlxb-weather .weather-decoration {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 32px;
}
.hlxb-card-header {
  display: flex;
  align-items: center;
  padding: 0 16px;
  color: #333;
  min-height: 48px;
  justify-content: space-between;
  /* 底部分割线样式 */
  /* 左侧内容容器样式 */
  /* 标题文本样式 */
}
.hlxb-card-header.base-header_line {
  border-bottom: 1px solid #e9e9e9;
}
.hlxb-card-header .header_left {
  display: flex;
  /* gap: 0 16px; */
  align-items: center;
}
.hlxb-card-header .text {
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}
.hlxb-card-header.Dark {
  color: #fff;
}
.hlxb-card-header.light {
  color: #333;
}
.hlxb-card-header.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-card-body {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 16px;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
}
.hlxb-card-body.Dark {
  color: #fff;
}
.hlxb-card-body.light {
  color: #333;
}
.hlxb-card-body.screenColor {
  color: #fff;
  background: transparent;
}
/* 卡片基础样式 */
.hlxb-card {
  width: 100%;
  background-color: #fcfcfc;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* 卡片内容容器样式 */
}
.hlxb-card .container-content {
  flex: 1;
  overflow: hidden;
}
.hlxb-card.Dark {
  color: #fff;
}
.hlxb-card.light {
  color: #333;
}
.hlxb-card.screenColor {
  color: #fff;
  background-color: transparent;
}
.hlxb-ranking {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-ranking.Dark {
  color: #fff;
}
.hlxb-ranking.light {
  color: #333;
}
.hlxb-ranking.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-ranking .item-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}
.hlxb-ranking .item-list .activeItem {
  position: relative;
  border-radius: 4px;
  border: 1px solid var(--theme-color-88p) !important;
}
.hlxb-ranking .item-list .activeItem .img_box {
  width: 20px;
  height: 20px;
  position: absolute;
  right: 0;
  bottom: 0;
}
.hlxb-ranking .item-list .hoverItem {
  cursor: pointer;
}
.hlxb-ranking .item-list .hoverItem:hover {
  border: 1px solid var(--theme-color-88p);
}
.hlxb-ranking .item-list .item {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #fff;
}
.hlxb-ranking .item-list .item .top-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.hlxb-ranking .item-list .item .bar-box {
  width: 100%;
  margin-top: 12px;
  height: 12px;
  background: #eeeff1;
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .bar-box .bar-conter {
  height: 12px;
  background: #999;
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .bar-box .abar-conter {
  height: 12px;
  background: var(--theme-color-88p);
  border-radius: 4px;
}
.hlxb-ranking .item-list .item .abar-box {
  background: rgba(11, 98, 203, 0.16);
}
.hlxb-ranking .item-list .item .item-index {
  flex: 1;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #333;
}
.hlxb-ranking .item-list .item .item-index .num-index {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
}
.hlxb-ranking .item-list .item .item-index .label {
  padding-left: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;
  width: 120px;
  white-space: nowrap;
  /* 让文本不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号代替超出部分 */
}
.hlxb-ranking .item-list .item .item-content {
  flex: 1;
  display: flex;
  align-items: baseline;
  justify-content: end;
}
.hlxb-ranking .item-list .item .item-content .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
}
.hlxb-ranking .item-list .item .item-content .unit {
  font-weight: 400;
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}
.hlxb-ranking .item-list .item .num-index {
  color: #fff;
  background: url('../../assets/images/item_fo.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(1) .num-index {
  background: url('../../assets/images/item_o.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(2) .num-index {
  color: #fff;
  background: url('../../assets/images/item_tw.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking .item-list .item:nth-child(3) .num-index {
  color: #fff;
  background: url('../../assets/images/item_th.png') no-repeat center;
  background-size: 100% 100%;
}
.hlxb-ranking-simple {
  width: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}
.hlxb-ranking-simple.Dark {
  color: #fff;
}
.hlxb-ranking-simple.light {
  color: #333;
}
.hlxb-ranking-simple.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-ranking-simple .item-t {
  background: rgba(11, 98, 203, 0.08);
}
.hlxb-ranking-simple .item-t .num-index {
  color: #fff;
  background: var(--theme-color);
}
.hlxb-ranking-simple .empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999;
}
.hlxb-ranking-simple .item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 16px;
  border-radius: 4px;
  background: #f2f3f5;
  color: #fff;
}
.hlxb-ranking-simple .item .item-index {
  flex: 1;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 14px;
  color: #333;
}
.hlxb-ranking-simple .item .item-index .num-index {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  background: #999;
  color: #fff;
  border-radius: 10px;
}
.hlxb-ranking-simple .item .item-index .label {
  font-weight: 400;
  font-size: 14px;
  width: 120px;
  white-space: nowrap;
  /* 让文本不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 使用省略号代替超出部分 */
}
.hlxb-ranking-simple .item .item-content {
  flex: 1;
  display: flex;
  align-items: baseline;
  justify-content: end;
}
.hlxb-ranking-simple .item .item-content .value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
}
.hlxb-ranking-simple .item .item-content .unit {
  font-weight: 400;
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}
.hlxb-ranking-simple .item:nth-child(1),
.hlxb-ranking-simple .item:nth-child(2),
.hlxb-ranking-simple .item:nth-child(3) {
  background: rgba(11, 98, 203, 0.08);
}
.hlxb-ranking-simple .item:nth-child(1) .num-index,
.hlxb-ranking-simple .item:nth-child(2) .num-index,
.hlxb-ranking-simple .item:nth-child(3) .num-index {
  color: #fff;
  background: var(--theme-color);
}
.hlxb-energy-summary {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-energy-summary.Dark {
  color: #fff;
}
.hlxb-energy-summary.light {
  color: #333;
}
.hlxb-energy-summary.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-energy-summary .empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hlxb-energy-summary .container-list {
  display: flex;
  flex-direction: column;
  gap: 16px 0;
  height: 100%;
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item {
  padding: 0 12px 0 0;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  height: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hlxb-energy-summary .container-list .list-item .item {
  width: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
}
.hlxb-energy-summary .container-list .list-item .item .conten-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: self-start;
  overflow: hidden;
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item .item .name {
  width: 100%;
  display: flex;
  padding-left: 24px;
  justify-content: flex-start;
  align-items: center;
}
.hlxb-energy-summary .container-list .list-item .item .name .icon {
  width: 24px;
  height: 24px;
}
.hlxb-energy-summary .container-list .list-item .item .name .icon img {
  width: 100%;
}
.hlxb-energy-summary .container-list .list-item .item .name .text {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-energy-summary .container-list .list-item .item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  width: 100%;
  justify-content: start;
  padding-left: 24px;
  overflow: hidden;
  align-items: end;
}
.hlxb-energy-summary .container-list .list-item .item .value .number-value {
  font-family: 'D-DIN-PRO, D-DIN-PRO';
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-energy-summary .container-list .list-item .item .value .unitLevel {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-bottom: 3px;
}
.hlxb-energy-summary .container-list .list-item .item .value .unit {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-energy-summary .container-list .list-item .item .value .ups-icon {
  display: inline-block;
  margin-left: 6px;
}
.hlxb-energy-summary .container-list .list-item .item .value-before {
  margin: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .conten-box {
  padding-left: 24px;
  padding-right: 24px;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .name {
  padding-left: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(1) .value {
  padding-left: 0;
}
.hlxb-energy-summary .container-list .list-item .item:nth-child(2) .value,
.hlxb-energy-summary .container-list .list-item .item:nth-child(3) .value {
  padding-left: 24px;
}
.hlxb-energy-summary .container-list .list-item .item-after::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 34px;
  background-color: #bebebe;
}
.hlxb-drug-summary {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-drug-summary.Dark {
  color: #fff;
}
.hlxb-drug-summary.light {
  color: #333;
}
.hlxb-drug-summary.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-drug-summary .empty {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hlxb-drug-summary .container-list {
  display: flex;
  flex-direction: column;
  gap: 16px 0;
  height: 100%;
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item {
  padding: 0 12px 0 0;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  height: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hlxb-drug-summary .container-list .list-item .item {
  width: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  align-items: center;
}
.hlxb-drug-summary .container-list .list-item .item .conten-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: self-start;
  overflow: hidden;
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item .item .name {
  width: 100%;
  display: flex;
  padding-left: 24px;
  justify-content: flex-start;
  align-items: center;
}
.hlxb-drug-summary .container-list .list-item .item .name .icon {
  width: 24px;
  height: 24px;
}
.hlxb-drug-summary .container-list .list-item .item .name .icon img {
  width: 100%;
}
.hlxb-drug-summary .container-list .list-item .item .name .text {
  font-size: 14px;
  color: #333;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-drug-summary .container-list .list-item .item .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  width: 100%;
  justify-content: start;
  padding-left: 24px;
  overflow: hidden;
  align-items: end;
}
.hlxb-drug-summary .container-list .list-item .item .value .number-value {
  font-family: 'D-DIN-PRO, D-DIN-PRO';
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-drug-summary .container-list .list-item .item .value .unitLevel {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-bottom: 3px;
}
.hlxb-drug-summary .container-list .list-item .item .value .unit {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-drug-summary .container-list .list-item .item .value .ups-icon {
  display: inline-block;
  margin-left: 6px;
}
.hlxb-drug-summary .container-list .list-item .item .value-before {
  margin: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .conten-box {
  padding-left: 24px;
  padding-right: 24px;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .name {
  padding-left: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(1) .value {
  padding-left: 0;
}
.hlxb-drug-summary .container-list .list-item .item:nth-child(2) .value,
.hlxb-drug-summary .container-list .list-item .item:nth-child(3) .value {
  padding-left: 24px;
}
.hlxb-drug-summary .container-list .list-item .item-after::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 34px;
  background-color: #bebebe;
}
.hlxb-horizonta-small-square {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.hlxb-horizonta-small-square.Dark {
  color: #fff;
}
.hlxb-horizonta-small-square.light {
  color: #333;
}
.hlxb-horizonta-small-square.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-horizonta-small-square .extra-wrap :deep(.ant-select) {
  margin-right: 16px;
}
.hlxb-horizonta-small-square .total-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 17px;
  overflow-x: auto;
}
.hlxb-horizonta-small-square .total-list .item {
  height: 100%;
  flex: 1;
  background: rgba(11, 98, 203, 0.08);
  border-radius: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}
.hlxb-horizonta-small-square .total-list .item .icon {
  width: 40px;
  height: 40px;
}
.hlxb-horizonta-small-square .total-list .item .content-column {
  flex-direction: column;
}
.hlxb-horizonta-small-square .total-list .item .content {
  display: flex;
  flex: 1;
  padding: 16px;
  justify-content: space-between;
  align-items: start;
  gap: 8px 0;
}
.hlxb-horizonta-small-square .total-list .item .content .name {
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  line-height: 15px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_0::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(46, 196, 255, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_1::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(67, 92, 255, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_2::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(34, 205, 128, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_3::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(255, 140, 46, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_4::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(237, 210, 38, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .beforecor_5::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin-right: 8px;
  vertical-align: middle;
  background: rgba(255, 82, 43, 0.8);
}
.hlxb-horizonta-small-square .total-list .item .content .value_pleft {
  padding-left: 14px;
}
.hlxb-horizonta-small-square .total-list .item .content .value {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: flex-end;
  line-height: 1;
}
.hlxb-horizonta-small-square .total-list .item .content .value .number-value {
  font-family: D-DIN-PRO;
  display: inline-block;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.hlxb-horizonta-small-square .total-list .item .content .value .unit {
  margin-left: 8px;
  display: inline-block;
  font-size: 14px;
  font-weight: 400;
  color: #666;
}
.hlxb-horizonta-small-square .empty-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  color: #999;
}
.hlxb-card-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hlxb-card-loading.Dark {
  color: #fff;
}
.hlxb-card-loading.light {
  color: #333;
}
.hlxb-card-loading.screenColor {
  color: #fff;
  background: transparent;
}
.hlxb-card-empty {
  color: #999;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.hlxb-card-empty .text-center {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}
.hlxb-card-empty.Dark {
  color: #fff;
}
.hlxb-card-empty.light {
  color: #333;
}
.hlxb-card-empty.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}
/* 图表类组合卡片样式 */
.hlxb-card-combination-pie-simple,
.hlxb-card-combination-pie-plus,
.hlxb-card-combination-line-simple,
.hlxb-card-combination-line-plus,
.hlxb-card-combination-bar-simple,
.hlxb-card-combination-bar-plus,
.hlxb-card-combination-radar-simple,
.hlxb-card-combination-radar-plus,
.hlxb-card-combination-summary-simple {
  width: 100%;
  height: 100%;
}
.hlxb-card-combination-pie-simple.Dark,
.hlxb-card-combination-pie-plus.Dark,
.hlxb-card-combination-line-simple.Dark,
.hlxb-card-combination-line-plus.Dark,
.hlxb-card-combination-bar-simple.Dark,
.hlxb-card-combination-bar-plus.Dark,
.hlxb-card-combination-radar-simple.Dark,
.hlxb-card-combination-radar-plus.Dark,
.hlxb-card-combination-summary-simple.Dark {
  color: #fff;
}
.hlxb-card-combination-pie-simple.light,
.hlxb-card-combination-pie-plus.light,
.hlxb-card-combination-line-simple.light,
.hlxb-card-combination-line-plus.light,
.hlxb-card-combination-bar-simple.light,
.hlxb-card-combination-bar-plus.light,
.hlxb-card-combination-radar-simple.light,
.hlxb-card-combination-radar-plus.light,
.hlxb-card-combination-summary-simple.light {
  color: #333;
}
.hlxb-card-combination-pie-simple.screenColor,
.hlxb-card-combination-pie-plus.screenColor,
.hlxb-card-combination-line-simple.screenColor,
.hlxb-card-combination-line-plus.screenColor,
.hlxb-card-combination-bar-simple.screenColor,
.hlxb-card-combination-bar-plus.screenColor,
.hlxb-card-combination-radar-simple.screenColor,
.hlxb-card-combination-radar-plus.screenColor,
.hlxb-card-combination-summary-simple.screenColor {
  color: #fff;
  background: transparent;
}

import { defineComponent, createElementBlock, createCommentVNode, openBlock, normalizeClass, unref, Fragment, renderList, createElementVNode, toDisplayString, normalizeStyle } from "vue";
import { getPrefixCls } from "../../../config/index.js";
const _hoisted_1 = { class: "item-index" };
const _hoisted_2 = {
  key: 0,
  class: "num-index"
};
const _hoisted_3 = { class: "item-content" };
const _hoisted_4 = { class: "value" };
const _hoisted_5 = { class: "unit" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "HlxbRankingSimple"
  },
  __name: "RankingSimple",
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    // 是否显示序号
    indexType: {
      type: Boolean,
      default: false
    },
    themeColor: {
      type: String,
      default: "light"
      // Dark, light, screenColor
    }
  },
  setup(__props) {
    const prefixCls = getPrefixCls("ranking-simple");
    const props = __props;
    console.log("dataList", props);
    return (_ctx, _cache) => {
      return __props.dataList.length ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass([unref(prefixCls), __props.themeColor])
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(__props.dataList, (item, index) => {
          return openBlock(), createElementBlock("div", {
            key: index,
            class: normalizeClass(["item", __props.indexType ? "item-t" : ""])
          }, [
            createElementVNode("div", _hoisted_1, [
              __props.indexType ? (openBlock(), createElementBlock("div", _hoisted_2, toDisplayString(index + 1), 1)) : createCommentVNode("", true),
              createElementVNode("div", {
                style: normalizeStyle({ paddingLeft: __props.indexType ? "8px" : 0 }),
                class: "label"
              }, toDisplayString(item.indexName), 5)
            ]),
            createElementVNode("div", _hoisted_3, [
              createElementVNode("div", _hoisted_4, toDisplayString(item.value), 1),
              createElementVNode("div", _hoisted_5, toDisplayString(item.unitName), 1)
            ])
          ], 2);
        }), 128))
      ], 2)) : createCommentVNode("", true);
    };
  }
});
export {
  _sfc_main as default
};

import { mergePath as mergePath$1, extendFromString, createFromString } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/tool/path.js";
import { identity, mul, invert, copy, create } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/matrix.js";
import { applyTransform as applyTransform$1 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/vector.js";
import Path from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Path.js";
import Transformable from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Transformable.js";
import ZRImage from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Image.js";
import { default as default2 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
import { default as default3 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
import Circle from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Circle.js";
import Ellipse from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Ellipse.js";
import Sector from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Sector.js";
import Ring from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Ring.js";
import Polygon from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Polygon.js";
import Polyline from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Polyline.js";
import Rect from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Rect.js";
import Line from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Line.js";
import BezierCurve from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/BezierCurve.js";
import Arc from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/shape/Arc.js";
import { default as default4 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/CompoundPath.js";
import { default as default5 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/LinearGradient.js";
import { default as default6 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/RadialGradient.js";
import BoundingRect from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/BoundingRect.js";
import { default as default7 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/OrientedBoundingRect.js";
import { default as default8 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/Point.js";
import { default as default9 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/IncrementalDisplayable.js";
import { subPixelOptimize as subPixelOptimize$1, subPixelOptimizeLine as subPixelOptimizeLine$1, subPixelOptimizeRect as subPixelOptimizeRect$1 } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/helper/subPixelOptimize.js";
import { isArray, extend, defaults, map, each, hasOwn, keys, isArrayLike, isNumber, assert, clone, isString } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import { getECData } from "./innerStore.js";
import { updateProps } from "../animation/basicTransition.js";
import { initProps, isElementRemoved, removeElement, removeElementWithFadeOut } from "../animation/basicTransition.js";
import { mathMax, mathMin, mathAbs } from "./number.js";
var _customShapeMap = {};
var XY = ["x", "y"];
var WH = ["width", "height"];
function extendShape(opts) {
  return Path.extend(opts);
}
var extendPathFromString = extendFromString;
function extendPath(pathData, opts) {
  return extendPathFromString(pathData, opts);
}
function registerShape(name, ShapeClass) {
  _customShapeMap[name] = ShapeClass;
}
function getShapeClass(name) {
  if (_customShapeMap.hasOwnProperty(name)) {
    return _customShapeMap[name];
  }
}
function makePath(pathData, opts, rect, layout) {
  var path = createFromString(pathData, opts);
  if (rect) {
    if (layout === "center") {
      rect = centerGraphic(rect, path.getBoundingRect());
    }
    resizePath(path, rect);
  }
  return path;
}
function makeImage(imageUrl, rect, layout) {
  var zrImg = new ZRImage({
    style: {
      image: imageUrl,
      x: rect.x,
      y: rect.y,
      width: rect.width,
      height: rect.height
    },
    onload: function(img) {
      if (layout === "center") {
        var boundingRect = {
          width: img.width,
          height: img.height
        };
        zrImg.setStyle(centerGraphic(rect, boundingRect));
      }
    }
  });
  return zrImg;
}
function centerGraphic(rect, boundingRect) {
  var aspect = boundingRect.width / boundingRect.height;
  var width = rect.height * aspect;
  var height;
  if (width <= rect.width) {
    height = rect.height;
  } else {
    width = rect.width;
    height = width / aspect;
  }
  var cx = rect.x + rect.width / 2;
  var cy = rect.y + rect.height / 2;
  return {
    x: cx - width / 2,
    y: cy - height / 2,
    width,
    height
  };
}
var mergePath = mergePath$1;
function resizePath(path, rect) {
  if (!path.applyTransform) {
    return;
  }
  var pathRect = path.getBoundingRect();
  var m = pathRect.calculateTransform(rect);
  path.applyTransform(m);
}
function subPixelOptimizeLine(shape, lineWidth) {
  subPixelOptimizeLine$1(shape, shape, {
    lineWidth
  });
  return shape;
}
function subPixelOptimizeRect(shape, style) {
  subPixelOptimizeRect$1(shape, shape, style);
  return shape;
}
var subPixelOptimize = subPixelOptimize$1;
function getTransform(target, ancestor) {
  var mat = identity([]);
  while (target && target !== ancestor) {
    mul(mat, target.getLocalTransform(), mat);
    target = target.parent;
  }
  return mat;
}
function applyTransform(target, transform, invert$1) {
  if (transform && !isArrayLike(transform)) {
    transform = Transformable.getLocalTransform(transform);
  }
  if (invert$1) {
    transform = invert([], transform);
  }
  return applyTransform$1([], target, transform);
}
function transformDirection(direction, transform, invert2) {
  var hBase = transform[4] === 0 || transform[5] === 0 || transform[0] === 0 ? 1 : mathAbs(2 * transform[4] / transform[0]);
  var vBase = transform[4] === 0 || transform[5] === 0 || transform[2] === 0 ? 1 : mathAbs(2 * transform[4] / transform[2]);
  var vertex = [direction === "left" ? -hBase : direction === "right" ? hBase : 0, direction === "top" ? -vBase : direction === "bottom" ? vBase : 0];
  vertex = applyTransform(vertex, transform, invert2);
  return mathAbs(vertex[0]) > mathAbs(vertex[1]) ? vertex[0] > 0 ? "right" : "left" : vertex[1] > 0 ? "bottom" : "top";
}
function isNotGroup(el) {
  return !el.isGroup;
}
function isPath(el) {
  return el.shape != null;
}
function groupTransition(g1, g2, animatableModel) {
  if (!g1 || !g2) {
    return;
  }
  function getElMap(g) {
    var elMap = {};
    g.traverse(function(el) {
      if (isNotGroup(el) && el.anid) {
        elMap[el.anid] = el;
      }
    });
    return elMap;
  }
  function getAnimatableProps(el) {
    var obj = {
      x: el.x,
      y: el.y,
      rotation: el.rotation
    };
    if (isPath(el)) {
      obj.shape = clone(el.shape);
    }
    return obj;
  }
  var elMap1 = getElMap(g1);
  g2.traverse(function(el) {
    if (isNotGroup(el) && el.anid) {
      var oldEl = elMap1[el.anid];
      if (oldEl) {
        var newProp = getAnimatableProps(el);
        el.attr(getAnimatableProps(oldEl));
        updateProps(el, newProp, animatableModel, getECData(el).dataIndex);
      }
    }
  });
}
function clipPointsByRect(points, rect) {
  return map(points, function(point) {
    var x = point[0];
    x = mathMax(x, rect.x);
    x = mathMin(x, rect.x + rect.width);
    var y = point[1];
    y = mathMax(y, rect.y);
    y = mathMin(y, rect.y + rect.height);
    return [x, y];
  });
}
function clipRectByRect(targetRect, rect) {
  var x = mathMax(targetRect.x, rect.x);
  var x2 = mathMin(targetRect.x + targetRect.width, rect.x + rect.width);
  var y = mathMax(targetRect.y, rect.y);
  var y2 = mathMin(targetRect.y + targetRect.height, rect.y + rect.height);
  if (x2 >= x && y2 >= y) {
    return {
      x,
      y,
      width: x2 - x,
      height: y2 - y
    };
  }
}
function createIcon(iconStr, opt, rect) {
  var innerOpts = extend({
    rectHover: true
  }, opt);
  var style = innerOpts.style = {
    strokeNoScale: true
  };
  rect = rect || {
    x: -1,
    y: -1,
    width: 2,
    height: 2
  };
  if (iconStr) {
    return iconStr.indexOf("image://") === 0 ? (style.image = iconStr.slice(8), defaults(style, rect), new ZRImage(innerOpts)) : makePath(iconStr.replace("path://", ""), innerOpts, rect, "center");
  }
}
function linePolygonIntersect(a1x, a1y, a2x, a2y, points) {
  for (var i = 0, p2 = points[points.length - 1]; i < points.length; i++) {
    var p = points[i];
    if (lineLineIntersect(a1x, a1y, a2x, a2y, p[0], p[1], p2[0], p2[1])) {
      return true;
    }
    p2 = p;
  }
}
function lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {
  var mx = a2x - a1x;
  var my = a2y - a1y;
  var nx = b2x - b1x;
  var ny = b2y - b1y;
  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);
  if (nearZero(nmCrossProduct)) {
    return false;
  }
  var b1a1x = a1x - b1x;
  var b1a1y = a1y - b1y;
  var q = crossProduct2d(b1a1x, b1a1y, mx, my) / nmCrossProduct;
  if (q < 0 || q > 1) {
    return false;
  }
  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;
  if (p < 0 || p > 1) {
    return false;
  }
  return true;
}
function crossProduct2d(x1, y1, x2, y2) {
  return x1 * y2 - x2 * y1;
}
function nearZero(val) {
  return val <= 1e-6 && val >= -1e-6;
}
function expandOrShrinkRect(rect, delta, shrinkOrExpand, noNegative, minSize) {
  if (delta == null) {
    return rect;
  } else if (isNumber(delta)) {
    _tmpExpandRectDelta[0] = _tmpExpandRectDelta[1] = _tmpExpandRectDelta[2] = _tmpExpandRectDelta[3] = delta;
  } else {
    if (process.env.NODE_ENV !== "production") {
      assert(delta.length === 4);
    }
    _tmpExpandRectDelta[0] = delta[0];
    _tmpExpandRectDelta[1] = delta[1];
    _tmpExpandRectDelta[2] = delta[2];
    _tmpExpandRectDelta[3] = delta[3];
  }
  if (noNegative) {
    _tmpExpandRectDelta[0] = mathMax(0, _tmpExpandRectDelta[0]);
    _tmpExpandRectDelta[1] = mathMax(0, _tmpExpandRectDelta[1]);
    _tmpExpandRectDelta[2] = mathMax(0, _tmpExpandRectDelta[2]);
    _tmpExpandRectDelta[3] = mathMax(0, _tmpExpandRectDelta[3]);
  }
  if (shrinkOrExpand) {
    _tmpExpandRectDelta[0] = -_tmpExpandRectDelta[0];
    _tmpExpandRectDelta[1] = -_tmpExpandRectDelta[1];
    _tmpExpandRectDelta[2] = -_tmpExpandRectDelta[2];
    _tmpExpandRectDelta[3] = -_tmpExpandRectDelta[3];
  }
  expandRectOnOneDimension(rect, _tmpExpandRectDelta, "x", "width", 3, 1, minSize && minSize[0] || 0);
  expandRectOnOneDimension(rect, _tmpExpandRectDelta, "y", "height", 0, 2, minSize && minSize[1] || 0);
  return rect;
}
var _tmpExpandRectDelta = [0, 0, 0, 0];
function expandRectOnOneDimension(rect, delta, xy, wh, ltIdx, rbIdx, minSize) {
  var deltaSum = delta[rbIdx] + delta[ltIdx];
  var oldSize = rect[wh];
  rect[wh] += deltaSum;
  minSize = mathMax(0, mathMin(minSize, oldSize));
  if (rect[wh] < minSize) {
    rect[wh] = minSize;
    rect[xy] += delta[ltIdx] >= 0 ? -delta[ltIdx] : delta[rbIdx] >= 0 ? oldSize + delta[rbIdx] : mathAbs(deltaSum) > 1e-8 ? (oldSize - minSize) * delta[ltIdx] / deltaSum : 0;
  } else {
    rect[xy] -= delta[ltIdx];
  }
}
function setTooltipConfig(opt) {
  var itemTooltipOption = opt.itemTooltipOption;
  var componentModel = opt.componentModel;
  var itemName = opt.itemName;
  var itemTooltipOptionObj = isString(itemTooltipOption) ? {
    formatter: itemTooltipOption
  } : itemTooltipOption;
  var mainType = componentModel.mainType;
  var componentIndex = componentModel.componentIndex;
  var formatterParams = {
    componentType: mainType,
    name: itemName,
    $vars: ["name"]
  };
  formatterParams[mainType + "Index"] = componentIndex;
  var formatterParamsExtra = opt.formatterParamsExtra;
  if (formatterParamsExtra) {
    each(keys(formatterParamsExtra), function(key) {
      if (!hasOwn(formatterParams, key)) {
        formatterParams[key] = formatterParamsExtra[key];
        formatterParams.$vars.push(key);
      }
    });
  }
  var ecData = getECData(opt.el);
  ecData.componentMainType = mainType;
  ecData.componentIndex = componentIndex;
  ecData.tooltipConfig = {
    name: itemName,
    option: defaults({
      content: itemName,
      encodeHTMLContent: true,
      formatterParams
    }, itemTooltipOptionObj)
  };
}
function traverseElement(el, cb) {
  var stopped;
  if (el.isGroup) {
    stopped = cb(el);
  }
  if (!stopped) {
    el.traverse(cb);
  }
}
function traverseElements(els, cb) {
  if (els) {
    if (isArray(els)) {
      for (var i = 0; i < els.length; i++) {
        traverseElement(els[i], cb);
      }
    } else {
      traverseElement(els, cb);
    }
  }
}
function isBoundingRectAxisAligned(transform) {
  return !transform || mathAbs(transform[1]) < AXIS_ALIGN_EPSILON && mathAbs(transform[2]) < AXIS_ALIGN_EPSILON || mathAbs(transform[0]) < AXIS_ALIGN_EPSILON && mathAbs(transform[3]) < AXIS_ALIGN_EPSILON;
}
var AXIS_ALIGN_EPSILON = 1e-5;
function ensureCopyRect(target, source) {
  return target ? BoundingRect.copy(target, source) : source.clone();
}
function ensureCopyTransform(target, source) {
  return source ? copy(target || create(), source) : void 0;
}
function retrieveZInfo(model) {
  return {
    z: model.get("z") || 0,
    zlevel: model.get("zlevel") || 0
  };
}
function calcZ2Range(el) {
  var max = -Infinity;
  var min = Infinity;
  traverseElement(el, function(el2) {
    visitEl(el2);
    visitEl(el2.getTextContent());
    visitEl(el2.getTextGuideLine());
  });
  function visitEl(el2) {
    if (!el2 || el2.isGroup) {
      return;
    }
    var currentStates = el2.currentStates;
    if (currentStates.length) {
      for (var idx = 0; idx < currentStates.length; idx++) {
        calcZ2(el2.states[currentStates[idx]]);
      }
    }
    calcZ2(el2);
  }
  function calcZ2(entity) {
    if (entity) {
      var z2 = entity.z2;
      if (z2 > max) {
        max = z2;
      }
      if (z2 < min) {
        min = z2;
      }
    }
  }
  if (min > max) {
    min = max = 0;
  }
  return {
    min,
    max
  };
}
function traverseUpdateZ(el, z, zlevel) {
  doUpdateZ(el, z, zlevel, -Infinity);
}
function doUpdateZ(el, z, zlevel, maxZ2) {
  if (el.ignoreModelZ) {
    return maxZ2;
  }
  var label = el.getTextContent();
  var labelLine = el.getTextGuideLine();
  var isGroup = el.isGroup;
  if (isGroup) {
    var children = el.childrenRef();
    for (var i = 0; i < children.length; i++) {
      maxZ2 = mathMax(doUpdateZ(children[i], z, zlevel, maxZ2), maxZ2);
    }
  } else {
    el.z = z;
    el.zlevel = zlevel;
    maxZ2 = mathMax(el.z2 || 0, maxZ2);
  }
  if (label) {
    label.z = z;
    label.zlevel = zlevel;
    isFinite(maxZ2) && (label.z2 = maxZ2 + 2);
  }
  if (labelLine) {
    var textGuideLineConfig = el.textGuideLineConfig;
    labelLine.z = z;
    labelLine.zlevel = zlevel;
    isFinite(maxZ2) && (labelLine.z2 = maxZ2 + (textGuideLineConfig && textGuideLineConfig.showAbove ? 1 : -1));
  }
  return maxZ2;
}
registerShape("circle", Circle);
registerShape("ellipse", Ellipse);
registerShape("sector", Sector);
registerShape("ring", Ring);
registerShape("polygon", Polygon);
registerShape("polyline", Polyline);
registerShape("rect", Rect);
registerShape("line", Line);
registerShape("bezierCurve", BezierCurve);
registerShape("arc", Arc);
export {
  Arc,
  BezierCurve,
  BoundingRect,
  Circle,
  default4 as CompoundPath,
  Ellipse,
  default2 as Group,
  ZRImage as Image,
  default9 as IncrementalDisplayable,
  Line,
  default5 as LinearGradient,
  default7 as OrientedBoundingRect,
  Path,
  default8 as Point,
  Polygon,
  Polyline,
  default6 as RadialGradient,
  Rect,
  Ring,
  Sector,
  default3 as Text,
  WH,
  XY,
  applyTransform,
  calcZ2Range,
  clipPointsByRect,
  clipRectByRect,
  createIcon,
  ensureCopyRect,
  ensureCopyTransform,
  expandOrShrinkRect,
  extendPath,
  extendShape,
  getShapeClass,
  getTransform,
  groupTransition,
  initProps,
  isBoundingRectAxisAligned,
  isElementRemoved,
  lineLineIntersect,
  linePolygonIntersect,
  makeImage,
  makePath,
  mergePath,
  registerShape,
  removeElement,
  removeElementWithFadeOut,
  resizePath,
  retrieveZInfo,
  setTooltipConfig,
  subPixelOptimize,
  subPixelOptimizeLine,
  subPixelOptimizeRect,
  transformDirection,
  traverseElements,
  traverseUpdateZ,
  updateProps
};

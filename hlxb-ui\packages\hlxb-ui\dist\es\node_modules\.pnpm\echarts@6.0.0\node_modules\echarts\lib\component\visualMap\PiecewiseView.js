import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import { retrieve, each, bind, retrieve2, map, clone } from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
import VisualMapView from "./VisualMapView.js";
import { createSymbol } from "../../util/symbol.js";
import { box } from "../../util/layout.js";
import { makeHighDownBatch, getItemAlign } from "./helper.js";
import { createTextStyle } from "../../label/labelStyle.js";
import Group from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Group.js";
import ZRText from "../../../../../../zrender@6.0.0/node_modules/zrender/lib/graphic/Text.js";
var PiecewiseVisualMapView = (
  /** @class */
  function(_super) {
    __extends(PiecewiseVisualMapView2, _super);
    function PiecewiseVisualMapView2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = PiecewiseVisualMapView2.type;
      return _this;
    }
    PiecewiseVisualMapView2.prototype.doRender = function() {
      var thisGroup = this.group;
      thisGroup.removeAll();
      var visualMapModel = this.visualMapModel;
      var textGap = visualMapModel.get("textGap");
      var textStyleModel = visualMapModel.textStyleModel;
      var itemAlign = this._getItemAlign();
      var itemSize = visualMapModel.itemSize;
      var viewData = this._getViewData();
      var endsText = viewData.endsText;
      var showLabel = retrieve(visualMapModel.get("showLabel", true), !endsText);
      var silent = !visualMapModel.get("selectedMode");
      endsText && this._renderEndsText(thisGroup, endsText[0], itemSize, showLabel, itemAlign);
      each(viewData.viewPieceList, function(item) {
        var piece = item.piece;
        var itemGroup = new Group();
        itemGroup.onclick = bind(this._onItemClick, this, piece);
        this._enableHoverLink(itemGroup, item.indexInModelPieceList);
        var representValue = visualMapModel.getRepresentValue(piece);
        this._createItemSymbol(itemGroup, representValue, [0, 0, itemSize[0], itemSize[1]], silent);
        if (showLabel) {
          var visualState = this.visualMapModel.getValueState(representValue);
          var align = textStyleModel.get("align") || itemAlign;
          itemGroup.add(new ZRText({
            style: createTextStyle(textStyleModel, {
              x: align === "right" ? -textGap : itemSize[0] + textGap,
              y: itemSize[1] / 2,
              text: piece.text,
              verticalAlign: textStyleModel.get("verticalAlign") || "middle",
              align,
              opacity: retrieve2(textStyleModel.get("opacity"), visualState === "outOfRange" ? 0.5 : 1)
            }),
            silent
          }));
        }
        thisGroup.add(itemGroup);
      }, this);
      endsText && this._renderEndsText(thisGroup, endsText[1], itemSize, showLabel, itemAlign);
      box(visualMapModel.get("orient"), thisGroup, visualMapModel.get("itemGap"));
      this.renderBackground(thisGroup);
      this.positionGroup(thisGroup);
    };
    PiecewiseVisualMapView2.prototype._enableHoverLink = function(itemGroup, pieceIndex) {
      var _this = this;
      itemGroup.on("mouseover", function() {
        return onHoverLink("highlight");
      }).on("mouseout", function() {
        return onHoverLink("downplay");
      });
      var onHoverLink = function(method) {
        var visualMapModel = _this.visualMapModel;
        visualMapModel.option.hoverLink && _this.api.dispatchAction({
          type: method,
          batch: makeHighDownBatch(visualMapModel.findTargetDataIndices(pieceIndex), visualMapModel)
        });
      };
    };
    PiecewiseVisualMapView2.prototype._getItemAlign = function() {
      var visualMapModel = this.visualMapModel;
      var modelOption = visualMapModel.option;
      if (modelOption.orient === "vertical") {
        return getItemAlign(visualMapModel, this.api, visualMapModel.itemSize);
      } else {
        var align = modelOption.align;
        if (!align || align === "auto") {
          align = "left";
        }
        return align;
      }
    };
    PiecewiseVisualMapView2.prototype._renderEndsText = function(group, text, itemSize, showLabel, itemAlign) {
      if (!text) {
        return;
      }
      var itemGroup = new Group();
      var textStyleModel = this.visualMapModel.textStyleModel;
      itemGroup.add(new ZRText({
        style: createTextStyle(textStyleModel, {
          x: showLabel ? itemAlign === "right" ? itemSize[0] : 0 : itemSize[0] / 2,
          y: itemSize[1] / 2,
          verticalAlign: "middle",
          align: showLabel ? itemAlign : "center",
          text
        })
      }));
      group.add(itemGroup);
    };
    PiecewiseVisualMapView2.prototype._getViewData = function() {
      var visualMapModel = this.visualMapModel;
      var viewPieceList = map(visualMapModel.getPieceList(), function(piece, index) {
        return {
          piece,
          indexInModelPieceList: index
        };
      });
      var endsText = visualMapModel.get("text");
      var orient = visualMapModel.get("orient");
      var inverse = visualMapModel.get("inverse");
      if (orient === "horizontal" ? inverse : !inverse) {
        viewPieceList.reverse();
      } else if (endsText) {
        endsText = endsText.slice().reverse();
      }
      return {
        viewPieceList,
        endsText
      };
    };
    PiecewiseVisualMapView2.prototype._createItemSymbol = function(group, representValue, shapeParam, silent) {
      var itemSymbol = createSymbol(
        // symbol will be string
        this.getControllerVisual(representValue, "symbol"),
        shapeParam[0],
        shapeParam[1],
        shapeParam[2],
        shapeParam[3],
        // color will be string
        this.getControllerVisual(representValue, "color")
      );
      itemSymbol.silent = silent;
      group.add(itemSymbol);
    };
    PiecewiseVisualMapView2.prototype._onItemClick = function(piece) {
      var visualMapModel = this.visualMapModel;
      var option = visualMapModel.option;
      var selectedMode = option.selectedMode;
      if (!selectedMode) {
        return;
      }
      var selected = clone(option.selected);
      var newKey = visualMapModel.getSelectedMapKey(piece);
      if (selectedMode === "single" || selectedMode === true) {
        selected[newKey] = true;
        each(selected, function(o, key) {
          selected[key] = key === newKey;
        });
      } else {
        selected[newKey] = !selected[newKey];
      }
      this.api.dispatchAction({
        type: "selectDataRange",
        from: this.uid,
        visualMapId: this.visualMapModel.id,
        selected
      });
    };
    PiecewiseVisualMapView2.type = "visualMap.piecewise";
    return PiecewiseVisualMapView2;
  }(VisualMapView)
);
export {
  PiecewiseVisualMapView as default
};

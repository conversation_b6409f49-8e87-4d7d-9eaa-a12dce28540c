import { __extends } from "../../../../../../tslib@2.3.0/node_modules/tslib/tslib.es6.js";
import Path from "../Path.js";
import { buildPath } from "../helper/poly.js";
var PolygonShape = /* @__PURE__ */ function() {
  function PolygonShape2() {
    this.points = null;
    this.smooth = 0;
    this.smoothConstraint = null;
  }
  return PolygonShape2;
}();
var Polygon = function(_super) {
  __extends(Polygon2, _super);
  function Polygon2(opts) {
    return _super.call(this, opts) || this;
  }
  Polygon2.prototype.getDefaultShape = function() {
    return new PolygonShape();
  };
  Polygon2.prototype.buildPath = function(ctx, shape) {
    buildPath(ctx, shape, true);
  };
  return Polygon2;
}(Path);
Polygon.prototype.type = "polygon";
export {
  PolygonShape,
  Polygon as default
};

import dayjs from "../../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/esm/index.js";
function generateFullMonthDates(currentDate, fillPrevNextMonth = true) {
  const year = currentDate.year();
  const month = currentDate.month();
  const today = dayjs().startOf("day");
  const firstDay = dayjs().year(year).month(month).date(1);
  const lastDay = dayjs().year(year).month(month).endOf("month");
  let startDate;
  let endDate;
  if (fillPrevNextMonth) {
    startDate = firstDay.startOf("week");
    endDate = lastDay.endOf("week");
  } else {
    startDate = firstDay;
    endDate = lastDay;
  }
  const dates = [];
  let tempDate = dayjs(startDate);
  while (tempDate.isBefore(endDate) || tempDate.isSame(endDate)) {
    const dateString = tempDate.format("YYYY-MM-DD");
    const isCurrentMonth = tempDate.month() === month;
    const isToday = tempDate.isSame(today, "day");
    dates.push({
      date: dayjs(tempDate),
      isCurrentMonth,
      isToday,
      dateString
    });
    tempDate = tempDate.add(1, "day");
  }
  return dates;
}
export {
  generateFullMonthDates
};

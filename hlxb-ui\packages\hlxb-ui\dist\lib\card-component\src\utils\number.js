"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const pureFunctionsAny_generated = require("../../../node_modules/.pnpm/mathjs@14.6.0/node_modules/mathjs/lib/esm/entry/pureFunctionsAny.generated.js");
function roundAndConvert(num, decimalPlaces, abs, automatic, level) {
  if (typeof num !== "number" || isNaN(num)) {
    throw new TypeError("参数 num 必须是有效的数字");
  }
  if (typeof decimalPlaces !== "number" || !Number.isInteger(decimalPlaces) || decimalPlaces < 0 || decimalPlaces > 20) {
    throw new RangeError("参数 decimalPlaces 必须是非负整数，且不超过 20");
  }
  {
    const factor2 = Math.pow(10, decimalPlaces);
    const result2 = Number(pureFunctionsAny_generated.multiply(pureFunctionsAny_generated.bignumber(num), pureFunctionsAny_generated.bignumber(factor2)));
    const roundedValue2 = Math.round(result2) / factor2;
    return roundedValue2.toFixed(decimalPlaces);
  }
}
exports.roundAndConvert = roundAndConvert;

import { getPrecision, nice, round, quantityExponent } from "../util/number.js";
import { bind } from "../../../../../zrender@6.0.0/node_modules/zrender/lib/core/util.js";
function isValueNice(val) {
  var exp10 = Math.pow(10, quantityExponent(Math.abs(val)));
  var f = Math.abs(val / exp10);
  return f === 0 || f === 1 || f === 2 || f === 3 || f === 5;
}
function isIntervalOrLogScale(scale2) {
  return scale2.type === "interval" || scale2.type === "log";
}
function intervalScaleNiceTicks(extent, spanWithBreaks, splitNumber, minInterval, maxInterval) {
  var result = {};
  var interval = result.interval = nice(spanWithBreaks / splitNumber, true);
  if (minInterval != null && interval < minInterval) {
    interval = result.interval = minInterval;
  }
  if (maxInterval != null && interval > maxInterval) {
    interval = result.interval = maxInterval;
  }
  var precision = result.intervalPrecision = getIntervalPrecision(interval);
  var niceTickExtent = result.niceTickExtent = [round(Math.ceil(extent[0] / interval) * interval, precision), round(Math.floor(extent[1] / interval) * interval, precision)];
  fixExtent(niceTickExtent, extent);
  return result;
}
function increaseInterval(interval) {
  var exp10 = Math.pow(10, quantityExponent(interval));
  var f = interval / exp10;
  if (!f) {
    f = 1;
  } else if (f === 2) {
    f = 3;
  } else if (f === 3) {
    f = 5;
  } else {
    f *= 2;
  }
  return round(f * exp10);
}
function getIntervalPrecision(interval) {
  return getPrecision(interval) + 2;
}
function clamp(niceTickExtent, idx, extent) {
  niceTickExtent[idx] = Math.max(Math.min(niceTickExtent[idx], extent[1]), extent[0]);
}
function fixExtent(niceTickExtent, extent) {
  !isFinite(niceTickExtent[0]) && (niceTickExtent[0] = extent[0]);
  !isFinite(niceTickExtent[1]) && (niceTickExtent[1] = extent[1]);
  clamp(niceTickExtent, 0, extent);
  clamp(niceTickExtent, 1, extent);
  if (niceTickExtent[0] > niceTickExtent[1]) {
    niceTickExtent[0] = niceTickExtent[1];
  }
}
function contain(val, extent) {
  return val >= extent[0] && val <= extent[1];
}
var ScaleCalculator = (
  /** @class */
  function() {
    function ScaleCalculator2() {
      this.normalize = normalize;
      this.scale = scale;
    }
    ScaleCalculator2.prototype.updateMethods = function(brkCtx) {
      if (brkCtx.hasBreaks()) {
        this.normalize = bind(brkCtx.normalize, brkCtx);
        this.scale = bind(brkCtx.scale, brkCtx);
      } else {
        this.normalize = normalize;
        this.scale = scale;
      }
    };
    return ScaleCalculator2;
  }()
);
function normalize(val, extent) {
  if (extent[1] === extent[0]) {
    return 0.5;
  }
  return (val - extent[0]) / (extent[1] - extent[0]);
}
function scale(val, extent) {
  return val * (extent[1] - extent[0]) + extent[0];
}
function logTransform(base, extent, noClampNegative) {
  var loggedBase = Math.log(base);
  return [
    // log(negative) is NaN, so safe guard here.
    // PENDING: But even getting a -Infinity still does not make sense in extent.
    //  Just keep it as is, getting a NaN to make some previous cases works by coincidence.
    Math.log(noClampNegative ? extent[0] : Math.max(0, extent[0])) / loggedBase,
    Math.log(noClampNegative ? extent[1] : Math.max(0, extent[1])) / loggedBase
  ];
}
export {
  ScaleCalculator,
  contain,
  fixExtent,
  getIntervalPrecision,
  increaseInterval,
  intervalScaleNiceTicks,
  isIntervalOrLogScale,
  isValueNice,
  logTransform
};

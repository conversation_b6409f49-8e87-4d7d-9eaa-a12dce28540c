import { defineComponent, createElementBlock, openBlock, normalizeClass, unref, Fragment, renderList, createElementVNode, toDisplayString } from "vue";
import { weekdaysHeaderPrefixCls } from "../hooks/prefixCls.js";
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...{
    name: "WeekdaysHeader"
  },
  __name: "WeekdaysHeader",
  setup(__props) {
    const weekdays = ["日", "一", "二", "三", "四", "五", "六"];
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        class: normalizeClass(unref(weekdaysHeaderPrefixCls))
      }, [
        (openBlock(), createElementBlock(Fragment, null, renderList(weekdays, (day) => {
          return createElementVNode("div", {
            class: "weekday",
            key: day
          }, toDisplayString(day), 1);
        }), 64))
      ], 2);
    };
  }
});
export {
  _sfc_main as default
};
